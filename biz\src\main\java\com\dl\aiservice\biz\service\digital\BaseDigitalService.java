package com.dl.aiservice.biz.service.digital;

import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateTrainingRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.RobotDetailRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TrainRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateTrainingResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotDetailResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.TrainResponseDTO;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.model.ResultPageModel;

import java.util.List;

/**
 * @author: xuebin
 * @description 智能数字人RPC接口
 * @Date: 2023/2/28 17:50
 */
public interface BaseDigitalService {

    /**
     * 获取厂商类型列表
     */
    List<ServiceChannelEnum> getEnums();

    /**
     * 创建合成视频任务
     *
     * @param gjVideoCreateRequestDTO 请求参数
     * @return 视频作品ID
     */
    CreateResponseDTO videoCreate(CreateRequestDTO gjVideoCreateRequestDTO);

    /**
     * 定制模特列表查询
     *
     * @param pageRequestDTO 分页条件
     * @return 返回结果
     */
    ResultPageModel<RobotResponseDTO> robotPageList(PageRequestDTO pageRequestDTO);

    /**
     * 模特详情接口
     * @param requestDTO 具体参数
     * @return 返回结果
     */
    RobotDetailResponseDTO robotDetail(RobotDetailRequestDTO requestDTO);

    /**
     * 训练任务信息查询
     *
     * @param gjTrainRequestDTO 提交的训练ID(路径参数)
     * @return 返回结果
     */
    TrainResponseDTO getTrain(TrainRequestDTO gjTrainRequestDTO);

    /**
     * 合成任务信息查询
     *
     * @param taskRequestDTO 提交的合成任务ID(路径参数)
     * @return 返回结果
     */
    ProgressResponseDTO getProgress(TaskRequestDTO taskRequestDTO);

    /**
     * 训练视频提交
     *
     * @param request 请求参数
     * @return 训练任务ID
     */
    CreateTrainingResponseDTO createTraining(CreateTrainingRequestDTO request);

    /**
     * 回调业务侧
     *
     * @param tenantCode
     * @param worksBizId
     * @param callBackUrl
     * @param digitalCallbackDTO
     */
    void callBackBiz(String tenantCode, Long worksBizId, String callBackUrl, DigitalVideoCallbackDTO digitalCallbackDTO,
            String extCallbackRespBody);
}

