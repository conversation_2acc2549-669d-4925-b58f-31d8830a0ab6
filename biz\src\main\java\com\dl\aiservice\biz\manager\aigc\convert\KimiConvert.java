package com.dl.aiservice.biz.manager.aigc.convert;

import com.dl.aiservice.biz.client.kimi.req.ChatCompletionRequest;
import com.dl.aiservice.biz.client.kimi.resp.ChatCompletionMessage;
import com.dl.aiservice.biz.common.constant.Const;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-26 16:08
 */
public class KimiConvert {

    public static ChatCompletionRequest buildChatCompletionRequest(List<ChatCompletionMessage> messages, String model,
            Integer respMaxToken) {
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setN(Const.ONE);
        request.setStream(Boolean.FALSE);
        request.setMessages(messages);
        request.setModel(model);
        request.setMaxTokens(respMaxToken);
        return request;
    }
}
