package com.dl.aiservice.biz.digitaljobhandler;

import com.dl.aiservice.share.enums.ServiceChannelEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 腾讯云数字人任务处理器
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-11 16:36
 */
@Component
public class IvhDigitalManJobHandler extends AbstractHaveConcurrencyLimitDigitalManJobHandler {

    /**
     * 任务队列的key
     */
    private static final String IVH_DM_JOB_QUEUE_KEY = "Ivh_DM_Job_Queue";

    /**
     * 分布式锁的key
     */
    private static final String IVH_DM_JOB_HANDLER_LOCK_KEY = "Ivh_Dm_Job_Handler_Lock";

    /**
     * 分布式锁的失效时间 单位：秒
     */
    private static final long IVH_DM_JOB_HANDLER_LOCK_TIMEOUT = 4;

    @Value("${digtal.ivh.video.concurrency}")
    private Integer ivhConcurrency;

    @Override
    public ServiceChannelEnum supportChannel() {
        return ServiceChannelEnum.IVH;
    }

    @Override
    public String getQueueKey() {
        return IVH_DM_JOB_QUEUE_KEY;
    }

    @Override
    String getHandlerLockKey() {
        return IVH_DM_JOB_HANDLER_LOCK_KEY;
    }

    @Override
    long getHandlerLockTimeout() {
        return IVH_DM_JOB_HANDLER_LOCK_TIMEOUT;
    }

    @Override
    int getConcurrency() {
        return ivhConcurrency;
    }

}
