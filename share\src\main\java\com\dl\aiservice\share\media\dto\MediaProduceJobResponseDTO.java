package com.dl.aiservice.share.media.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @describe: MediaProduceJobResponseDTO
 * @author: zhousx
 * @date: 2023/3/29 17:48
 */
@Data
public class MediaProduceJobResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 媒体id 雪花算法
     */
    private Long mediaJobId;

    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    private Integer channel;

    /**
     * 合成类型：0-视频合成 1-数字人 2-TTS音频
     */
    private Integer jobType;

    /**
     * 任务状态：1 合成中；0 合成完成；-1 合成失败
     */
    private Integer status;

    /**
     * 合成后url
     */
    private String mediaUrl;

    /**
     * 封面图url
     */
    private String coverUrl;

    /**
     * 时长(单位：秒，如11.32)
     */
    private Double duration;

    /**
     * 作品名称
     */
    private String mediaName;

    /**
     * 上层业务作品唯一标识
     */
    private Long worksBizId;

    /**
     * 合成费用
     */
    private Long cost;
}
