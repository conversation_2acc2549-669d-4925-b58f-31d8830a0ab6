package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class IvhVideoRequest implements Serializable {


    private static final long serialVersionUID = -1301231693693196145L;
    /**
     * 视频输出格式，默认TransparentWebmTransparentWebm:透明背景webm格式视频GreenScreenMp4:绿幕mp4格式视频
     */
    @JsonProperty(value = "Format")
    private String format;

}
