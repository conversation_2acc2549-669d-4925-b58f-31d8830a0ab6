package com.dl.aiservice.share.digitalasset;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName DaVirtualVoiceRequestDTO
 * @Description
 * <AUTHOR>
 * @Date 2023/6/5 10:28
 * @Version 1.0
 **/
@Data
public class DaVirtualVoiceRequestDTO implements Serializable {

    private static final long serialVersionUID = -5679966422110293071L;

    @Deprecated
    @ApiModelProperty(value = "当指定租户返回数据空时，是否返回DL租户下的数据")
    private boolean dlShow;

    @ApiModelProperty(value = "声音类型：1 克隆音；2 合成音")
    private Integer voiceType;

    private Long voiceBizId;

    private String voiceKey;

    @ApiModelProperty(value = "渠道：4-阿里云；6-火山引擎")
    private List<Integer> channels;
}
