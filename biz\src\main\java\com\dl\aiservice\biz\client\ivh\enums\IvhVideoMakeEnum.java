package com.dl.aiservice.biz.client.ivh.enums;

import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;

public enum IvhVideoMakeEnum {

    /**
     * 驱动类型，默认Text1. Text：⽂本驱动，要求InputSsml字
     * 段必填2. OriginalVoice：原声⾳频驱动，要求
     * InputAudioUrl字段必填3. ModulatedVoice：变声⾳频驱
     * 动，可通过Speech.TimbreKey指定⾳⾊，未填写时使⽤主
     * 播默认⾳⾊
     */
    TEXT(1, "Text"),
    ORIGINAL_VOICE(2, "OriginalVoice"),
    MODULATED_VOICE(3, "ModulatedVoice");


    private final Integer code;
    private final String desc;

    IvhVideoMakeEnum(Integer errorCode, String errorDesc) {
        this.code = errorCode;
        this.desc = errorDesc;
    }

    public static IvhVideoMakeEnum getEnum(String desc) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getDesc(), desc)).findFirst().orElseThrow(() -> BusinessServiceException.getInstance("枚举异常"));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
