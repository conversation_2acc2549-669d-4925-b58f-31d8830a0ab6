package com.dl.aiservice.biz.common.enums;

/**
 * @describe: ProduceJobStatusE
 * @author: zhousx
 * @date: 2023/2/11 17:05
 */
public enum ProduceJobStatusE {
    INIT(0, "未开始"),
    PROCESSING(1, "合成中"),
    SUCCESS(2, "合成成功"),
    FAILED(3, "合成失败"),;

    private Integer code;

    private String desc;

    ProduceJobStatusE(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }
}
