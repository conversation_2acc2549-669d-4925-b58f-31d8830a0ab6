package com.dl.aiservice.biz.client.deepsound.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DsTtsRequest implements Serializable {

    private static final long serialVersionUID = -8450722761284975366L;

    @JsonProperty("serverConfig")
    private DsTtsServiceConfig serverConfig;

    @JsonProperty("audioConfig")
    private DsTtsAudioConfig audioConfig;

    @JsonProperty("voice")
    private DsTtsVoice voice;

    @JsonProperty("input")
    private DsTtsInput input;

}
