package com.dl.aiservice.biz.client.volcengine.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-28 14:48
 */
public enum VolcEngineTtsErrorCodeEnum {
    
    CODE_3000(3000, "请求正确"),
    CODE_3001(3001, "无效的请求"),
    CODE_3003(3003, "并发超限"),
    CODE_3005(3005, "后端服务忙"),
    CODE_3006(3006, "服务中断"),
    CODE_3010(3010, "文本长度超限"),
    CODE_3011(3011, "无效文本"),
    CODE_3030(3030, "处理超时"),
    CODE_3031(3031, "处理错误"),
    CODE_3032(3032, "等待获取音频超时"),
    CODE_3040(3040, "音色克隆链路网络异常"),
    CODE_3050(3050, "音色克隆音色查询失败"),
    ;

    private Integer code;

    private String desc;

    VolcEngineTtsErrorCodeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static VolcEngineTtsErrorCodeEnum parse(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (VolcEngineTtsErrorCodeEnum errorCodeEnum : VolcEngineTtsErrorCodeEnum.values()) {
            if (errorCodeEnum.getCode().equals(code)) {
                return errorCodeEnum;
            }
        }
        return null;
    }
}
