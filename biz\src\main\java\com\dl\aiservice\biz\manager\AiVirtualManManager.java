package com.dl.aiservice.biz.manager;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.digitaljobhandler.AbstractDigitalManJobHandler;
import com.dl.aiservice.biz.digitaljobhandler.DigitalManJobHandlerFactory;
import com.dl.aiservice.biz.service.digital.DigitalDBService;
import com.dl.aiservice.biz.service.digital.bo.CreateVideoBO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @ClassName AiVirtualManManager
 * @Description
 * <AUTHOR>
 * @Date 2023/6/28 19:40
 * @Version 1.0
 **/
@Component
public class AiVirtualManManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(AiVirtualManManager.class);

    @Resource
    private DigitalDBService digitalDBService;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private DigitalManJobHandlerFactory digitalManJobHandlerFactory;

    /**
     * 三方数字人供应商视频合成
     *
     * @param createRequest
     * @return
     */
    public CreateResponseDTO videoCreate(String tenantCode, CreateRequestDTO createRequest) {
        channelUtil.init(tenantCode, createRequest.getChannel());
        ServiceChannelEnum serviceChannelEnum = ServiceChannelEnum.getByCode(createRequest.getChannel());
        if (Objects.isNull(serviceChannelEnum)) {
            throw BusinessServiceException.getInstance("不支持该数字人厂商");
        }

        //保存数字人任务
        CreateVideoBO createVideoBO = new CreateVideoBO();
        createVideoBO.setVideoName(createRequest.getVideoName());
        createVideoBO.setCallbackUrl(createRequest.getCallbackUrl());
        createVideoBO.setWorksBizId(createRequest.getWorksBizId());
        createVideoBO.setVideoTaskJobId(createRequest.getVideoTaskJobId());
        MediaProduceJobPO mediaProduceJobPO = digitalDBService.saveVideoCreate(createVideoBO);

        //回填数据库主键id
        createRequest.setUpdateId(mediaProduceJobPO.getId());

        //获取数字人任务处理器，将数字人合成请求放入队列
        AbstractDigitalManJobHandler dmJobHandler = digitalManJobHandlerFactory.getJobHandler(serviceChannelEnum);
        dmJobHandler.pushToRedis(createRequest);
        LOGGER.info("已将该数字人合成请求放入redis队列中。mediaJobId:{},,,createRequest:{}", mediaProduceJobPO.getMediaJobId(),
                JSONUtil.toJsonStr(createRequest));

        //构建返回对象并返回
        CreateResponseDTO responseDTO = new CreateResponseDTO();
        responseDTO.setMediaJobId(mediaProduceJobPO.getMediaJobId());
        responseDTO.setWorksBizId(createRequest.getWorksBizId());
        return responseDTO;
    }

}
