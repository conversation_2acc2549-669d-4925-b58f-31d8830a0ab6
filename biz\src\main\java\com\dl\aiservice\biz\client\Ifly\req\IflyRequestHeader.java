package com.dl.aiservice.biz.client.Ifly.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class IflyRequestHeader {

    /**
     * 在平台申请的appid信息
     */
    @JsonProperty(value = "app_id")
    private String appId;

    /**
     * 本次会话的id
     */
    private String sid;

    private long timestamp;

    private String sign;
}

