package com.dl.aiservice.web.controller.digital;

import com.dl.aiservice.biz.client.ivh.IvhDigitalClient;
import com.dl.aiservice.biz.client.ivh.req.IvhBaseRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetSsmlTimeRequest;
import com.dl.aiservice.biz.client.ivh.resp.IvhBaseResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhSentences;
import com.dl.aiservice.biz.client.ivh.resp.IvhSsmlTimeResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhWords;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.manager.AiVirtualManManager;
import com.dl.aiservice.biz.register.DigitalHelper;
import com.dl.aiservice.biz.service.digital.DigitalDBService;
import com.dl.aiservice.biz.service.digital.bo.CreateTrainBO;
import com.dl.aiservice.biz.service.digital.bo.CreateTrainUpdateBO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateTrainingRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.RobotDetailRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TrainRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateTrainingResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotDetailResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.TrainResponseDTO;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.digitalman.DmSubtitleDTO;
import com.dl.aiservice.share.digitalman.DmSubtitleRequestDTO;
import com.dl.aiservice.share.digitalman.DmSubtitleResponseDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description : 数字人相关接口
 * @date :2022-08-19 13:52:25
 */
@Slf4j
@RestController
@RequestMapping("/digital")
@Api(tags = "数字人相关接口")
public class DigitalController {

    @Resource
    private DigitalHelper digitalHelper;
    @Resource
    private DigitalDBService digitalDBService;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private IvhDigitalClient ivhDigitalClient;
    @Resource
    private AiVirtualManManager aiVirtualManManager;

    @PostMapping("/videoCreate")
    @ApiOperation("音频或文本合成视频")
    public ResultModel<CreateResponseDTO> videoCreate(@RequestBody @Validated CreateRequestDTO createRequest) {
        createRequest.setChannel(channelUtil.getChannel());
        return ResultModel.success(aiVirtualManManager.videoCreate(channelUtil.getTenantCode(), createRequest));
    }

    @GetMapping("/getProgress")
    @ApiOperation("音频或文本合成视频进度查询")
    public ResultModel<ProgressResponseDTO> getProgress(@RequestBody @Validated TaskRequestDTO taskRequestDTO) {
        if (StringUtils.isBlank(taskRequestDTO.getTaskId())) {
            MediaProduceJobPO mediaProduceJobPO = digitalDBService.getWorksBizId(taskRequestDTO);
            Assert.notNull(mediaProduceJobPO, "未找到对应业务id的合成任务");
            taskRequestDTO.setTaskId(mediaProduceJobPO.getExtJobId());
            taskRequestDTO.setMediaJobId(mediaProduceJobPO.getMediaJobId());
        }
        ProgressResponseDTO progress =
                digitalHelper.get(ServiceChannelEnum.getByCode(channelUtil.getChannel())).getProgress(taskRequestDTO);
        progress.setWorksBizId(taskRequestDTO.getWorksBizId());
        return ResultModel.success(progress);
    }

    /**
     * 后续可以废弃，新增了 数字人结构化 存储数据表
     *
     * @param pageRequestDTO
     * @return
     */
    @PostMapping("/robotPageList")
    @ApiOperation("数字人列表查询")
    @Deprecated
    public ResultPageModel<RobotResponseDTO> robotPageList(@RequestBody PageRequestDTO pageRequestDTO) {
        return digitalHelper.get(ServiceChannelEnum.getByCode(channelUtil.getChannel())).robotPageList(pageRequestDTO);
    }

    @PostMapping("/robotDetail")
    @ApiOperation("数字人详情查询")
    public ResultModel<RobotDetailResponseDTO> robotDetail(@RequestBody RobotDetailRequestDTO requestDTO) {
        return ResultModel.success(
                digitalHelper.get(ServiceChannelEnum.getByCode(channelUtil.getChannel())).robotDetail(requestDTO));
    }

    @GetMapping("/getTrain")
    @ApiOperation("训练详情查询")
    public ResultModel<TrainResponseDTO> getTrain(@RequestBody TrainRequestDTO TrainRequestDTO) {
        return ResultModel.success(
                digitalHelper.get(ServiceChannelEnum.getByCode(channelUtil.getChannel())).getTrain(TrainRequestDTO));
    }

    @PostMapping("/createTraining")
    @ApiOperation("创建训练")
    public ResultModel<CreateTrainingResponseDTO> createTraining(
            @RequestBody CreateTrainingRequestDTO VideoRequestDTO) {
        CreateTrainBO createVideoBO = new CreateTrainBO();
        createVideoBO.setCallbackUrl(VideoRequestDTO.getCallbackUrl());
        TrainJobPO trainJobPO = digitalDBService.saveTrainCreate(createVideoBO);
        VideoRequestDTO.setUpdateId(trainJobPO.getId());

        CreateTrainingResponseDTO training = digitalHelper.get(ServiceChannelEnum.getByCode(channelUtil.getChannel()))
                .createTraining(VideoRequestDTO);

        if (Objects.nonNull(training)) {
            CreateTrainUpdateBO createVideoUpdateBO = new CreateTrainUpdateBO();
            createVideoUpdateBO.setId(trainJobPO.getId());
            createVideoUpdateBO.setTaskId(training.getTaskId());
            digitalDBService.updateTrainById(createVideoUpdateBO);
        }
        return ResultModel.success(training);
    }

    @Deprecated
    @PostMapping("/gensubtitle")
    @ApiOperation("获取数字人字幕")
    public ResultModel<DmSubtitleResponseDTO> generateSubtitle(
            @RequestBody @Validated DmSubtitleRequestDTO requestDTO) {
        Assert.isTrue(Objects.equals(channelUtil.getChannel(), ServiceChannelEnum.IVH.getCode()), "数字人字幕功能暂时只支持腾讯云渠道");
        IvhGetSsmlTimeRequest request = new IvhGetSsmlTimeRequest();
        request.setInputSsml(requestDTO.getText());
        request.setVirtualmanKey(requestDTO.getSceneId());
        request.setSpeed(Objects.isNull(requestDTO.getSpeed()) ? 1.0 : requestDTO.getSpeed());
        IvhBaseResponse<IvhSsmlTimeResponse> resp = ivhDigitalClient
                .getSsmlTime(IvhBaseRequest.<IvhGetSsmlTimeRequest>builder().payload(request).build());
        DmSubtitleResponseDTO responseDTO = new DmSubtitleResponseDTO();
        if (resp.isSuccess()) {
            List<DmSubtitleDTO> subtitles = new ArrayList<>();
            responseDTO.setSubtitles(subtitles);
            for (IvhSentences sentence : resp.getPayload().getSentences()) {
                long lastEndTime = sentence.getWords().get(0).getStartTimestamp() / 10000;
                long beginTime = sentence.getWords().get(0).getStartTimestamp() / 10000;
                DmSubtitleDTO dmSubtitleDTO = new DmSubtitleDTO();
                StringBuilder sb = new StringBuilder();
                for (IvhWords word : sentence.getWords()) {
                    long wordBeginTime = word.getStartTimestamp() / 10000;
                    long wordEndTime = word.getEndTimestamp() / 10000;
                    if (Objects.isNull(requestDTO.getMaxLength()) || sb.length() < requestDTO.getMaxLength()) {
                        sb.append(word.getWord());
                    } else {
                        dmSubtitleDTO.setText(trimSymbol(sb.toString()));
                        dmSubtitleDTO.setBeginTime(beginTime);
                        dmSubtitleDTO.setEndTime(lastEndTime);
                        subtitles.add(dmSubtitleDTO);

                        dmSubtitleDTO = new DmSubtitleDTO();
                        sb = new StringBuilder();
                        sb.append(word.getWord());
                        beginTime = wordBeginTime;
                    }
                    lastEndTime = wordEndTime;
                }
                if (sb.length() > 0) {
                    dmSubtitleDTO.setText(trimSymbol(sb.toString()));
                    dmSubtitleDTO.setBeginTime(beginTime);
                    dmSubtitleDTO.setEndTime(lastEndTime);
                    subtitles.add(dmSubtitleDTO);
                }
            }
        }
        return ResultModel.success(responseDTO);
    }

    private String trimSymbol(String orignal) {
        if (StringUtils.isBlank(orignal)) {
            return orignal;
        }
        return orignal.replaceAll("【", "")
                .replaceAll("】", "")
                .replaceAll("。", "")
                .replaceAll(",", "")
                .replaceAll("，", "")
                .replaceAll("!", "")
                .replaceAll("！", "")
                .replaceAll("、", "")
                .replaceAll("[?]", "")
                .replaceAll("？", "");
    }
}
