package com.dl.aiservice.biz.manager.subtitle.aliyun;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.aliyun.AliyunAsrClient;
import com.dl.aiservice.biz.client.aliyun.resp.AliyunFlashAsrResp;
import com.dl.aiservice.biz.client.aliyun.resp.AliyunFlashAsrReult;
import com.dl.aiservice.biz.common.util.RedisUtil;
import com.dl.aiservice.biz.manager.subtitle.aliyun.convert.AliyunAsrConvert;
import com.dl.aiservice.biz.manager.subtitle.aliyun.enums.AliyunAsrFormatEnum;
import com.dl.aiservice.biz.manager.subtitle.utils.AsrUtil;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleDTO;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-31 13:42
 */
@Component
public class AliyunAsrManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(AliyunAsrManager.class);

    @Resource
    private AliyunAsrClient aliyunAsrClient;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private AliyunDslConfig aliyunDslConfig;

    public String getToken() {
        return redisUtil.get(AliyunDslConst.ACCESS_TOKEN_REDIS_KEY);
    }

    /**
     * 极速asr
     *
     * @param audioUrl
     * @return
     */
    public List<AsrSubtitleDTO> flashAsr(String audioUrl, Integer sentenceMaxLength) {
        if (Objects.nonNull(sentenceMaxLength)) {
            Assert.isTrue(sentenceMaxLength >= 4 && sentenceMaxLength <= 50, "每句最多展示字数，取值范围：[4，50]");
        }

        String token = this.getToken();
        Assert.isTrue(StringUtils.isNotBlank(token), "阿里云智能合成服务token为空");
        String suffix = AsrUtil.extractAudioSuffix(audioUrl);
        AliyunAsrFormatEnum aliyunAsrFormatEnum = AliyunAsrFormatEnum.parse(suffix);
        Assert.notNull(aliyunAsrFormatEnum, "不支持该音频格式");

        AliyunFlashAsrResp resp = aliyunAsrClient
                .flashRecognizer(aliyunDslConfig.getAppKey(), token, aliyunAsrFormatEnum.getFormat(), audioUrl,
                        sentenceMaxLength);

        if (!AliyunDslConst.SUCCESS.equals(resp.getStatus())) {
            LOGGER.error("调用阿里云极速ASR接口失败, resp:{}", JSONUtil.toJsonStr(resp));
            throw BusinessServiceException.getInstance("调用阿里云极速ASR接口失败");
        }
        AliyunFlashAsrReult aliyunFlashAsrReult = resp.getFlashResult();
        return AliyunAsrConvert.buildAsrSubtitleDTOList(aliyunFlashAsrReult);
    }

    /**
     * 极速asr
     *
     * @param multipartFile
     * @return
     */
    public List<AsrSubtitleDTO> flashAsrFile(MultipartFile multipartFile, Integer sentenceMaxLength) {
        String token = this.getToken();
        Assert.isTrue(StringUtils.isNotBlank(token), "阿里云智能合成服务token为空");
        if (Objects.nonNull(sentenceMaxLength)) {
            Assert.isTrue(sentenceMaxLength >= 4 && sentenceMaxLength <= 50, "每句最多展示字数，取值范围：[4，50]");
        }

        String suffix = AsrUtil.extractAudioSuffix(multipartFile.getOriginalFilename());
        AliyunAsrFormatEnum aliyunAsrFormatEnum = AliyunAsrFormatEnum.parse(suffix);
        Assert.notNull(aliyunAsrFormatEnum, "不支持该音频格式");

        AliyunFlashAsrResp resp = null;
        try {
            resp = aliyunAsrClient
                    .flashRecognizerFile(aliyunDslConfig.getAppKey(), token, aliyunAsrFormatEnum.getFormat(), true,
                            true, sentenceMaxLength, true, multipartFile.getBytes());
        } catch (Exception e) {
            LOGGER.error("调用阿里云极速ASR接口发生异常, e:{}", e);
            throw BusinessServiceException.getInstance("调用阿里云极速ASR接口失败");
        }
        if (Objects.isNull(resp) || !AliyunDslConst.SUCCESS.equals(resp.getStatus())) {
            LOGGER.error("调用阿里云极速ASR接口失败, resp:{}", JSONUtil.toJsonStr(resp));
            throw BusinessServiceException.getInstance("调用阿里云极速ASR接口失败");
        }
        AliyunFlashAsrReult aliyunFlashAsrReult = resp.getFlashResult();
        return AliyunAsrConvert.buildAsrSubtitleDTOList(aliyunFlashAsrReult);
    }

}
