package com.dl.aiservice.biz.manager.voiceclone;

import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.model.ResultModel;

import java.util.List;

/**
 * @ClassName VoiceCloneManager
 * @Description 克隆音接口
 * <AUTHOR>
 * @Date 2023/3/8 15:41
 * @Version 1.0
 **/
public interface VoiceCloneHandlerManager {

    List<ServiceChannelEnum> getEnums();

    /**
     * 环境音检测h
     * 在开始录制前，录制一段 3 秒以上的 环境静音的音频，以检测录制环境是 否符合可录制的标准，只有环境音检 测通过，才可进入文本录制环节。
     *
     * @param url 音频文件存放 url 链接,url 长度不超过 1KB
     * @return
     */
    ResultModel envCheck(String url);

    /**
     * 音质检测接口
     * 检测采集的数据是否 符合声音克隆模型训练要求。
     *
     * @param url  音频文件存放 url 链接,url 长度不超过 1KB
     * @param text 音频内容的对应文本，不超过 35 个中文字符
     * @param language 待检音频的语种；目前支持 zh（中 文）和 en（英文），默认为 zh
     * @return
     */
    ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language);

    /**
     * 模型训练
     * 提交采集录音数据，训练声音模型。
     *
     * @param request
     * @return
     */
    ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request);

    /**
     * 模型查询
     * 查询已提交训练模型 的状态。
     *
     * @param recordId
     * @return
     */
    ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId);

    /**
     * 语音合成
     * 将待合成的文本上传到服务端，服务端返回文本的语音合成结果，开发者需要保证在语音合成结果返回之前连接不中断
     *
     * @param request
     * @return
     */
    ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO request);
}
