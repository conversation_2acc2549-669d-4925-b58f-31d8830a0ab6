package com.dl.aiservice.biz.client.Ifly.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class IflyTtsCreateRequest {

    /**
     * 发音人，例如：xiaopei。如果是音频制作试听，传该值，anchorId和vcn只传一个
     */
    private String vcn;
    /**
     * 语速，取值范围：0-100 默认50
     */
    private Integer spd;
    /**
     * 音量，取值范围：0-100 默认100
     */
    private Integer vol;
    /**
     * 音高，取值范围：0-100 默认50
     */
    private Integer pitch;
    /**
     * 音频格式  mp3  wav。默认mp3
     */
    private String format;
    /**
     * 输入文本，utf8编码  小于等于500字；p标签、动作标签、情感标签不计算在内
     */
    private String text;
    /**
     * 音频输出形式 0：音频地址  1:二进制流 默认0
     */
    private Integer audioType;
    /**
     * 采样率 16  24 48
     */
    private Integer rate;
    /**
     * 多语种，1表示多语种
     */
    private Integer lanType;
    /**
     * 【英文】/【中文】，多语种支持列表见附件4.2，只支持传一种多语种；具体使用见上面案例
     */
    private String language;
    /**
     * 0 无字幕 1 有字幕 /多语种不支持字幕
     */
    private Integer subtitles;
    /**
     * 字幕限制大小
     */
    private Integer limit;
    /**
     * 制作视频的宽，根据宽高限制字幕大小；200-1000，必须是4的倍数
     */
    private Integer width;
    /**
     * 制作视频的高，200-1000，必须是4的倍数
     */
    private Integer Height;
}
