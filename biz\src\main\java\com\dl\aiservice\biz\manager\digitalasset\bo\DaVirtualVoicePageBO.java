package com.dl.aiservice.biz.manager.digitalasset.bo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.aiservice.biz.dal.po.DaVirtualVoicePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DaVirtualVoicePageBO extends Page<DaVirtualVoicePO> {

    @ApiModelProperty(value = "声音名称")
    private String voiceName;

    @ApiModelProperty(value = "来源")
    private Integer channel;

    @ApiModelProperty(value = "是否启用")
    private Integer isEnabled;
}
