package com.dl.aiservice.share.voiceclone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName DsAudioTrainSource
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 18:17
 * @Version 1.0
 **/
@Data
public class AudioTrainSourceDTO implements Serializable {

    private static final long serialVersionUID = -4213730735414879102L;

    @ApiModelProperty("录音文本，需不多于35个中文字")
    private String text;

    @ApiModelProperty("录音音频链接")
    private String link;

}
