package com.dl.aiservice.biz.service.digital.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class RobotResponseDTO implements Serializable {

    private static final long serialVersionUID = 6660662421497170627L;
    /**
     * 硅基：模特ID/腾讯云：AnchorCode主播code
     */
    @ApiModelProperty(value = "硅基：模特ID/腾讯云：AnchorCode主播code")
    private String id;

    /**
     * 硅基：模特封面地址/腾讯云：HeaderImage数智⼈头像图⽚url
     */
    @ApiModelProperty(value = "硅基：模特封面地址/腾讯云：HeaderImage数智⼈头像图⽚url")
    private String coverUrl;

    /**
     * 硅基：模特名称/腾讯云：AnchorName主播名称
     */
    @ApiModelProperty(value = "硅基：模特名称/腾讯云：AnchorName主播名称")
    private String robotName;


    //**************************************硅基参数参数**********************************************

    /**
     * 模特描述
     */
    @ApiModelProperty(value = "硅基：模特描述")
    private String robotDesc;
    /**
     * 性别1男 2女
     */
    @ApiModelProperty(value = "硅基：性别1男 2女")
    private Integer gender;
    /**
     * 类型0:2D 1:3D
     */
    @ApiModelProperty(value = "硅基：类型0:2D 1:3D")
    private Integer version;


    /**
     * 模特类型 1-私人定制 2-会员订阅 3或空-其他
     */
    @ApiModelProperty(value = "硅基：模特类型 1-私人定制 2-会员订阅 3或空-其他")
    private Integer type;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "硅基：年龄")
    private Integer age;
    /**
     * 星座
     * 1:'白羊座', 2:'金牛座', 3:'双子座', 4:'巨蟹座', 5:'狮子座', 6:'处女座',
     * 7:'天秤座', 8:'天蝎座', 9:'射手座', 10:'摩羯座', 11:'水瓶座', 12:'双鱼座'
     */
    @ApiModelProperty(value = "硅基：星座")
    private Integer starSigns;

    /**
     * 数字人场景列表
     */
    @ApiModelProperty(value = "硅基：数字人场景列表")
    public List<SceneDTO> sceneList;


    //**************************************腾讯云参数参数**********************************************


    /**
     * 数智⼈类型
     */
    @ApiModelProperty(value = "腾讯：数智⼈类型z")
    private String virtualmanType;
    /**
     * 数智⼈类型code
     */
    @ApiModelProperty(value = "腾讯：数智⼈类型code")
    private String virtualmanTypeCode;
    /**
     * 数智⼈模型tag，分三类：BasicStandardAdvanced⽬前tag仅影响了主播横纵向位置功能，⻅接⼝4.3。
     */
    @ApiModelProperty(value = "腾讯：数智⼈模型tag")
    private String tag;

}

