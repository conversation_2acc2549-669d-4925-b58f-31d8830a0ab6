package com.dl.aiservice.share.voiceclone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName DsAudioCheckResponse
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 15:47
 * @Version 1.0
 **/
@Data
public class AudioTrainDetailResponseDTO implements Serializable {

    private static final long serialVersionUID = -255119982193376160L;

    @ApiModelProperty("训练阶段，取值说明如下：INQUEUE-排队中；TRAINING-训练中；FINISHED-任务结束")
    private String stage;

    @ApiModelProperty("训练状态，取值说明如下：0-成功；1-任务处理中（表示stage处于“INQUEUE”和“TRAINING”两个阶段时的状态);-1-失败")
    private Integer status;

    @ApiModelProperty("服务内部对当前提交音色的唯一编号")
    private String trainJobId;

    @ApiModelProperty("音色编号，仅当status=0时，该字段不会空")
    private String voiceName;

    @ApiModelProperty("预计排队剩余时长，单位：秒；当stage=INQUEUE时，该值有意义；stage为非INQUEUE状态时，该值为0")
    private Integer estimatedStartTime;

    @ApiModelProperty("预计训练剩余时长，单位：秒；当stage=TRAINNING时，该值有意义，stage为非TRAINNING状态时该值为0; 需注意预计训练剩余时长中不包含排队时间")
    private Integer estimatedFinishTime;

}
