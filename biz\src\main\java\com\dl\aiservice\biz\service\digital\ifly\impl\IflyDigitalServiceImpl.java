package com.dl.aiservice.biz.service.digital.ifly.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.client.Ifly.IflyDigitalClient;
import com.dl.aiservice.biz.client.Ifly.req.IflyBaseRequest;
import com.dl.aiservice.biz.client.Ifly.req.IflyRequestHeader;
import com.dl.aiservice.biz.client.Ifly.req.IflyVideoCreateRequest;
import com.dl.aiservice.biz.client.Ifly.req.IflyVideoQueryRequest;
import com.dl.aiservice.biz.client.Ifly.resp.IflyBaseResponse;
import com.dl.aiservice.biz.client.Ifly.resp.IflyVideoCreateResponse;
import com.dl.aiservice.biz.client.Ifly.resp.IflyVideoQueryResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.service.digital.AbstractDigitalService;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateTrainingRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.RobotDetailRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TrainRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateTrainingResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotDetailResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.TrainResponseDTO;
import com.dl.aiservice.biz.service.digital.enums.SynthesisStatusEnum;
import com.dl.aiservice.biz.service.digital.ifly.IflyDigitalService;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
public class IflyDigitalServiceImpl extends AbstractDigitalService implements IflyDigitalService {

    @Resource
    private CosFileUploadManager cosFileUploadManager;
    @Resource
    private IflyDigitalClient iflyDigitalClient;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private HostTimeIdg hostTimeIdg;

    @Resource
    private AiConfig aiConfig;

    private static final String CALL_BACK_URL = "%s/ifly/callback";

    @Override
    public CreateResponseDTO videoCreate(CreateRequestDTO requestDTO) {
        log.info("科大讯飞视频合成请求参数：{}", JSONUtil.toJsonStr(requestDTO));
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setId(requestDTO.getUpdateId());
        CreateResponseDTO createResponseDTO = new CreateResponseDTO();
        IflyBaseResponse<IflyVideoCreateResponse> resp;
        try {
            IflyBaseRequest<IflyVideoCreateRequest> request = buildCreateRequest(requestDTO);
            job.setJobContent(JSONUtil.toJsonStr(request));
            resp = iflyDigitalClient.videoCreate(request);
            if (!resp.isSuccess()) {
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(-Const.ONE);
                // 失败原因
                job.setFailCode(resp.getCode());
                job.setFailReason(resp.getMsg());
                job.setExtJobId(StringUtils.isNotBlank(resp.getData().getTaskId()) ? resp.getData().getTaskId() : "");
                mediaProduceJobManager.updateById(job);
                log.info("科大讯飞语音合成失败,param={},response={}", JSONUtil.toJsonStr(requestDTO), JSONUtil.toJsonStr(resp));
                throw BusinessServiceException.getInstance("科大讯飞数字人合成失败");
            } else {
                job.setExtJobId(StringUtils.isNotBlank(resp.getData().getTaskId()) ? resp.getData().getTaskId() : "");
                mediaProduceJobManager.updateById(job);
            }
        } catch (Exception e) {
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            job.setStatus(-Const.ONE);
            // 失败原因
            job.setFailReason(e.getMessage());
            mediaProduceJobManager.updateById(job);
            log.info("科大讯飞数字人合成异常", e);
            throw BusinessServiceException.getInstance("科大讯飞数字人合成失败");
        }
        return createResponseDTO;

    }


    private IflyBaseRequest<IflyVideoCreateRequest> buildCreateRequest(CreateRequestDTO requestDTO) {
        String sid = hostTimeIdg.generateId().toString();
        IflyVideoCreateRequest createRequest = IflyVideoCreateRequest.builder()
                .anchorId(Integer.valueOf(requestDTO.getSceneId()))
                .type(1)
                .format(StringUtils.isNotBlank(requestDTO.getVideoFormat()) ? requestDTO.getVideoFormat() : "webm")
                .width(StringUtils.isNotBlank(requestDTO.getWidth()) ? requestDTO.getWidth() : "1080")
                .height(StringUtils.isNotBlank(requestDTO.getHeight()) ? requestDTO.getHeight() : "1920")
//                .callbackUrl("https://244273l11a.goho.co/ifly/callback")
                .callbackUrl(String.format(CALL_BACK_URL, getCallbackPrefix()))
                .build();

        if (requestDTO.getType().equals(Const.ONE)) {
            createRequest.setText(requestDTO.getText());
            createRequest.setVol(StringUtils.isNotEmpty(requestDTO.getVolume()) ? Integer.valueOf(requestDTO.getVolume()) : null);
            createRequest.setSpd(StringUtils.isNotEmpty(requestDTO.getSpeechRate()) ? Integer.valueOf(requestDTO.getSpeechRate()) : null);
            createRequest.setVcn(requestDTO.getSpeakerId());
        } else if (requestDTO.getType().equals(Const.ZERO)) {
            //必须要传值 否则报错
            createRequest.setText("1");
            createRequest.setAudioUrl(requestDTO.getAudioUrl());
        } else {
            throw BusinessServiceException.getInstance("暂不支持的类型");
        }
        return IflyBaseRequest.<IflyVideoCreateRequest>builder()
                .base(IflyRequestHeader.builder()
                        .sid(sid)
                        .build())
                .param(createRequest)
                .build();
    }

    @Override
    public ResultPageModel<RobotResponseDTO> robotPageList(PageRequestDTO pageRequestDTO) {
        return null;
    }

    @Override
    public RobotDetailResponseDTO robotDetail(RobotDetailRequestDTO requestDTO) {
        return null;
    }

    @Override
    public TrainResponseDTO getTrain(TrainRequestDTO trainRequestDTO) {
        return null;
    }

    @Override
    public ProgressResponseDTO getProgress(TaskRequestDTO taskRequestDTO) {
        MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.getOne(Wrappers.<MediaProduceJobPO>lambdaQuery()
//                .eq(MediaProduceJobPO::getStatus, Const.ZERO)
                .eq(MediaProduceJobPO::getExtJobId, taskRequestDTO.getTaskId())
                .eq(MediaProduceJobPO::getWorksBizId, taskRequestDTO.getWorksBizId()));

        if (Objects.nonNull(mediaProduceJobPO) && Objects.equals(mediaProduceJobPO.getStatus(), Const.ZERO)) {
            ProgressResponseDTO progressResponseDTO = new ProgressResponseDTO();
            progressResponseDTO.setWorksBizId(taskRequestDTO.getWorksBizId());
            progressResponseDTO.setSynthesisStatus(mediaProduceJobPO.getStatus());
            progressResponseDTO.setVideoUrl(mediaProduceJobPO.getMediaUrl());
            progressResponseDTO.setDuration(mediaProduceJobPO.getDuration().toString());
            return progressResponseDTO;
        }
        IflyBaseRequest<IflyVideoQueryRequest> build = IflyBaseRequest.<IflyVideoQueryRequest>builder()
                .base(IflyRequestHeader.builder()
                        .sid(hostTimeIdg.generateId().toString())
                        .build())
                .param(IflyVideoQueryRequest.builder()
                        .taskId(taskRequestDTO.getTaskId())
                        .build())
                .build();
        IflyBaseResponse<IflyVideoQueryResponse> response = iflyDigitalClient.videoQuery(build);
        ProgressResponseDTO progressResponseDTO = new ProgressResponseDTO();

        if (response.isSuccess() && response.getData().getStatus().equals(Const.ONE)) {
            if (Objects.nonNull(response.getData().getUrl())) {
                String url = buildFileUrl(response.getData());
                progressResponseDTO.setVideoUrl(url);
                progressResponseDTO.setSynthesisStatus(SynthesisStatusEnum.SUCCESS.getCode());
            }
            progressResponseDTO.setSynthesisStatus(SynthesisStatusEnum.FAIL.getCode());
        } else {
            progressResponseDTO.setSynthesisStatus(SynthesisStatusEnum.MAKING.getCode());
        }
        return progressResponseDTO;
    }

    @Override
    public CreateTrainingResponseDTO createTraining(CreateTrainingRequestDTO request) {
        return null;
    }

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.IFLY_TEK);
    }

    @Override
    public String buildFileUrl(IflyVideoQueryResponse responseData) {
        String video = responseData.getUrl();
        String taskId = responseData.getTaskId();

        byte[] file = HttpUtil.createGet(video).execute().bodyBytes();
        String filePath = taskId + "." + video.substring(video.lastIndexOf('.') + 1);
        File audioFile;
        String url;
        try {
            audioFile = new File(filePath);
            FileUtils.writeByteArrayToFile(new File(filePath), file, false);
            url = cosFileUploadManager.uploadFile(audioFile, null, "/temp/visual/dm");
            FileUtil.del(audioFile.getAbsolutePath());
            MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.getOne(Wrappers.<MediaProduceJobPO>lambdaQuery()
                    .eq(MediaProduceJobPO::getExtJobId, taskId));
            mediaProduceJobPO.setMediaUrl(url);
            mediaProduceJobPO.setStatus(Const.ZERO);
            mediaProduceJobPO.setDuration(convertToSeconds(responseData.getDuration()));
            mediaProduceJobPO.setResponseDt(new Date());
            mediaProduceJobManager.updateById(mediaProduceJobPO);
        } catch (IOException e) {
            log.error("科大讯飞数字人文件写入失败", e);
            throw BusinessServiceException.getInstance("科大讯飞数字人文件写入失败");
        }
        return url;
    }

    private String getCallbackPrefix() {
        String callback = aiConfig.getCallbackPrefix();
        if (StringUtils.lastIndexOf(callback, Const.SLASH) == (callback.length() - Const.ONE)) {
            return StringUtils.substring(callback, Const.ZERO, callback.length() - Const.ONE);
        }
        return callback;
    }


    public static double convertToSeconds(String time) {
        String[] parts = time.split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);
        double seconds = Double.parseDouble(parts[2]);
        return (hours * 3600) + (minutes * 60) + seconds;
    }

}

