package com.dl.aiservice.biz.client.guiji.enums;

import java.util.Objects;

public enum GjErrCodeEnum {

    ERROR_CODE_0(0,"正常"),
    ERROR_CODE_40001(40001,"内部异常"),
    ERROR_CODE_40002(40002,"token无效"),
    ERROR_CODE_40003(40003,"token超时"),
    ERROR_CODE_40010(40010,"余额不足"),
    ERROR_CODE_40011(40011,"不合法的音频地址"),
    ERROR_CODE_40012(40012,"不合法的音频时长"),
    ERROR_CODE_40013(40013,"不合法的文件大小"),
    ERROR_CODE_40014(40014,"缺少必要文件"),
    ERROR_CODE_40015(40015,"缺少必要参数"),
    ERROR_CODE_40016(40016,"文件上传失败"),
    ERROR_CODE_40017(40017,"文件下载失败"),
    ERROR_CODE_40018(40018,"文件不存在"),
    ERROR_CODE_40019(40019,"超出并发"),
    ERROR_CODE_40020(40020,"不支持的背景图片格式"),
    ERROR_CODE_40021(40021,"提交训练视频失败"),
    ERROR_CODE_40022(40022,"不合法的码率值"),
    ERROR_CODE_40023(40023,"不合法的分辨率值"),
    ERROR_CODE_40024(40024,"不合法的fps值"),
    ERROR_CODE_40025(40025,"模特不存在"),
    ERROR_CODE_40026(40026,"模特已过期"),
    ERROR_CODE_40027(40027,"不合法的视频格式"),
    UNKNOWN(9999999, "未知异常");

    private final Integer errorCode;
    private final String errorDesc;

    GjErrCodeEnum(Integer errorCode, String errorDesc) {
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }

    public static String getErrorDesc(Integer errorCode) {
        for (GjErrCodeEnum wxErrorCodeEnum : values()) {
            if (Objects.equals(wxErrorCodeEnum.errorCode, errorCode)) {
                return wxErrorCodeEnum.errorDesc;
            }
        }
        return null;
    }

    public static GjErrCodeEnum errorCode(Integer code) {
        for (GjErrCodeEnum value : values()) {
            if (Objects.nonNull(code)) {
                if (value.getErrorCode().equals(code)) {
                    return value;
                }
            }
        }
        return UNKNOWN;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorDesc() {
        return errorDesc;
    }
}
