package com.dl.aiservice.share.aichat;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 单条对话 请求
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 17:03
 */
@Data
public class AiSingleChatRequestDTO implements Serializable {
    private static final long serialVersionUID = -1L;

    @NotNull(message = "userId不能为空")
    private Long userId;
    /**
     * 用户消息
     */
    @NotBlank(message = "用户消息不能为空")
    private String userMessage;
    /**
     * 文件url
     * 非必填
     * 若有值，则会将文件下载到服务器，然后上传到外部ai厂商服务以做解析
     */
    private String fileUrl;

    /**
     * ai模型
     * 支持上层业务指定
     * 非必填
     */
    private String model;

    /**
     * 响应的最大token数
     * 支持上层业务指定
     * 非必填
     */
    private Integer respMaxToken;

}
