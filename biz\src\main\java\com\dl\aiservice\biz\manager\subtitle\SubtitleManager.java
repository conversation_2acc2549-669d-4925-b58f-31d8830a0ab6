package com.dl.aiservice.biz.manager.subtitle;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dl.aiservice.biz.client.ivh.resp.IvhSentences;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.TextUtil;
import com.dl.aiservice.biz.manager.subtitle.aliyun.AliyunAsrManager;
import com.dl.aiservice.biz.manager.subtitle.aliyun.convert.AliyunAsrConvert;
import com.dl.aiservice.biz.manager.subtitle.consts.SubtitleConst;
import com.dl.aiservice.biz.manager.subtitle.dto.LineBreakTimesDTO;
import com.dl.aiservice.biz.manager.subtitle.tencent.TencentAsrManager;
import com.dl.aiservice.biz.manager.voiceclone.volcengine.VolcEngineHandlerManager;
import com.dl.aiservice.biz.service.digital.ivh.impl.IvhDigitalServiceImpl;
import com.dl.aiservice.share.digitalman.ivh.SentencesDTO;
import com.dl.aiservice.share.digitalman.ivh.WordsDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.subtitle.dto.*;
import com.dl.aiservice.share.voiceclone.TtsSubtitleDTO;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import difflib.Delta;
import difflib.DiffUtils;
import difflib.Patch;
import org.ansj.domain.Result;
import org.ansj.domain.Term;
import org.ansj.splitWord.analysis.DicAnalysis;
import org.ansj.splitWord.analysis.NlpAnalysis;
import org.ansj.splitWord.analysis.ToAnalysis;
import org.apache.commons.lang3.StringUtils;
import org.nlpcn.commons.lang.tire.domain.Forest;
import org.nlpcn.commons.lang.tire.library.Library;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SubtitleManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(SubtitleManager.class);

    private static final char[] PUNCTUATION_SET = "。，,、".toCharArray();
    //private static final char[] PUNCTUATION_SET = ";:。，；：？?!！".toCharArray();

    private static final String SEPARATOR = "\n";

    @Resource
    private AliyunAsrManager aliyunAsrManager;

    @Resource
    private TencentAsrManager tencentAsrManager;

    public RevisedAsrResponseDTO asrRevise(ReviseAsrRequestDTO requestDTO) {
        RevisedAsrResponseDTO responseDTO = new RevisedAsrResponseDTO();
        String originalScript = formatSubtitle(requestDTO.getOriginalScript());
        // 字幕列表排序，确保按时间排序
        Collections.sort(requestDTO.getSubtitles(), Comparator.comparing(AsrSubtitleDTO::getTimePointStart));
        String asrScript = StringUtils
                .join(requestDTO.getSubtitles().stream().map(AsrSubtitleDTO::getSubtitle).collect(Collectors.toList()),
                        SEPARATOR);
        List<String> originStringList = new ArrayList<>();
        for (char c : originalScript.toCharArray()) {
            originStringList.add(String.valueOf(c));
        }
        List<String> asrStringList = new ArrayList<>();
        for (char c : asrScript.toCharArray()) {
            asrStringList.add(String.valueOf(c));
        }
        Patch<String> patch = DiffUtils.diff(originStringList, asrStringList);
        Map<Integer, LineBreakTimesDTO> insertMap = Maps.newHashMap();
        Map<Integer, LineBreakTimesDTO> changeMap = Maps.newHashMap();
        for (Delta<String> delta : patch.getDeltas()) {
            int position = delta.getOriginal().getPosition();
            List<String> revisedLines = delta.getRevised().getLines();
            if (delta.getType() == Delta.TYPE.INSERT) {
                for (String insertStr : delta.getRevised().getLines()) {
                    if (SEPARATOR.equals(insertStr)) {
                        if (insertMap.containsKey(position)) {
                            LineBreakTimesDTO lineBreakTimes = insertMap.get(position);
                            lineBreakTimes.setTimes(lineBreakTimes.getTimes() + Const.ONE);
                        } else {
                            insertMap.put(position, new LineBreakTimesDTO(position, Const.ONE));
                        }
                    }
                }
            }
            if (delta.getType() == Delta.TYPE.CHANGE) {
                // 正向循环遍历，处理首部 |n 换行符
                int breakLineCnt = getBreakLineCnt(revisedLines);
                positionMark(changeMap, breakLineCnt, position);
                revisedLines = revisedLines.subList(breakLineCnt, revisedLines.size());
                // 负向循环遍历，处理尾部 |n 换行符
                breakLineCnt = getBreakLineCnt(Lists.reverse(revisedLines));
                positionMark(changeMap, breakLineCnt, position + delta.getOriginal().getLines().size());
                revisedLines = revisedLines.subList(0, revisedLines.size() - breakLineCnt);
                // 处理剩下的字符数组，什么offset出现 \n 就在 position+offset 的位子插入 \n 即可
                int offset = 0;
                for (String changedStr : revisedLines) {
                    if (SEPARATOR.equals(changedStr)) {
                        int p = position;
                        if (offset > delta.getOriginal().getLines().size()) {
                            // 当 offset 超出原始文本长度时，在末尾加
                            p = p + delta.getOriginal().getLines().size();
                        } else {
                            p = p + offset;
                        }
                        if (changeMap.containsKey(p)) {
                            LineBreakTimesDTO lineBreakTimes = changeMap.get(p);
                            lineBreakTimes.setTimes(lineBreakTimes.getTimes() + 1);
                        } else {
                            changeMap.put(p, new LineBreakTimesDTO(p, 1));
                        }
                    }
                    offset++;
                }
            }
        }
        List<RevisedAsrSubtitleDTO> revisedAsrSubtitles = new ArrayList<>();
        StringBuilder revisedStr = new StringBuilder();
        for (int i = 0; i < originStringList.size(); i++) {
            if (insertMap.containsKey(i)) {
                LineBreakTimesDTO lineBreakTimes = insertMap.get(i);
                for (int y = 0; y < lineBreakTimes.getTimes(); y++) {
                    // 有特殊情况需要连续插入多个 \n 这种情况需要标记出来
                    revisedStr.append(SEPARATOR);
                }
            }
            if (changeMap.containsKey(i)) {
                LineBreakTimesDTO lineBreakTimes = changeMap.get(i);
                for (int y = 0; y < lineBreakTimes.getTimes(); y++) {
                    // 有特殊情况需要连续插入多个 \n 这种情况需要标记出来
                    revisedStr.append(SEPARATOR);
                }
            }
            revisedStr.append(originStringList.get(i));
        }
        // 矫正字幕重新分段
        int asrScriptIndex = 0;
        for (String revisedSubtitle : revisedStr.toString().split(SEPARATOR)) {
            AsrSubtitleDTO asrSubtitleDTO = requestDTO.getSubtitles().get(asrScriptIndex++);
            revisedAsrSubtitles.add(RevisedAsrSubtitleDTO.builder()
                    .timePointStart(asrSubtitleDTO.getTimePointStart())
                    .timePointEnd(asrSubtitleDTO.getTimePointEnd())
                    .revisedSubtitle(formatSubtitle(revisedSubtitle))
                    .uniqId(asrSubtitleDTO.getUniqId())
                    .build());
        }
        responseDTO.setRevisedAsrSubtitles(revisedAsrSubtitles);
        return responseDTO;
    }

    private void positionMark(Map<Integer, LineBreakTimesDTO> changeMap, int breakLineCnt, int position) {
        if (breakLineCnt <= 0) {
            return;
        }
        int flag = breakLineCnt;
        while (flag > 0) {
            if (changeMap.containsKey(position)) {
                LineBreakTimesDTO lineBreakTimes = changeMap.get(position);
                lineBreakTimes.setTimes(lineBreakTimes.getTimes() + 1);
            } else {
                changeMap.put(position, new LineBreakTimesDTO(position, 1));
            }
            flag--;
        }
    }

    private int getBreakLineCnt(List<String> revisedLines) {
        if (CollectionUtils.isEmpty(revisedLines)) {
            return 0;
        }
        int breakLineIdx = 0;
        for (String str : revisedLines) {
            if (SEPARATOR.equals(str)) {
                breakLineIdx++;
            } else {
                break;
            }
        }
        return breakLineIdx;
    }

    public List<AsrSubtitleDTO> asr(AsrSubtitleRequestDTO requestDTO) {
        return aliyunAsrManager.flashAsr(requestDTO.getAudioUrl(), null);
    }

    public List<AsrSubtitleDTO> asrFile(MultipartFile multipartFile, Integer sentenceMaxLength, Integer channel) {
        if (ServiceChannelEnum.IVH.getCode().equals(channel) || ServiceChannelEnum.TENCENT_CLOUD.getCode()
                .equals(channel)) {
            return tencentAsrManager.flashAsrFile(multipartFile, sentenceMaxLength);
        }

        if (ServiceChannelEnum.ALIYUN.getCode().equals(channel)) {
            return aliyunAsrManager.flashAsrFile(multipartFile, sentenceMaxLength);
        }

        throw BusinessServiceException.getInstance("暂不支持该渠道的ASR");
    }

    public RevisedAsrResponseDTO asrAndRevise(AsrSubtitleAndReviseRequestDTO requestDTO) {
        Integer channel = requestDTO.getChannel();

        //1.语音识别-音频链接
        List<AsrSubtitleDTO> asrSubtitleDTOS = null;
        if (ServiceChannelEnum.IVH.getCode().equals(channel) || ServiceChannelEnum.TENCENT_CLOUD.getCode()
                .equals(channel)) {
            asrSubtitleDTOS = tencentAsrManager.flashAsr(requestDTO.getAudioUrl(), requestDTO.getSentenceMaxLength());
        } else if (ServiceChannelEnum.ALIYUN.getCode().equals(channel)) {
            asrSubtitleDTOS = aliyunAsrManager.flashAsr(requestDTO.getAudioUrl(), requestDTO.getSentenceMaxLength());
        } else {
            throw BusinessServiceException.getInstance("暂不支持该渠道的asr");
        }

        //2.字幕校验
        ReviseAsrRequestDTO reviseAsrRequestDTO = AliyunAsrConvert
                .buildReviseAsrRequestDTO(requestDTO.getOriginalScript(), asrSubtitleDTOS);
        return this.asrRevise(reviseAsrRequestDTO);
    }

    public RevisedAsrResponseDTO asrFileAndRevise(MultipartFile multipartFile, Integer sentenceMaxLength,
            Integer channel, String originalScript) {
        //1.语音识别-音频文件
        List<AsrSubtitleDTO> asrSubtitleDTOS = null;
        if (ServiceChannelEnum.IVH.getCode().equals(channel) || ServiceChannelEnum.TENCENT_CLOUD.getCode()
                .equals(channel)) {
            asrSubtitleDTOS = tencentAsrManager.flashAsrFile(multipartFile, sentenceMaxLength);
        } else if (ServiceChannelEnum.ALIYUN.getCode().equals(channel)) {
            asrSubtitleDTOS = aliyunAsrManager.flashAsrFile(multipartFile, sentenceMaxLength);
        } else {
            throw BusinessServiceException.getInstance("暂不支持该渠道的asr");
        }

        //2.字幕校验
        ReviseAsrRequestDTO reviseAsrRequestDTO = AliyunAsrConvert
                .buildReviseAsrRequestDTO(originalScript, asrSubtitleDTOS);
        return this.asrRevise(reviseAsrRequestDTO);
    }

    private String formatSubtitle(String subtitle) {
        return StrUtil.removeAll(StrUtil.removeAllLineBreaks(subtitle), PUNCTUATION_SET);
    }

    /**
     * 获取分词后 词组集合
     *
     * @param sequence
     * @return
     */
    public List<String> segmentor(String sequence) {
        Result result = ToAnalysis.parse(sequence);
        //拿到terms
        List<Term> terms = result.getTerms();
        return terms.stream().map(param -> param.getName()).collect(Collectors.toList());
    }

    /**
     * 获取分词后 词组集合
     *
     * @param sequence
     * @return
     */
    public List<String> nlpSegmentor(String sequence) {
        Result result = NlpAnalysis.parse(sequence);
        //拿到terms
        List<Term> terms = result.getTerms();
        return terms.stream().map(param -> param.getName()).collect(Collectors.toList());
    }

    /**
     * 获取分词后 词组集合
     *
     * @param sequence
     * @return
     */
    public List<String> dicSegmentor(String sequence) {
        Result result = DicAnalysis.parse(sequence);
        //拿到terms
        List<Term> terms = result.getTerms();
        return terms.stream().map(param -> param.getName()).collect(Collectors.toList());
    }

    /**
     * 通过分词器生成字幕
     * 屎山代码，不要妄想看得懂，用下面的main方法debug吧
     *
     * @param originalText
     * @param maxLength
     */
    public List<TtsSubtitleDTO> genSubtitlesBySegmentor(String originalText, Integer maxLength,
            List<SentencesDTO> ttsResultSentnecesList) {
        if (CollectionUtils.isEmpty(ttsResultSentnecesList)) {
            LOGGER.warn("通过分词器生成字幕，入参ttsResultSentnecesList为空！");
            return Collections.emptyList();
        }
        LOGGER.info("开始通过分词器生成字幕!,originalText:{},,,maxLength:{},,,,,,,ttsResultSentnecesList:{}", originalText,
                maxLength, JSONUtil.toJsonStr(ttsResultSentnecesList));
        float maxLengthFloat = maxLength.floatValue();

        try {
            //移除html标签（移除ssml标签）、空格、\n、\r 得到纯Tts文本
            String pureTtsText = TextUtil.rmHtml(originalText).replaceAll("\n", "").replaceAll("\r", "");

            //进行分词
            List<String> segmentResults = this.segmentor(pureTtsText);
            //去除空格
            segmentResults = segmentResults.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            //处理英文缩写
            segmentResults = mergeEnglishAbbreviations(segmentResults);
            LOGGER.info("处理英文缩写后的分词结果: segmentResults:{}", JSONUtil.toJsonStr(segmentResults));
            //去除户换行符和标点符号
            segmentResults = segmentResults.stream()
                    .map(str -> StrUtil.removeAll(StrUtil.removeAllLineBreaks(str), SubtitleConst.ALL_PUNCTUATION_SET))
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            LOGGER.info("分词结果: segmentResults:{}", JSONUtil.toJsonStr(segmentResults));

            //存放用来成为字幕句子的拼接词串
            StringBuilder concatWordsToBeSubtitleSentence = new StringBuilder();
            float concatWordsToBeSubtitleSentenceLength = 0.0f;
            //对应字幕句子的开始时间和结束时间
            Long sentenceBeginTime = 0L;
            Long sentenceEndTime = 0L;

            //存放用来匹配分词结果的移除标点后的拼接词串
            String concatWordsToMatchSegmentWords = "";
            float concatWordsToMatchSegmentWordsLength = 0.0f;
            //存放用来匹配分词结果的完整的拼接词串
            String concatWholeWordsToMatchSegmentWords = "";
            Long segmentBeginTime = 0L;

            //进行分词的索引
            int segmentResultsIndex = 0;

            List<TtsSubtitleDTO> resultList = new ArrayList<>();
            for (int sentenceIndex = 0; sentenceIndex < ttsResultSentnecesList.size(); sentenceIndex++) {
                SentencesDTO sentencesDTO = ttsResultSentnecesList.get(sentenceIndex);
                //分词匹配完成 标志位
                boolean segmentMatchFinished = false;
                for (int i = 0; i < sentencesDTO.getWords().size(); i++) {
                    WordsDTO currentWordDTO = sentencesDTO.getWords().get(i);
                    String originalCurrentWord = currentWordDTO.getWord();
                    //从分词结果中取对应的分词
                    String segmentWords = segmentResults.get(segmentResultsIndex);

                    if (concatWordsToBeSubtitleSentenceLength == 0.0f && StringUtils
                            .isBlank(concatWordsToMatchSegmentWords)) {
                        sentenceBeginTime = currentWordDTO.getStartTime();
                    }
                    if (StringUtils.isBlank(concatWordsToMatchSegmentWords)) {
                        segmentBeginTime = currentWordDTO.getStartTime();
                    }
                    //移除空格
                    String rmBlankCurrentWord = currentWordDTO.getWord().replaceAll(" ", "");
                    //移除标点符号
                    String rmPuncCurrentWord = rmBlankCurrentWord.replaceAll(SubtitleConst.PUNCTUATION_REGEX, "");

                    //英文字母2个算1个
                    boolean isEnglishWord = rmPuncCurrentWord.matches(SubtitleConst.ENGLISH_CHAR);
                    float currentWordLength = !isEnglishWord ?
                            (float) rmPuncCurrentWord.length() :
                            new BigDecimal(rmPuncCurrentWord.length())
                                    .divide(new BigDecimal(2), 1, RoundingMode.HALF_UP).floatValue();

                    //判断分段词 和 已拼接词+当前词 是否相等
                    //若不等
                    if (!StringUtils
                            .equalsIgnoreCase(segmentWords, concatWordsToMatchSegmentWords + rmPuncCurrentWord)) {
                        //若已经是最后一句的最后一个词，但是segmentResultsIndex却不是最后一个。 并且还没匹配成功，则说明匹配的有问题。
                        if (sentenceIndex == ttsResultSentnecesList.size() - 1
                                && i == sentencesDTO.getWords().size() - 1
                                && segmentResultsIndex != segmentResults.size() - 1) {
                            LOGGER.error("通过分词器生成字幕。已经是最后一句的最后一个词，但是segmentResultsIndex却不是最后一个。 并且还没匹配成功，匹配的有问题!");
                            throw BusinessServiceException.getInstance("通过分词器生成字幕，字幕匹配异常");
                        }

                        //用来成为字幕句子的拼接词串长度 + 用来匹配分词结果的移除标点后的拼接词串长度 + 当前词长度大于maxLength，则将已有拼接词长度提交为字幕句子。
                        if (concatWordsToBeSubtitleSentenceLength + concatWordsToMatchSegmentWordsLength
                                + currentWordLength > maxLengthFloat) {
                            TtsSubtitleDTO sentence = new TtsSubtitleDTO();
                            sentence.setText(concatWordsToBeSubtitleSentence.toString());
                            sentence.setBeginTime(sentenceBeginTime);
                            sentence.setEndTime(sentenceEndTime);
                            resultList.add(sentence);

                            //重置字幕句子
                            concatWordsToBeSubtitleSentence = new StringBuilder();
                            concatWordsToBeSubtitleSentenceLength = 0.0f;

                            sentenceBeginTime = segmentBeginTime;
                            if (StringUtils.isBlank(concatWholeWordsToMatchSegmentWords)) {
                                segmentBeginTime = currentWordDTO.getStartTime();
                            }

                            concatWordsToMatchSegmentWords = concatWordsToMatchSegmentWords + rmPuncCurrentWord;
                            concatWordsToMatchSegmentWordsLength =
                                    concatWordsToMatchSegmentWordsLength + currentWordLength;
                            concatWholeWordsToMatchSegmentWords =
                                    concatWholeWordsToMatchSegmentWords + currentWordDTO.getWord();
                        } else {
                            //rmPuncCurrentWord不是空串，即表明该词不是标点符号
                            if (!StringUtils.isBlank(rmPuncCurrentWord)) {
                                if (StringUtils.isBlank(concatWholeWordsToMatchSegmentWords)) {
                                    segmentBeginTime = currentWordDTO.getStartTime();
                                }
                                concatWordsToMatchSegmentWords = concatWordsToMatchSegmentWords + rmPuncCurrentWord;
                                concatWordsToMatchSegmentWordsLength =
                                        concatWordsToMatchSegmentWordsLength + currentWordLength;

                            } else {
                                //该词是标点符号
                                //遇到以下几个标点符号，则强制提交为字幕句子
                                if (currentWordDTO.getWord().contains("，") || currentWordDTO.getWord().contains("。")
                                        || currentWordDTO.getWord().contains("！") || currentWordDTO.getWord()
                                        .contains("!") || currentWordDTO.getWord().contains("?") || currentWordDTO
                                        .getWord().contains("？") || currentWordDTO.getWord().contains(",")) {
                                    //拼接当前标点到句子中
                                    concatWholeWordsToMatchSegmentWords =
                                            concatWholeWordsToMatchSegmentWords + currentWordDTO.getWord();
                                    concatWordsToBeSubtitleSentence.append(concatWholeWordsToMatchSegmentWords);
                                    sentenceEndTime = currentWordDTO.getEndTime();

                                    TtsSubtitleDTO sentence = new TtsSubtitleDTO();
                                    sentence.setText(concatWordsToBeSubtitleSentence.toString());
                                    sentence.setBeginTime(sentenceBeginTime);
                                    sentence.setEndTime(sentenceEndTime);
                                    resultList.add(sentence);

                                    concatWordsToBeSubtitleSentence = new StringBuilder();
                                    concatWordsToBeSubtitleSentenceLength = 0.0f;

                                    concatWordsToMatchSegmentWords = "";
                                    concatWholeWordsToMatchSegmentWords = "";
                                    concatWordsToMatchSegmentWordsLength = 0.0f;
                                    continue;
                                }

                                if (StringUtils.isBlank(concatWholeWordsToMatchSegmentWords)) {
                                    segmentBeginTime = currentWordDTO.getStartTime();
                                }
                                //若分词匹配已完成，而当前这个词又是标点，则直接将当前词拼到句子中。
                                if (segmentMatchFinished) {
                                    concatWholeWordsToMatchSegmentWords =
                                            concatWholeWordsToMatchSegmentWords + currentWordDTO.getWord();
                                    concatWordsToBeSubtitleSentence.append(concatWholeWordsToMatchSegmentWords);
                                    sentenceEndTime = currentWordDTO.getEndTime();

                                    TtsSubtitleDTO sentence = new TtsSubtitleDTO();
                                    sentence.setText(concatWordsToBeSubtitleSentence.toString());
                                    sentence.setBeginTime(sentenceBeginTime);
                                    sentence.setEndTime(sentenceEndTime);
                                    resultList.add(sentence);
                                    continue;
                                }
                            }
                            concatWholeWordsToMatchSegmentWords =
                                    concatWholeWordsToMatchSegmentWords + currentWordDTO.getWord();
                        }
                        continue;
                    }

                    //若拼接词与分段词相等
                    concatWordsToMatchSegmentWords = concatWordsToMatchSegmentWords + rmPuncCurrentWord;
                    concatWordsToMatchSegmentWordsLength = concatWordsToMatchSegmentWordsLength + currentWordLength;
                    concatWholeWordsToMatchSegmentWords =
                            concatWholeWordsToMatchSegmentWords + currentWordDTO.getWord();

                    //如果已有拼接词长度 + 用来匹配分词结果的移除标点后的拼接词串长度+当前词长度小于等于maxLength，则将当前词拼接到concatWordsToBeSubtitleSentence
                    if (concatWordsToBeSubtitleSentenceLength + concatWordsToMatchSegmentWordsLength
                            <= maxLengthFloat) {
                        concatWordsToBeSubtitleSentence.append(concatWholeWordsToMatchSegmentWords);
                        concatWordsToBeSubtitleSentenceLength += concatWordsToMatchSegmentWordsLength;
                        sentenceEndTime = currentWordDTO.getEndTime();
                        //遇到以下几个标点符号，则强制提交为字幕句子
                        if (currentWordDTO.getWord().contains("，") || currentWordDTO.getWord().contains("。")
                                || currentWordDTO.getWord().contains("！") || currentWordDTO.getWord().contains("!")
                                || currentWordDTO.getWord().contains("?") || currentWordDTO.getWord().contains("？")
                                || currentWordDTO.getWord().contains(",")) {
                            TtsSubtitleDTO sentence = new TtsSubtitleDTO();
                            sentence.setText(concatWordsToBeSubtitleSentence.toString());
                            sentence.setBeginTime(sentenceBeginTime);
                            sentence.setEndTime(sentenceEndTime);
                            resultList.add(sentence);

                            concatWordsToBeSubtitleSentence = new StringBuilder();
                            concatWordsToBeSubtitleSentenceLength = 0.0f;

                            //如果该词是sentencesDTO的最后一个词，则提交为句子
                        } else if (i == sentencesDTO.getWords().size() - 1) {
                            TtsSubtitleDTO sentence = new TtsSubtitleDTO();
                            sentence.setText(concatWordsToBeSubtitleSentence.toString());
                            sentence.setBeginTime(sentenceBeginTime);
                            sentence.setEndTime(sentenceEndTime);
                            resultList.add(sentence);

                            concatWordsToBeSubtitleSentence = new StringBuilder();
                            concatWordsToBeSubtitleSentenceLength = 0.0f;
                        }

                    } else {
                        //如果已有拼接词长度+当前词长度大于maxLength，则将已有拼接句子提交到resultList。然后重置concatWords为当前词。
                        TtsSubtitleDTO sentence = new TtsSubtitleDTO();
                        sentence.setText(concatWordsToBeSubtitleSentence.toString());
                        sentence.setBeginTime(sentenceBeginTime);
                        sentence.setEndTime(sentenceEndTime);
                        resultList.add(sentence);

                        //当当前词包含以下几个标点符号，则强制提交当前词为字幕句子
                        if (currentWordDTO.getWord().contains("，") || currentWordDTO.getWord().contains("。")
                                || currentWordDTO.getWord().contains("！") || currentWordDTO.getWord().contains("!")
                                || currentWordDTO.getWord().contains("?") || currentWordDTO.getWord().contains("？")
                                || currentWordDTO.getWord().contains(",")) {
                            TtsSubtitleDTO sentence2 = new TtsSubtitleDTO();
                            sentence2.setText(concatWholeWordsToMatchSegmentWords);
                            sentence2.setBeginTime(segmentBeginTime);
                            sentence2.setEndTime(currentWordDTO.getEndTime());
                            resultList.add(sentence2);

                            concatWordsToBeSubtitleSentence = new StringBuilder();
                            concatWordsToBeSubtitleSentenceLength = 0.0f;
                        } else {
                            //重置concatWords为当前词
                            concatWordsToBeSubtitleSentence = new StringBuilder();
                            concatWordsToBeSubtitleSentence.append(concatWholeWordsToMatchSegmentWords);
                            concatWordsToBeSubtitleSentenceLength = concatWordsToMatchSegmentWordsLength;
                            sentenceBeginTime = segmentBeginTime;
                            sentenceEndTime = currentWordDTO.getEndTime();
                        }
                    }

                    concatWordsToMatchSegmentWords = "";
                    concatWholeWordsToMatchSegmentWords = "";
                    concatWordsToMatchSegmentWordsLength = 0.0f;

                    if (segmentResultsIndex != segmentResults.size() - 1) {
                        segmentResultsIndex += 1;
                    } else {
                        //分词匹配完成 标志位 设置为true
                        segmentMatchFinished = true;
                    }
                }
            }

            //全局处理：1.将、替换为空格 2.去除。和， 3.去除收尾空格
            resultList.forEach(result -> {
                String text = result.getText().replaceAll("、", " ").replaceAll("。", "").replaceAll("，", "")
                        .replaceAll(",", "").trim();
                result.setText(text);
            });
            return resultList;
        } catch (Exception e) {
            LOGGER.error("通过分词器生成字幕发生异常!,originalText:{},,,maxLength:{},,,,,,,ttsResultSentnecesList:{},,,,,e:{}",
                    originalText, maxLength, JSONUtil.toJsonStr(ttsResultSentnecesList), e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理英文缩写
     *
     * @param input
     * @return
     */
    public static List<String> mergeEnglishAbbreviations(List<String> input) {
        List<String> result = new ArrayList<>();

        for (int i = 0; i < input.size(); i++) {
            String s = input.get(i);
            //若当前是英文字母并且下一个是'
            if (s.matches(SubtitleConst.ENGLISH_CHAR) && i < input.size() - 1 && "'".equals(input.get(i + 1))) {
                //跳过当前
                continue;
            }

            if ("'".equals(s) && i + 1 < input.size() && input.get(i + 1).matches(SubtitleConst.ENGLISH_CHAR)) {
                result.add(mergeAbbreviation(input, i));
                i++; // Skip the next word since it's already merged
            } else {
                result.add(s);
            }
        }

        return result;
    }

    private static String mergeAbbreviation(List<String> input, int index) {
        String wordBeforeApostrophe = input.get(index - 1);
        String wordAfterApostrophe = input.get(index + 1);
        String merged = wordBeforeApostrophe + "'" + wordAfterApostrophe;
        return merged + "'";
    }

    public static void main(String[] args) throws Exception {
        //方法1：绝对路径加载dic
        /*MyStaticValue.ENV.put(DicLibrary.DEFAULT,
                "/Users/<USER>/git_repository/dl-ai-service/biz/target/classes/library/default.dic");*/
        SubtitleManager subtitleManager = new SubtitleManager();
        //方法2：相对路径加载dic
        //org.springframework.core.io.Resource resource = new ClassPathResource("/library/userLibrary.dic");
        //方法3：从网络上加载dic
        org.springframework.core.io.Resource resource = new UrlResource(
                "https://dl-test-1309667514.cos.ap-shanghai.myqcloud.com/ansjseg/userLibrary.dic");
        InputStream fis = resource.getInputStream();
        Forest forest = null;
        forest = Library.makeForest(fis);

        String testSegmentStr = "医药行业迎来重大利好，药明康德管理层近日回应称，零售药店前景广阔，欣旺达最近很活跃，圆晶制造业有很大利好";
        System.out.println(ToAnalysis.parse(testSegmentStr));
        System.out.println(ToAnalysis.parse(testSegmentStr, forest));

        /*System.out.println(JSONUtil.toJsonStr(subtitleManager.segmentor(testSegmentStr)));
        System.out.println(JSONUtil.toJsonStr(subtitleManager.nlpSegmentor(testSegmentStr)));
        System.out.println(JSONUtil.toJsonStr(subtitleManager.dicSegmentor(testSegmentStr)));*/

        System.out.println("====================分界线====================");
        System.out.println("====================分界线====================");

        //todo:case1.1-腾讯云:
        //String str = "华为P70系列手机发布，4月18日“先锋计划”启动，Pura70Ultra和Pura70Pro迅速售罄。作为华为影像旗舰，P系列新机在光学、外观、卫星通话等方面进行了多项升级，销量的热卖验证了技术路径的可行性，对产业链上下游均产生了积极影响。";
        //String jsonStr = "[{\"sentence\":\"华为P70系列手机发布，4月18日“先锋计划”启动，Pura\",\"words\":[{\"word\":\"华\",\"endTimestamp\":3170000,\"startTimestamp\":1670000},{\"word\":\"为\",\"endTimestamp\":4670000,\"startTimestamp\":3170000},{\"word\":\"P\",\"endTimestamp\":6250000,\"startTimestamp\":4670000},{\"word\":\"7\",\"endTimestamp\":7920000,\"startTimestamp\":6250000},{\"word\":\"0\",\"endTimestamp\":9510000,\"startTimestamp\":7920000},{\"word\":\"系\",\"endTimestamp\":11510000,\"startTimestamp\":9510000},{\"word\":\"列\",\"endTimestamp\":13010000,\"startTimestamp\":11510000},{\"word\":\"手\",\"endTimestamp\":14760000,\"startTimestamp\":13010000},{\"word\":\"机\",\"endTimestamp\":16260000,\"startTimestamp\":14760000},{\"word\":\"发\",\"endTimestamp\":18100000,\"startTimestamp\":16260000},{\"word\":\"布，\",\"endTimestamp\":19600000,\"startTimestamp\":18100000},{\"word\":\"4\",\"endTimestamp\":22850000,\"startTimestamp\":21100000},{\"word\":\"月\",\"endTimestamp\":24180000,\"startTimestamp\":22850000},{\"word\":\"1\",\"endTimestamp\":26090000,\"startTimestamp\":24180000},{\"word\":\"8\",\"endTimestamp\":28090000,\"startTimestamp\":26090000},{\"word\":\"日“\",\"endTimestamp\":29500000,\"startTimestamp\":28090000},{\"word\":\"先\",\"endTimestamp\":31750000,\"startTimestamp\":29500000},{\"word\":\"锋\",\"endTimestamp\":33660000,\"startTimestamp\":31750000},{\"word\":\"计\",\"endTimestamp\":34910000,\"startTimestamp\":33660000},{\"word\":\"划”\",\"endTimestamp\":36910000,\"startTimestamp\":34910000},{\"word\":\"启\",\"endTimestamp\":38660000,\"startTimestamp\":36910000},{\"word\":\"动，\",\"endTimestamp\":40070000,\"startTimestamp\":38660000},{\"word\":\"Pura\",\"endTimestamp\":50410000,\"startTimestamp\":46410000}]},{\"sentence\":\"70Ultra和Pura70Pro迅速售罄。\",\"words\":[{\"word\":\"70\",\"endTimestamp\":51910000,\"startTimestamp\":53330000},{\"word\":\"Ultra\",\"endTimestamp\":57910000,\"startTimestamp\":53330000},{\"word\":\"和\",\"endTimestamp\":60000000,\"startTimestamp\":57910000},{\"word\":\"Pura\",\"endTimestamp\":62750000,\"startTimestamp\":60000000},{\"word\":\"70\",\"endTimestamp\":66000000,\"startTimestamp\":62750000},{\"word\":\"Pro\",\"endTimestamp\":69250000,\"startTimestamp\":66000000},{\"word\":\"迅\",\"endTimestamp\":71830000,\"startTimestamp\":70000000},{\"word\":\"速\",\"endTimestamp\":73410000,\"startTimestamp\":71830000},{\"word\":\"售\",\"endTimestamp\":75240000,\"startTimestamp\":73410000},{\"word\":\"罄。\",\"endTimestamp\":77240000,\"startTimestamp\":75240000}]},{\"sentence\":\"作为华为影像旗舰，P系列新机在光学、外观、卫星通话等方面进行\",\"words\":[{\"word\":\"作\",\"endTimestamp\":84740000,\"startTimestamp\":83330000},{\"word\":\"为\",\"endTimestamp\":86740000,\"startTimestamp\":84740000},{\"word\":\"华\",\"endTimestamp\":89240000,\"startTimestamp\":86740000},{\"word\":\"为\",\"endTimestamp\":90570000,\"startTimestamp\":89240000},{\"word\":\"影\",\"endTimestamp\":92570000,\"startTimestamp\":90570000},{\"word\":\"像\",\"endTimestamp\":94820000,\"startTimestamp\":92570000},{\"word\":\"旗\",\"endTimestamp\":96820000,\"startTimestamp\":94820000},{\"word\":\"舰，\",\"endTimestamp\":98820000,\"startTimestamp\":96820000},{\"word\":\"P\",\"endTimestamp\":106650000,\"startTimestamp\":104820000},{\"word\":\"系\",\"endTimestamp\":108490000,\"startTimestamp\":106650000},{\"word\":\"列\",\"endTimestamp\":109740000,\"startTimestamp\":108490000},{\"word\":\"新\",\"endTimestamp\":111740000,\"startTimestamp\":109740000},{\"word\":\"机\",\"endTimestamp\":113410000,\"startTimestamp\":111740000},{\"word\":\"在\",\"endTimestamp\":115240000,\"startTimestamp\":113410000},{\"word\":\"光\",\"endTimestamp\":117070000,\"startTimestamp\":115240000},{\"word\":\"学、\",\"endTimestamp\":119230000,\"startTimestamp\":117070000},{\"word\":\"外\",\"endTimestamp\":120980000,\"startTimestamp\":119230000},{\"word\":\"观、\",\"endTimestamp\":122900000,\"startTimestamp\":120980000},{\"word\":\"卫\",\"endTimestamp\":124150000,\"startTimestamp\":122900000},{\"word\":\"星\",\"endTimestamp\":126230000,\"startTimestamp\":124150000},{\"word\":\"通\",\"endTimestamp\":127480000,\"startTimestamp\":126230000},{\"word\":\"话\",\"endTimestamp\":129310000,\"startTimestamp\":127480000},{\"word\":\"等\",\"endTimestamp\":130970000,\"startTimestamp\":129310000},{\"word\":\"方\",\"endTimestamp\":132220000,\"startTimestamp\":130970000},{\"word\":\"面\",\"endTimestamp\":134220000,\"startTimestamp\":132220000},{\"word\":\"进\",\"endTimestamp\":135970000,\"startTimestamp\":134220000},{\"word\":\"行\",\"endTimestamp\":137640000,\"startTimestamp\":135970000}]},{\"sentence\":\"了多项升级，销量的热卖验证了技术路径的可行性，对产业链上下游\",\"words\":[{\"word\":\"了\",\"endTimestamp\":138720000,\"startTimestamp\":137640000},{\"word\":\"多\",\"endTimestamp\":140300000,\"startTimestamp\":138720000},{\"word\":\"项\",\"endTimestamp\":141960000,\"startTimestamp\":140300000},{\"word\":\"升\",\"endTimestamp\":144040000,\"startTimestamp\":141960000},{\"word\":\"级，\",\"endTimestamp\":146040000,\"startTimestamp\":144040000},{\"word\":\"销\",\"endTimestamp\":153880000,\"startTimestamp\":151880000},{\"word\":\"量\",\"endTimestamp\":155290000,\"startTimestamp\":153880000},{\"word\":\"的\",\"endTimestamp\":156040000,\"startTimestamp\":155290000},{\"word\":\"热\",\"endTimestamp\":157380000,\"startTimestamp\":156040000},{\"word\":\"卖\",\"endTimestamp\":160050000,\"startTimestamp\":157380000},{\"word\":\"验\",\"endTimestamp\":161970000,\"startTimestamp\":160050000},{\"word\":\"证\",\"endTimestamp\":163130000,\"startTimestamp\":161970000},{\"word\":\"了\",\"endTimestamp\":163880000,\"startTimestamp\":163130000},{\"word\":\"技\",\"endTimestamp\":165550000,\"startTimestamp\":163880000},{\"word\":\"术\",\"endTimestamp\":167220000,\"startTimestamp\":165550000},{\"word\":\"路\",\"endTimestamp\":168890000,\"startTimestamp\":167220000},{\"word\":\"径\",\"endTimestamp\":170470000,\"startTimestamp\":168890000},{\"word\":\"的\",\"endTimestamp\":171050000,\"startTimestamp\":170470000},{\"word\":\"可\",\"endTimestamp\":172630000,\"startTimestamp\":171050000},{\"word\":\"行\",\"endTimestamp\":174460000,\"startTimestamp\":172630000},{\"word\":\"性，\",\"endTimestamp\":176460000,\"startTimestamp\":174460000},{\"word\":\"对\",\"endTimestamp\":184040000,\"startTimestamp\":182630000},{\"word\":\"产\",\"endTimestamp\":185710000,\"startTimestamp\":184040000},{\"word\":\"业\",\"endTimestamp\":187380000,\"startTimestamp\":185710000},{\"word\":\"链\",\"endTimestamp\":188880000,\"startTimestamp\":187380000},{\"word\":\"上\",\"endTimestamp\":190880000,\"startTimestamp\":188880000},{\"word\":\"下\",\"endTimestamp\":192710000,\"startTimestamp\":190880000},{\"word\":\"游\",\"endTimestamp\":194630000,\"startTimestamp\":192710000}]},{\"sentence\":\"均产生了积极影响。\",\"words\":[{\"word\":\"均\",\"endTimestamp\":196630000,\"startTimestamp\":194630000},{\"word\":\"产\",\"endTimestamp\":198550000,\"startTimestamp\":196630000},{\"word\":\"生\",\"endTimestamp\":199800000,\"startTimestamp\":198550000},{\"word\":\"了\",\"endTimestamp\":200630000,\"startTimestamp\":199800000},{\"word\":\"积\",\"endTimestamp\":202630000,\"startTimestamp\":200630000},{\"word\":\"极\",\"endTimestamp\":204550000,\"startTimestamp\":202630000},{\"word\":\"影\",\"endTimestamp\":205550000,\"startTimestamp\":204550000},{\"word\":\"响\",\"endTimestamp\":207550000,\"startTimestamp\":205550000}]}]";
        //todo:case1.2-腾讯云:
        //String str = "南京市发布《南京市促进低空经济高质量发展实施方案（2024—2026）》，明确提出利用5.5g技术，为低空经济注入数字化、智能化的全新活力。";
        //String jsonStr = "[{\"sentence\":\"南京市发布《南京市促进低空经济高质量发展实施方案（2024—\",\"words\":[{\"word\":\"南\",\"endTimestamp\":3260000,\"startTimestamp\":1670000},{\"word\":\"京\",\"endTimestamp\":4510000,\"startTimestamp\":3260000},{\"word\":\"市\",\"endTimestamp\":6260000,\"startTimestamp\":4510000},{\"word\":\"发\",\"endTimestamp\":8180000,\"startTimestamp\":6260000},{\"word\":\"布《\",\"endTimestamp\":9930000,\"startTimestamp\":8180000},{\"word\":\"南\",\"endTimestamp\":12010000,\"startTimestamp\":9930000},{\"word\":\"京\",\"endTimestamp\":13510000,\"startTimestamp\":12010000},{\"word\":\"市\",\"endTimestamp\":15340000,\"startTimestamp\":13510000},{\"word\":\"促\",\"endTimestamp\":17340000,\"startTimestamp\":15340000},{\"word\":\"进\",\"endTimestamp\":19260000,\"startTimestamp\":17340000},{\"word\":\"低\",\"endTimestamp\":21180000,\"startTimestamp\":19260000},{\"word\":\"空\",\"endTimestamp\":23430000,\"startTimestamp\":21180000},{\"word\":\"经\",\"endTimestamp\":25260000,\"startTimestamp\":23430000},{\"word\":\"济\",\"endTimestamp\":26920000,\"startTimestamp\":25260000},{\"word\":\"高\",\"endTimestamp\":28750000,\"startTimestamp\":26920000},{\"word\":\"质\",\"endTimestamp\":30250000,\"startTimestamp\":28750000},{\"word\":\"量\",\"endTimestamp\":32000000,\"startTimestamp\":30250000},{\"word\":\"发\",\"endTimestamp\":33750000,\"startTimestamp\":32000000},{\"word\":\"展\",\"endTimestamp\":35500000,\"startTimestamp\":33750000},{\"word\":\"实\",\"endTimestamp\":37330000,\"startTimestamp\":35500000},{\"word\":\"施\",\"endTimestamp\":38990000,\"startTimestamp\":37330000},{\"word\":\"方\",\"endTimestamp\":40910000,\"startTimestamp\":38990000},{\"word\":\"案\",\"endTimestamp\":42990000,\"startTimestamp\":40910000},{\"word\":\"（\",\"endTimestamp\":44570000,\"startTimestamp\":42990000},{\"word\":\"2\",\"endTimestamp\":46910000,\"startTimestamp\":44570000},{\"word\":\"0\",\"endTimestamp\":48830000,\"startTimestamp\":46910000},{\"word\":\"2\",\"endTimestamp\":50330000,\"startTimestamp\":48830000},{\"word\":\"4\",\"endTimestamp\":51830000,\"startTimestamp\":50330000},{\"word\":\"—\",\"endTimestamp\":53580000,\"startTimestamp\":51830000}]},{\"sentence\":\"2026）》，明确提出利用5.5g技术，为低空经济注入数字\",\"words\":[{\"word\":\"2\",\"endTimestamp\":55240000,\"startTimestamp\":53580000},{\"word\":\"0\",\"endTimestamp\":57160000,\"startTimestamp\":55240000},{\"word\":\"2\",\"endTimestamp\":59500000,\"startTimestamp\":57160000},{\"word\":\"6\",\"endTimestamp\":61250000,\"startTimestamp\":59500000},{\"word\":\"）\",\"endTimestamp\":62750000,\"startTimestamp\":61250000},{\"word\":\"》\",\"endTimestamp\":64420000,\"startTimestamp\":62750000},{\"word\":\"，\",\"endTimestamp\":65920000,\"startTimestamp\":64420000},{\"word\":\"明\",\"endTimestamp\":74180000,\"startTimestamp\":72260000},{\"word\":\"确\",\"endTimestamp\":76100000,\"startTimestamp\":74180000},{\"word\":\"提\",\"endTimestamp\":77510000,\"startTimestamp\":76100000},{\"word\":\"出\",\"endTimestamp\":79090000,\"startTimestamp\":77510000},{\"word\":\"利\",\"endTimestamp\":80430000,\"startTimestamp\":79090000},{\"word\":\"用\",\"endTimestamp\":82180000,\"startTimestamp\":80430000},{\"word\":\"5\",\"endTimestamp\":83600000,\"startTimestamp\":82180000},{\"word\":\".\",\"endTimestamp\":85270000,\"startTimestamp\":83600000},{\"word\":\"5\",\"endTimestamp\":86770000,\"startTimestamp\":85270000},{\"word\":\"g\",\"endTimestamp\":88190000,\"startTimestamp\":86770000},{\"word\":\"技\",\"endTimestamp\":89780000,\"startTimestamp\":88190000},{\"word\":\"术，\",\"endTimestamp\":92110000,\"startTimestamp\":89780000},{\"word\":\"为\",\"endTimestamp\":96950000,\"startTimestamp\":95780000},{\"word\":\"低\",\"endTimestamp\":98450000,\"startTimestamp\":96950000},{\"word\":\"空\",\"endTimestamp\":100290000,\"startTimestamp\":98450000},{\"word\":\"经\",\"endTimestamp\":102210000,\"startTimestamp\":100290000},{\"word\":\"济\",\"endTimestamp\":103800000,\"startTimestamp\":102210000},{\"word\":\"注\",\"endTimestamp\":105470000,\"startTimestamp\":103800000},{\"word\":\"入\",\"endTimestamp\":106720000,\"startTimestamp\":105470000},{\"word\":\"数\",\"endTimestamp\":108560000,\"startTimestamp\":106720000},{\"word\":\"字\",\"endTimestamp\":109810000,\"startTimestamp\":108560000}]},{\"sentence\":\"化、智能化的全新活力。\",\"words\":[{\"word\":\"化、\",\"endTimestamp\":111970000,\"startTimestamp\":109810000},{\"word\":\"智\",\"endTimestamp\":113890000,\"startTimestamp\":111970000},{\"word\":\"能\",\"endTimestamp\":115470000,\"startTimestamp\":113890000},{\"word\":\"化\",\"endTimestamp\":117140000,\"startTimestamp\":115470000},{\"word\":\"的\",\"endTimestamp\":118220000,\"startTimestamp\":117140000},{\"word\":\"全\",\"endTimestamp\":120390000,\"startTimestamp\":118220000},{\"word\":\"新\",\"endTimestamp\":121970000,\"startTimestamp\":120390000},{\"word\":\"活\",\"endTimestamp\":123800000,\"startTimestamp\":121970000},{\"word\":\"力。\",\"endTimestamp\":125390000,\"startTimestamp\":123800000}]}]";
        //todo:case1.3-腾讯云-英语
        //String str = "Success is not final,failure is not fatal,it's the courage to continue that counts.Courage doesn't always roar.Sometimes courage is the quiet voice at the end of the day saying,I will try again tomorrow";
        //String jsonStr = "[{\"sentence\":\"Success is not final,failure\",\"words\":[{\"word\":\"Success\",\"endTimestamp\":7000000,\"startTimestamp\":1670000},{\"word\":\" is\",\"endTimestamp\":8500000,\"startTimestamp\":7000000},{\"word\":\" not\",\"endTimestamp\":10580000,\"startTimestamp\":8500000},{\"word\":\" final,\",\"endTimestamp\":14740000,\"startTimestamp\":10580000},{\"word\":\"failure\",\"endTimestamp\":21410000,\"startTimestamp\":17410000}]},{\"sentence\":\" is not fatal,it's the courage\",\"words\":[{\"word\":\" is\",\"endTimestamp\":23330000,\"startTimestamp\":21410000},{\"word\":\" not\",\"endTimestamp\":25920000,\"startTimestamp\":23330000},{\"word\":\" fatal,\",\"endTimestamp\":29330000,\"startTimestamp\":25920000},{\"word\":\"it's\",\"endTimestamp\":36990000,\"startTimestamp\":35080000},{\"word\":\" the\",\"endTimestamp\":37910000,\"startTimestamp\":36990000},{\"word\":\" courage\",\"endTimestamp\":43820000,\"startTimestamp\":37910000}]},{\"sentence\":\" to continue that counts.\",\"words\":[{\"word\":\" to\",\"endTimestamp\":45320000,\"startTimestamp\":43820000},{\"word\":\" continue\",\"endTimestamp\":50400000,\"startTimestamp\":45320000},{\"word\":\" that\",\"endTimestamp\":52480000,\"startTimestamp\":50400000},{\"word\":\" counts.\",\"endTimestamp\":57240000,\"startTimestamp\":52480000}]},{\"sentence\":\"Courage doesn't always roar.\",\"words\":[{\"word\":\"Courage\",\"endTimestamp\":64490000,\"startTimestamp\":60070000},{\"word\":\" doesn't\",\"endTimestamp\":67330000,\"startTimestamp\":64490000},{\"word\":\" always\",\"endTimestamp\":71160000,\"startTimestamp\":67330000},{\"word\":\" roar.\",\"endTimestamp\":74160000,\"startTimestamp\":71160000}]},{\"sentence\":\"Sometimes courage is the quiet\",\"words\":[{\"word\":\"Sometimes\",\"endTimestamp\":82660000,\"startTimestamp\":77080000},{\"word\":\" courage\",\"endTimestamp\":87750000,\"startTimestamp\":82660000},{\"word\":\" is\",\"endTimestamp\":89670000,\"startTimestamp\":87750000},{\"word\":\" the\",\"endTimestamp\":90420000,\"startTimestamp\":89670000},{\"word\":\" quiet\",\"endTimestamp\":94830000,\"startTimestamp\":90420000}]},{\"sentence\":\" voice at the end of the day\",\"words\":[{\"word\":\" voice\",\"endTimestamp\":99240000,\"startTimestamp\":94830000},{\"word\":\" at\",\"endTimestamp\":101240000,\"startTimestamp\":99240000},{\"word\":\" the\",\"endTimestamp\":102320000,\"startTimestamp\":101240000},{\"word\":\" end\",\"endTimestamp\":105150000,\"startTimestamp\":102320000},{\"word\":\" of\",\"endTimestamp\":106070000,\"startTimestamp\":105150000},{\"word\":\" the\",\"endTimestamp\":107070000,\"startTimestamp\":106070000},{\"word\":\" day\",\"endTimestamp\":108990000,\"startTimestamp\":107070000}]},{\"sentence\":\" saying,I will try again\",\"words\":[{\"word\":\" saying,\",\"endTimestamp\":113000000,\"startTimestamp\":108990000},{\"word\":\"I\",\"endTimestamp\":120170000,\"startTimestamp\":118840000},{\"word\":\" will\",\"endTimestamp\":122000000,\"startTimestamp\":120170000},{\"word\":\" try\",\"endTimestamp\":124830000,\"startTimestamp\":122000000},{\"word\":\" again\",\"endTimestamp\":128080000,\"startTimestamp\":124830000}]},{\"sentence\":\" tomorrow\",\"words\":[{\"word\":\" tomorrow\",\"endTimestamp\":133590000,\"startTimestamp\":128080000}]}]";
        //todo:case1.4-腾讯云-英语
        //String str = "Obama called thecurrent downturn \"the worst financial crisis since the GreatDepression\" and said the government must assure that the financial rescuepackage signed into law worked as planned.";
        //String jsonStr = "[{\"sentence\":\"Obama called thecurrent\",\"words\":[{\"word\":\"Obama\",\"endTimestamp\":5670000,\"startTimestamp\":1670000},{\"word\":\" called\",\"endTimestamp\":7840000,\"startTimestamp\":5670000},{\"word\":\" thecurrent\",\"endTimestamp\":13010000,\"startTimestamp\":7840000}]},{\"sentence\":\" downturn \\\"the worst financial\",\"words\":[{\"word\":\" downturn\",\"endTimestamp\":18850000,\"startTimestamp\":13010000},{\"word\":\" \\\"the\",\"endTimestamp\":19850000,\"startTimestamp\":18850000},{\"word\":\" worst\",\"endTimestamp\":23600000,\"startTimestamp\":19850000},{\"word\":\" financial\",\"endTimestamp\":28530000,\"startTimestamp\":23600000}]},{\"sentence\":\" crisis since the\",\"words\":[{\"word\":\" crisis\",\"endTimestamp\":34280000,\"startTimestamp\":28530000},{\"word\":\" since\",\"endTimestamp\":36950000,\"startTimestamp\":34280000},{\"word\":\" the\",\"endTimestamp\":37860000,\"startTimestamp\":36950000}]},{\"sentence\":\" GreatDepression\\\" and said the\",\"words\":[{\"word\":\" GreatDepression\\\"\",\"endTimestamp\":47020000,\"startTimestamp\":37860000},{\"word\":\" and\",\"endTimestamp\":49430000,\"startTimestamp\":47020000},{\"word\":\" said\",\"endTimestamp\":52600000,\"startTimestamp\":49430000},{\"word\":\" the\",\"endTimestamp\":54680000,\"startTimestamp\":52600000}]},{\"sentence\":\" government must assure that\",\"words\":[{\"word\":\" government\",\"endTimestamp\":59860000,\"startTimestamp\":54680000},{\"word\":\" must\",\"endTimestamp\":62940000,\"startTimestamp\":59860000},{\"word\":\" assure\",\"endTimestamp\":67190000,\"startTimestamp\":62940000},{\"word\":\" that\",\"endTimestamp\":70600000,\"startTimestamp\":67190000}]},{\"sentence\":\" the financial rescuepackage\",\"words\":[{\"word\":\" the\",\"endTimestamp\":71850000,\"startTimestamp\":70600000},{\"word\":\" financial\",\"endTimestamp\":77680000,\"startTimestamp\":71850000},{\"word\":\" rescuepackage\",\"endTimestamp\":85850000,\"startTimestamp\":77680000}]},{\"sentence\":\" signed into law worked as\",\"words\":[{\"word\":\" signed\",\"endTimestamp\":89260000,\"startTimestamp\":85850000},{\"word\":\" into\",\"endTimestamp\":92090000,\"startTimestamp\":89260000},{\"word\":\" law\",\"endTimestamp\":94260000,\"startTimestamp\":92090000},{\"word\":\" worked\",\"endTimestamp\":97510000,\"startTimestamp\":94260000},{\"word\":\" as\",\"endTimestamp\":98930000,\"startTimestamp\":97510000}]},{\"sentence\":\" planned.\",\"words\":[{\"word\":\" planned.\",\"endTimestamp\":103260000,\"startTimestamp\":98930000}]}]";
        //todo:case1.5-腾讯云-标点符号
        String str = "南京市英文逗号,中文逗号，英文句号.中文句号。英文分号;中文分号；英文单引号'中文单引号‘中文顿号、英文斜杆/中文问号？英文问号?中文冒号：英文冒号:中文双引号“”英文双引号\"\"中文方括号【】英文方括号[]中文大括号{}英文大括号{}中文点·英文点`中文感叹号！英文感叹号!中文艾特符号@英文艾特符号@中文井号#英文井号#中文美元￥英文美元$中文百分号%英文百分号%中文上箭头……英文上箭头^中文and符号&;英文and符号&;中文星号*英文星号*中文括号（）英文括号()中文减号-英文减号-中文等号=英文等号=中文加号+英文加号+中文长横线——英文长横线_";
        String jsonStr = "[{\"sentence\":\"南京市英文逗号,中文逗号，英文句号.中文句号。\",\"words\":[{\"word\":\"南\",\"endTimestamp\":3420000,\"startTimestamp\":1670000},{\"word\":\"京\",\"endTimestamp\":4670000,\"startTimestamp\":3420000},{\"word\":\"市\",\"endTimestamp\":6420000,\"startTimestamp\":4670000},{\"word\":\"英\",\"endTimestamp\":8000000,\"startTimestamp\":6420000},{\"word\":\"文\",\"endTimestamp\":9170000,\"startTimestamp\":8000000},{\"word\":\"逗\",\"endTimestamp\":10750000,\"startTimestamp\":9170000},{\"word\":\"号,\",\"endTimestamp\":12830000,\"startTimestamp\":10750000},{\"word\":\"中\",\"endTimestamp\":16830000,\"startTimestamp\":15160000},{\"word\":\"文\",\"endTimestamp\":18000000,\"startTimestamp\":16830000},{\"word\":\"逗\",\"endTimestamp\":19420000,\"startTimestamp\":18000000},{\"word\":\"号，\",\"endTimestamp\":21510000,\"startTimestamp\":19420000},{\"word\":\"英\",\"endTimestamp\":24260000,\"startTimestamp\":22930000},{\"word\":\"文\",\"endTimestamp\":25680000,\"startTimestamp\":24260000},{\"word\":\"句\",\"endTimestamp\":27180000,\"startTimestamp\":25680000},{\"word\":\"号.\",\"endTimestamp\":29340000,\"startTimestamp\":27180000},{\"word\":\"中\",\"endTimestamp\":32590000,\"startTimestamp\":30840000},{\"word\":\"文\",\"endTimestamp\":33840000,\"startTimestamp\":32590000},{\"word\":\"句\",\"endTimestamp\":35090000,\"startTimestamp\":33840000},{\"word\":\"号。\",\"endTimestamp\":37250000,\"startTimestamp\":35090000}]},{\"sentence\":\"英文分号;中文分号；\",\"words\":[{\"word\":\"英\",\"endTimestamp\":44830000,\"startTimestamp\":43250000},{\"word\":\"文\",\"endTimestamp\":46080000,\"startTimestamp\":44830000},{\"word\":\"分\",\"endTimestamp\":48000000,\"startTimestamp\":46080000},{\"word\":\"号;\",\"endTimestamp\":50330000,\"startTimestamp\":48000000},{\"word\":\"中\",\"endTimestamp\":57920000,\"startTimestamp\":55920000},{\"word\":\"文\",\"endTimestamp\":59250000,\"startTimestamp\":57920000},{\"word\":\"分\",\"endTimestamp\":61000000,\"startTimestamp\":59250000},{\"word\":\"号；\",\"endTimestamp\":63250000,\"startTimestamp\":61000000}]},{\"sentence\":\"英文单引号'中文单引号‘中文顿号、英文斜杆/中文问号？\",\"words\":[{\"word\":\"英\",\"endTimestamp\":69920000,\"startTimestamp\":69000000},{\"word\":\"文\",\"endTimestamp\":71840000,\"startTimestamp\":69920000},{\"word\":\"单\",\"endTimestamp\":73430000,\"startTimestamp\":71840000},{\"word\":\"引\",\"endTimestamp\":74760000,\"startTimestamp\":73430000},{\"word\":\"号'\",\"endTimestamp\":77010000,\"startTimestamp\":74760000},{\"word\":\"中\",\"endTimestamp\":78840000,\"startTimestamp\":77010000},{\"word\":\"文\",\"endTimestamp\":80260000,\"startTimestamp\":78840000},{\"word\":\"单\",\"endTimestamp\":82010000,\"startTimestamp\":80260000},{\"word\":\"引\",\"endTimestamp\":83010000,\"startTimestamp\":82010000},{\"word\":\"号‘\",\"endTimestamp\":85430000,\"startTimestamp\":83010000},{\"word\":\"中\",\"endTimestamp\":87430000,\"startTimestamp\":85430000},{\"word\":\"文\",\"endTimestamp\":88930000,\"startTimestamp\":87430000},{\"word\":\"顿\",\"endTimestamp\":90180000,\"startTimestamp\":88930000},{\"word\":\"号、\",\"endTimestamp\":92510000,\"startTimestamp\":90180000},{\"word\":\"英\",\"endTimestamp\":94090000,\"startTimestamp\":92510000},{\"word\":\"文\",\"endTimestamp\":96510000,\"startTimestamp\":94090000},{\"word\":\"斜\",\"endTimestamp\":98680000,\"startTimestamp\":96510000},{\"word\":\"杆/\",\"endTimestamp\":100350000,\"startTimestamp\":98680000},{\"word\":\"中\",\"endTimestamp\":102760000,\"startTimestamp\":100350000},{\"word\":\"文\",\"endTimestamp\":104340000,\"startTimestamp\":102760000},{\"word\":\"问\",\"endTimestamp\":105510000,\"startTimestamp\":104340000},{\"word\":\"号？\",\"endTimestamp\":107430000,\"startTimestamp\":105510000}]},{\"sentence\":\"英文问号?\",\"words\":[{\"word\":\"英\",\"endTimestamp\":114940000,\"startTimestamp\":113520000},{\"word\":\"文\",\"endTimestamp\":116190000,\"startTimestamp\":114940000},{\"word\":\"问\",\"endTimestamp\":117610000,\"startTimestamp\":116190000},{\"word\":\"号?\",\"endTimestamp\":119690000,\"startTimestamp\":117610000}]},{\"sentence\":\"中文冒号：英文冒号:中文双引号“”英文双引号\\\"\\\"中文方括\",\"words\":[{\"word\":\"中\",\"endTimestamp\":126940000,\"startTimestamp\":125360000},{\"word\":\"文\",\"endTimestamp\":128020000,\"startTimestamp\":126940000},{\"word\":\"冒\",\"endTimestamp\":129860000,\"startTimestamp\":128020000},{\"word\":\"号：\",\"endTimestamp\":132200000,\"startTimestamp\":129860000},{\"word\":\"英\",\"endTimestamp\":133530000,\"startTimestamp\":132200000},{\"word\":\"文\",\"endTimestamp\":134700000,\"startTimestamp\":133530000},{\"word\":\"冒\",\"endTimestamp\":136530000,\"startTimestamp\":134700000},{\"word\":\"号:\",\"endTimestamp\":138280000,\"startTimestamp\":136530000},{\"word\":\"中\",\"endTimestamp\":144110000,\"startTimestamp\":142610000},{\"word\":\"文\",\"endTimestamp\":145360000,\"startTimestamp\":144110000},{\"word\":\"双\",\"endTimestamp\":147450000,\"startTimestamp\":145360000},{\"word\":\"引\",\"endTimestamp\":148780000,\"startTimestamp\":147450000},{\"word\":\"号“”\",\"endTimestamp\":151110000,\"startTimestamp\":148780000},{\"word\":\"英\",\"endTimestamp\":152780000,\"startTimestamp\":151110000},{\"word\":\"文\",\"endTimestamp\":154530000,\"startTimestamp\":152780000},{\"word\":\"双\",\"endTimestamp\":156700000,\"startTimestamp\":154530000},{\"word\":\"引\",\"endTimestamp\":158030000,\"startTimestamp\":156700000},{\"word\":\"号\\\"\\\"\",\"endTimestamp\":160360000,\"startTimestamp\":158030000},{\"word\":\"中\",\"endTimestamp\":162360000,\"startTimestamp\":160360000},{\"word\":\"文\",\"endTimestamp\":163440000,\"startTimestamp\":162360000},{\"word\":\"方\",\"endTimestamp\":165360000,\"startTimestamp\":163440000},{\"word\":\"括\",\"endTimestamp\":166780000,\"startTimestamp\":165360000}]},{\"sentence\":\"号【】英文方括号[]中文大括号{}英文大括号{}中文点 英文\",\"words\":[{\"word\":\"号【】\",\"endTimestamp\":168780000,\"startTimestamp\":166780000},{\"word\":\"英\",\"endTimestamp\":175780000,\"startTimestamp\":174700000},{\"word\":\"文\",\"endTimestamp\":177200000,\"startTimestamp\":175780000},{\"word\":\"方\",\"endTimestamp\":178950000,\"startTimestamp\":177200000},{\"word\":\"括\",\"endTimestamp\":180200000,\"startTimestamp\":178950000},{\"word\":\"号[]\",\"endTimestamp\":182120000,\"startTimestamp\":180200000},{\"word\":\"中\",\"endTimestamp\":183960000,\"startTimestamp\":182120000},{\"word\":\"文\",\"endTimestamp\":185710000,\"startTimestamp\":183960000},{\"word\":\"大\",\"endTimestamp\":187380000,\"startTimestamp\":185710000},{\"word\":\"括\",\"endTimestamp\":188720000,\"startTimestamp\":187380000},{\"word\":\"号{}\",\"endTimestamp\":190810000,\"startTimestamp\":188720000},{\"word\":\"英\",\"endTimestamp\":192310000,\"startTimestamp\":190810000},{\"word\":\"文\",\"endTimestamp\":194060000,\"startTimestamp\":192310000},{\"word\":\"大\",\"endTimestamp\":195650000,\"startTimestamp\":194060000},{\"word\":\"括\",\"endTimestamp\":197070000,\"startTimestamp\":195650000},{\"word\":\"号{}\",\"endTimestamp\":199150000,\"startTimestamp\":197070000},{\"word\":\"中\",\"endTimestamp\":201400000,\"startTimestamp\":199150000},{\"word\":\"文\",\"endTimestamp\":202900000,\"startTimestamp\":201400000},{\"word\":\"点\",\"endTimestamp\":205320000,\"startTimestamp\":202900000},{\"word\":\" 英\",\"endTimestamp\":206650000,\"startTimestamp\":205320000},{\"word\":\"文\",\"endTimestamp\":208400000,\"startTimestamp\":206650000}]},{\"sentence\":\"点`中文感叹号！\",\"words\":[{\"word\":\"点\",\"endTimestamp\":210060000,\"startTimestamp\":208400000},{\"word\":\"\",\"endTimestamp\":212140000,\"startTimestamp\":210060000},{\"word\":\"\",\"endTimestamp\":213140000,\"startTimestamp\":212140000},{\"word\":\"`\",\"endTimestamp\":215220000,\"startTimestamp\":213140000},{\"word\":\"中\",\"endTimestamp\":217220000,\"startTimestamp\":215220000},{\"word\":\"文\",\"endTimestamp\":218640000,\"startTimestamp\":217220000},{\"word\":\"感\",\"endTimestamp\":220310000,\"startTimestamp\":218640000},{\"word\":\"叹\",\"endTimestamp\":221810000,\"startTimestamp\":220310000},{\"word\":\"号！\",\"endTimestamp\":223810000,\"startTimestamp\":221810000}]},{\"sentence\":\"英文感叹号!\",\"words\":[{\"word\":\"英\",\"endTimestamp\":230730000,\"startTimestamp\":229730000},{\"word\":\"文\",\"endTimestamp\":232150000,\"startTimestamp\":230730000},{\"word\":\"感\",\"endTimestamp\":233740000,\"startTimestamp\":232150000},{\"word\":\"叹\",\"endTimestamp\":235660000,\"startTimestamp\":233740000},{\"word\":\"号!\",\"endTimestamp\":237830000,\"startTimestamp\":235660000}]},{\"sentence\":\"中文艾特符号@英文艾特符号@中文井号#英文井号#中文美元￥英\",\"words\":[{\"word\":\"中\",\"endTimestamp\":245250000,\"startTimestamp\":243580000},{\"word\":\"文\",\"endTimestamp\":247170000,\"startTimestamp\":245250000},{\"word\":\"艾\",\"endTimestamp\":248840000,\"startTimestamp\":247170000},{\"word\":\"特\",\"endTimestamp\":250170000,\"startTimestamp\":248840000},{\"word\":\"符\",\"endTimestamp\":251920000,\"startTimestamp\":250170000},{\"word\":\"号\",\"endTimestamp\":253580000,\"startTimestamp\":251920000},{\"word\":\"@\",\"endTimestamp\":257080000,\"startTimestamp\":253580000},{\"word\":\"英\",\"endTimestamp\":258500000,\"startTimestamp\":257080000},{\"word\":\"文\",\"endTimestamp\":260080000,\"startTimestamp\":258500000},{\"word\":\"艾\",\"endTimestamp\":261580000,\"startTimestamp\":260080000},{\"word\":\"特\",\"endTimestamp\":262910000,\"startTimestamp\":261580000},{\"word\":\"符\",\"endTimestamp\":264660000,\"startTimestamp\":262910000},{\"word\":\"号\",\"endTimestamp\":266570000,\"startTimestamp\":264660000},{\"word\":\"@\",\"endTimestamp\":269660000,\"startTimestamp\":267490000},{\"word\":\"中\",\"endTimestamp\":271580000,\"startTimestamp\":269660000},{\"word\":\"文\",\"endTimestamp\":273080000,\"startTimestamp\":271580000},{\"word\":\"井\",\"endTimestamp\":275000000,\"startTimestamp\":273080000},{\"word\":\"号\",\"endTimestamp\":277170000,\"startTimestamp\":275000000},{\"word\":\"\",\"endTimestamp\":279010000,\"startTimestamp\":277170000},{\"word\":\"#\",\"endTimestamp\":281010000,\"startTimestamp\":279010000},{\"word\":\"英\",\"endTimestamp\":282590000,\"startTimestamp\":281010000},{\"word\":\"文\",\"endTimestamp\":284340000,\"startTimestamp\":282590000},{\"word\":\"井\",\"endTimestamp\":286090000,\"startTimestamp\":284340000},{\"word\":\"号\",\"endTimestamp\":288340000,\"startTimestamp\":286090000},{\"word\":\"\",\"endTimestamp\":289920000,\"startTimestamp\":288340000},{\"word\":\"#\",\"endTimestamp\":291830000,\"startTimestamp\":289920000},{\"word\":\"中\",\"endTimestamp\":293580000,\"startTimestamp\":291830000},{\"word\":\"文\",\"endTimestamp\":294660000,\"startTimestamp\":293580000},{\"word\":\"美\",\"endTimestamp\":296500000,\"startTimestamp\":294660000},{\"word\":\"元\",\"endTimestamp\":298670000,\"startTimestamp\":296500000},{\"word\":\"￥英\",\"endTimestamp\":300090000,\"startTimestamp\":298670000}]},{\"sentence\":\"文美元$中文百分号%英文百分号%中文上箭头……\",\"words\":[{\"word\":\"文\",\"endTimestamp\":301590000,\"startTimestamp\":300090000},{\"word\":\"美\",\"endTimestamp\":303180000,\"startTimestamp\":301590000},{\"word\":\"元\",\"endTimestamp\":304510000,\"startTimestamp\":303180000},{\"word\":\"$\",\"endTimestamp\":308010000,\"startTimestamp\":304510000},{\"word\":\"中\",\"endTimestamp\":309760000,\"startTimestamp\":308010000},{\"word\":\"文\",\"endTimestamp\":311340000,\"startTimestamp\":309760000},{\"word\":\"百\",\"endTimestamp\":313420000,\"startTimestamp\":311340000},{\"word\":\"分\",\"endTimestamp\":314830000,\"startTimestamp\":313420000},{\"word\":\"号\",\"endTimestamp\":316740000,\"startTimestamp\":314830000},{\"word\":\"\",\"endTimestamp\":318490000,\"startTimestamp\":316740000},{\"word\":\"\",\"endTimestamp\":319910000,\"startTimestamp\":318490000},{\"word\":\"%\",\"endTimestamp\":322330000,\"startTimestamp\":319910000},{\"word\":\"英\",\"endTimestamp\":324000000,\"startTimestamp\":322330000},{\"word\":\"文\",\"endTimestamp\":325500000,\"startTimestamp\":324000000},{\"word\":\"百\",\"endTimestamp\":327500000,\"startTimestamp\":325500000},{\"word\":\"分\",\"endTimestamp\":329000000,\"startTimestamp\":327500000},{\"word\":\"号\",\"endTimestamp\":330830000,\"startTimestamp\":329000000},{\"word\":\"\",\"endTimestamp\":332660000,\"startTimestamp\":330830000},{\"word\":\"\",\"endTimestamp\":334160000,\"startTimestamp\":332660000},{\"word\":\"%\",\"endTimestamp\":335910000,\"startTimestamp\":334160000},{\"word\":\"中\",\"endTimestamp\":337830000,\"startTimestamp\":335910000},{\"word\":\"文\",\"endTimestamp\":339080000,\"startTimestamp\":337830000},{\"word\":\"上\",\"endTimestamp\":341240000,\"startTimestamp\":339080000},{\"word\":\"箭\",\"endTimestamp\":342910000,\"startTimestamp\":341240000},{\"word\":\"头……\",\"endTimestamp\":344910000,\"startTimestamp\":342910000}]},{\"sentence\":\"英文上箭头^中文and符号&英文and符号&中文星号*英文星\",\"words\":[{\"word\":\"英\",\"endTimestamp\":352080000,\"startTimestamp\":350910000},{\"word\":\"文\",\"endTimestamp\":353000000,\"startTimestamp\":352080000},{\"word\":\"上\",\"endTimestamp\":355090000,\"startTimestamp\":353000000},{\"word\":\"箭\",\"endTimestamp\":356840000,\"startTimestamp\":355090000},{\"word\":\"头\",\"endTimestamp\":358840000,\"startTimestamp\":356840000},{\"word\":\"\",\"endTimestamp\":360750000,\"startTimestamp\":358840000},{\"word\":\"\",\"endTimestamp\":362500000,\"startTimestamp\":360750000},{\"word\":\"^\",\"endTimestamp\":364080000,\"startTimestamp\":362500000},{\"word\":\"中\",\"endTimestamp\":366420000,\"startTimestamp\":364080000},{\"word\":\"文\",\"endTimestamp\":368090000,\"startTimestamp\":366420000},{\"word\":\"and\",\"endTimestamp\":370000000,\"startTimestamp\":368090000},{\"word\":\"符\",\"endTimestamp\":371670000,\"startTimestamp\":370000000},{\"word\":\"号\",\"endTimestamp\":373260000,\"startTimestamp\":371670000},{\"word\":\"&\",\"endTimestamp\":375100000,\"startTimestamp\":373260000},{\"word\":\"英\",\"endTimestamp\":376520000,\"startTimestamp\":375100000},{\"word\":\"文\",\"endTimestamp\":378440000,\"startTimestamp\":376520000},{\"word\":\"and\",\"endTimestamp\":380940000,\"startTimestamp\":378440000},{\"word\":\"符\",\"endTimestamp\":382690000,\"startTimestamp\":380940000},{\"word\":\"号\",\"endTimestamp\":384440000,\"startTimestamp\":382690000},{\"word\":\"&\",\"endTimestamp\":387860000,\"startTimestamp\":386110000},{\"word\":\"中\",\"endTimestamp\":389610000,\"startTimestamp\":387860000},{\"word\":\"文\",\"endTimestamp\":391280000,\"startTimestamp\":389610000},{\"word\":\"星\",\"endTimestamp\":393530000,\"startTimestamp\":391280000},{\"word\":\"号\",\"endTimestamp\":395450000,\"startTimestamp\":393530000},{\"word\":\"\",\"endTimestamp\":397280000,\"startTimestamp\":395450000},{\"word\":\"*\",\"endTimestamp\":398780000,\"startTimestamp\":397280000},{\"word\":\"英\",\"endTimestamp\":400030000,\"startTimestamp\":398780000},{\"word\":\"文\",\"endTimestamp\":401700000,\"startTimestamp\":400030000},{\"word\":\"星\",\"endTimestamp\":403870000,\"startTimestamp\":401700000}]},{\"sentence\":\"号*中文括号（）英文括号()中文减号-英文减号-中��等号=英\",\"words\":[{\"word\":\"号\",\"endTimestamp\":405790000,\"startTimestamp\":403870000},{\"word\":\"\",\"endTimestamp\":407450000,\"startTimestamp\":405790000},{\"word\":\"*\",\"endTimestamp\":408620000,\"startTimestamp\":407450000},{\"word\":\"中\",\"endTimestamp\":410450000,\"startTimestamp\":408620000},{\"word\":\"文\",\"endTimestamp\":411620000,\"startTimestamp\":410450000},{\"word\":\"括\",\"endTimestamp\":413200000,\"startTimestamp\":411620000},{\"word\":\"号（）\",\"endTimestamp\":415030000,\"startTimestamp\":413200000},{\"word\":\"英\",\"endTimestamp\":422110000,\"startTimestamp\":421030000},{\"word\":\"文\",\"endTimestamp\":423530000,\"startTimestamp\":422110000},{\"word\":\"括\",\"endTimestamp\":425190000,\"startTimestamp\":423530000},{\"word\":\"号()\",\"endTimestamp\":427280000,\"startTimestamp\":425190000},{\"word\":\"中\",\"endTimestamp\":429370000,\"startTimestamp\":427280000},{\"word\":\"文\",\"endTimestamp\":431200000,\"startTimestamp\":429370000},{\"word\":\"减\",\"endTimestamp\":432950000,\"startTimestamp\":431200000},{\"word\":\"号-\",\"endTimestamp\":435280000,\"startTimestamp\":432950000},{\"word\":\"英\",\"endTimestamp\":437780000,\"startTimestamp\":436530000},{\"word\":\"文\",\"endTimestamp\":439530000,\"startTimestamp\":437780000},{\"word\":\"减\",\"endTimestamp\":441280000,\"startTimestamp\":439530000},{\"word\":\"号-\",\"endTimestamp\":443530000,\"startTimestamp\":441280000},{\"word\":\"中\",\"endTimestamp\":446030000,\"startTimestamp\":444610000},{\"word\":\"文\",\"endTimestamp\":447530000,\"startTimestamp\":446030000},{\"word\":\"等\",\"endTimestamp\":448950000,\"startTimestamp\":447530000},{\"word\":\"号\",\"endTimestamp\":450530000,\"startTimestamp\":448950000},{\"word\":\"\",\"endTimestamp\":452450000,\"startTimestamp\":450530000},{\"word\":\"=\",\"endTimestamp\":454200000,\"startTimestamp\":452450000},{\"word\":\"英\",\"endTimestamp\":455620000,\"startTimestamp\":454200000}]},{\"sentence\":\"文等号=中文加号+英文加号+中文长横线——英文长横线_\",\"words\":[{\"word\":\"文\",\"endTimestamp\":457370000,\"startTimestamp\":455620000},{\"word\":\"等\",\"endTimestamp\":459120000,\"startTimestamp\":457370000},{\"word\":\"号\",\"endTimestamp\":461040000,\"startTimestamp\":459120000},{\"word\":\"\",\"endTimestamp\":462870000,\"startTimestamp\":461040000},{\"word\":\"=\",\"endTimestamp\":464040000,\"startTimestamp\":462870000},{\"word\":\"中\",\"endTimestamp\":466120000,\"startTimestamp\":464040000},{\"word\":\"文\",\"endTimestamp\":467620000,\"startTimestamp\":466120000},{\"word\":\"加\",\"endTimestamp\":469450000,\"startTimestamp\":467620000},{\"word\":\"号\",\"endTimestamp\":470950000,\"startTimestamp\":469450000},{\"word\":\"+\",\"endTimestamp\":473040000,\"startTimestamp\":470950000},{\"word\":\"英\",\"endTimestamp\":479960000,\"startTimestamp\":478960000},{\"word\":\"文\",\"endTimestamp\":481290000,\"startTimestamp\":479960000},{\"word\":\"加\",\"endTimestamp\":482960000,\"startTimestamp\":481290000},{\"word\":\"号\",\"endTimestamp\":485050000,\"startTimestamp\":482960000},{\"word\":\"+\",\"endTimestamp\":486960000,\"startTimestamp\":485050000},{\"word\":\"中\",\"endTimestamp\":488960000,\"startTimestamp\":486960000},{\"word\":\"文\",\"endTimestamp\":490130000,\"startTimestamp\":488960000},{\"word\":\"长\",\"endTimestamp\":492300000,\"startTimestamp\":490130000},{\"word\":\"横\",\"endTimestamp\":493880000,\"startTimestamp\":492300000},{\"word\":\"线——\",\"endTimestamp\":496210000,\"startTimestamp\":493880000},{\"word\":\"英\",\"endTimestamp\":499290000,\"startTimestamp\":498040000},{\"word\":\"文\",\"endTimestamp\":500960000,\"startTimestamp\":499290000},{\"word\":\"长\",\"endTimestamp\":503040000,\"startTimestamp\":500960000},{\"word\":\"横\",\"endTimestamp\":504620000,\"startTimestamp\":503040000},{\"word\":\"线\",\"endTimestamp\":506290000,\"startTimestamp\":504620000},{\"word\":\"\",\"endTimestamp\":507790000,\"startTimestamp\":506290000},{\"word\":\"\",\"endTimestamp\":509290000,\"startTimestamp\":507790000},{\"word\":\"_\",\"endTimestamp\":511290000,\"startTimestamp\":509290000}]}]";

        //腾讯云调用这个方法
        List<IvhSentences> textTimestampResult = JSONObject
                .parseObject(jsonStr, new TypeReference<List<IvhSentences>>() {
                });
        List<SentencesDTO> ttsResultSentnecesList = IvhDigitalServiceImpl.cnvTextTimestampResult(textTimestampResult);

        System.out.println(JSONUtil.toJsonStr(subtitleManager.segmentor(str)));
        System.out.println(JSONUtil.toJsonStr(subtitleManager.nlpSegmentor(str)));

        System.out.println("====分词处理字幕的结果====");
        System.out
                .println(JSONUtil.toJsonStr(subtitleManager.genSubtitlesBySegmentor(str, 20, ttsResultSentnecesList)));

        System.out.println("====================分界线====================");
        System.out.println("====================分界线====================");

        //todo:case2.1-火山云:
        String str2 = "2023年2月15日，医保局发布重大通知，推动定点零售药店纳入门诊统筹管理。至8月，已有25个省份的14.14万家药店开通此项服务，累计结算人次高达1.74亿。这一政策不仅提升了药品的可及性和便利性，还有效减轻了患者的经济负担，同时为药店带来了客流和收入的双重增长。";
        String jsonStr2 = "{\"phonemes\":[{\"end_time\":25,\"phone\":\"\",\"start_time\":0},{\"end_time\":30,\"phone\":\"C0_\",\"start_time\":25},{\"end_time\":175,\"phone\":\"C0er\",\"start_time\":30},{\"end_time\":230,\"phone\":\"C0l\",\"start_time\":175},{\"end_time\":295,\"phone\":\"C0ing\",\"start_time\":230},{\"end_time\":300,\"phone\":\"C0_\",\"start_time\":295},{\"end_time\":435,\"phone\":\"C0er\",\"start_time\":300},{\"end_time\":510,\"phone\":\"C0s\",\"start_time\":435},{\"end_time\":590,\"phone\":\"C0an\",\"start_time\":510},{\"end_time\":665,\"phone\":\"C0n\",\"start_time\":590},{\"end_time\":805,\"phone\":\"C0ian\",\"start_time\":665},{\"end_time\":810,\"phone\":\"C0_\",\"start_time\":805},{\"end_time\":995,\"phone\":\"C0er\",\"start_time\":810},{\"end_time\":1000,\"phone\":\"C0yu\",\"start_time\":995},{\"end_time\":1090,\"phone\":\"C0ve\",\"start_time\":1000},{\"end_time\":1210,\"phone\":\"C0sh\",\"start_time\":1090},{\"end_time\":1270,\"phone\":\"C0iii\",\"start_time\":1210},{\"end_time\":1275,\"phone\":\"C0w\",\"start_time\":1270},{\"end_time\":1370,\"phone\":\"C0u\",\"start_time\":1275},{\"end_time\":1445,\"phone\":\"C0r\",\"start_time\":1370},{\"end_time\":1595,\"phone\":\"C0iii\",\"start_time\":1445},{\"end_time\":1820,\"phone\":\"\",\"start_time\":1595},{\"end_time\":1825,\"phone\":\"C0y\",\"start_time\":1820},{\"end_time\":1930,\"phone\":\"C0i\",\"start_time\":1825},{\"end_time\":1985,\"phone\":\"C0b\",\"start_time\":1930},{\"end_time\":2115,\"phone\":\"C0ao\",\"start_time\":1985},{\"end_time\":2200,\"phone\":\"C0j\",\"start_time\":2115},{\"end_time\":2280,\"phone\":\"C0v\",\"start_time\":2200},{\"end_time\":2355,\"phone\":\"C0f\",\"start_time\":2280},{\"end_time\":2475,\"phone\":\"C0a\",\"start_time\":2355},{\"end_time\":2550,\"phone\":\"C0b\",\"start_time\":2475},{\"end_time\":2635,\"phone\":\"C0u\",\"start_time\":2550},{\"end_time\":2720,\"phone\":\"C0zh\",\"start_time\":2635},{\"end_time\":2830,\"phone\":\"C0ong\",\"start_time\":2720},{\"end_time\":2875,\"phone\":\"C0d\",\"start_time\":2830},{\"end_time\":2965,\"phone\":\"C0a\",\"start_time\":2875},{\"end_time\":3060,\"phone\":\"C0t\",\"start_time\":2965},{\"end_time\":3195,\"phone\":\"C0ong\",\"start_time\":3060},{\"end_time\":3265,\"phone\":\"C0zh\",\"start_time\":3195},{\"end_time\":3410,\"phone\":\"C0iii\",\"start_time\":3265},{\"end_time\":3630,\"phone\":\"\",\"start_time\":3410},{\"end_time\":3685,\"phone\":\"C0t\",\"start_time\":3630},{\"end_time\":3785,\"phone\":\"C0uei\",\"start_time\":3685},{\"end_time\":3840,\"phone\":\"C0d\",\"start_time\":3785},{\"end_time\":3980,\"phone\":\"C0ong\",\"start_time\":3840},{\"end_time\":4035,\"phone\":\"C0d\",\"start_time\":3980},{\"end_time\":4175,\"phone\":\"C0ing\",\"start_time\":4035},{\"end_time\":4225,\"phone\":\"C0d\",\"start_time\":4175},{\"end_time\":4325,\"phone\":\"C0ian\",\"start_time\":4225},{\"end_time\":4405,\"phone\":\"C0l\",\"start_time\":4325},{\"end_time\":4530,\"phone\":\"C0ing\",\"start_time\":4405},{\"end_time\":4605,\"phone\":\"C0sh\",\"start_time\":4530},{\"end_time\":4690,\"phone\":\"C0ou\",\"start_time\":4605},{\"end_time\":4695,\"phone\":\"C0y\",\"start_time\":4690},{\"end_time\":4850,\"phone\":\"C0iao\",\"start_time\":4695},{\"end_time\":4905,\"phone\":\"C0d\",\"start_time\":4850},{\"end_time\":5045,\"phone\":\"C0ian\",\"start_time\":4905},{\"end_time\":5160,\"phone\":\"C0n\",\"start_time\":5045},{\"end_time\":5300,\"phone\":\"C0a\",\"start_time\":5160},{\"end_time\":5385,\"phone\":\"C0r\",\"start_time\":5300},{\"end_time\":5475,\"phone\":\"C0u\",\"start_time\":5385},{\"end_time\":5530,\"phone\":\"C0m\",\"start_time\":5475},{\"end_time\":5655,\"phone\":\"C0en\",\"start_time\":5530},{\"end_time\":5715,\"phone\":\"C0zh\",\"start_time\":5655},{\"end_time\":5820,\"phone\":\"C0en\",\"start_time\":5715},{\"end_time\":5910,\"phone\":\"C0t\",\"start_time\":5820},{\"end_time\":6005,\"phone\":\"C0ong\",\"start_time\":5910},{\"end_time\":6070,\"phone\":\"C0ch\",\"start_time\":6005},{\"end_time\":6140,\"phone\":\"C0ou\",\"start_time\":6070},{\"end_time\":6205,\"phone\":\"C0g\",\"start_time\":6140},{\"end_time\":6345,\"phone\":\"C0uan\",\"start_time\":6205},{\"end_time\":6405,\"phone\":\"C0l\",\"start_time\":6345},{\"end_time\":6530,\"phone\":\"C0i\",\"start_time\":6405},{\"end_time\":6750,\"phone\":\"\",\"start_time\":6530},{\"end_time\":6830,\"phone\":\"C0zh\",\"start_time\":6750},{\"end_time\":6885,\"phone\":\"C0iii\",\"start_time\":6830},{\"end_time\":6945,\"phone\":\"C0b\",\"start_time\":6885},{\"end_time\":7105,\"phone\":\"C0a\",\"start_time\":6945},{\"end_time\":7110,\"phone\":\"C0yu\",\"start_time\":7105},{\"end_time\":7295,\"phone\":\"C0ve\",\"start_time\":7110},{\"end_time\":7550,\"phone\":\"\",\"start_time\":7295},{\"end_time\":7555,\"phone\":\"C0y\",\"start_time\":7550},{\"end_time\":7650,\"phone\":\"C0i\",\"start_time\":7555},{\"end_time\":7655,\"phone\":\"C0y\",\"start_time\":7650},{\"end_time\":7790,\"phone\":\"C0iou\",\"start_time\":7655},{\"end_time\":7795,\"phone\":\"C0_\",\"start_time\":7790},{\"end_time\":7945,\"phone\":\"C0er\",\"start_time\":7795},{\"end_time\":8035,\"phone\":\"C0sh\",\"start_time\":7945},{\"end_time\":8085,\"phone\":\"C0iii\",\"start_time\":8035},{\"end_time\":8090,\"phone\":\"C0w\",\"start_time\":8085},{\"end_time\":8190,\"phone\":\"C0u\",\"start_time\":8090},{\"end_time\":8240,\"phone\":\"C0g\",\"start_time\":8190},{\"end_time\":8290,\"phone\":\"C0e\",\"start_time\":8240},{\"end_time\":8390,\"phone\":\"C0sh\",\"start_time\":8290},{\"end_time\":8490,\"phone\":\"C0eng\",\"start_time\":8390},{\"end_time\":8560,\"phone\":\"C0f\",\"start_time\":8490},{\"end_time\":8645,\"phone\":\"C0en\",\"start_time\":8560},{\"end_time\":8685,\"phone\":\"C0d\",\"start_time\":8645},{\"end_time\":8745,\"phone\":\"C0e\",\"start_time\":8685},{\"end_time\":8850,\"phone\":\"C0sh\",\"start_time\":8745},{\"end_time\":8890,\"phone\":\"C0iii\",\"start_time\":8850},{\"end_time\":8975,\"phone\":\"C0s\",\"start_time\":8890},{\"end_time\":9020,\"phone\":\"C0ii\",\"start_time\":8975},{\"end_time\":9075,\"phone\":\"C0d\",\"start_time\":9020},{\"end_time\":9235,\"phone\":\"C0ian\",\"start_time\":9075},{\"end_time\":9240,\"phone\":\"C0y\",\"start_time\":9235},{\"end_time\":9330,\"phone\":\"C0i\",\"start_time\":9240},{\"end_time\":9425,\"phone\":\"C0s\",\"start_time\":9330},{\"end_time\":9485,\"phone\":\"C0ii\",\"start_time\":9425},{\"end_time\":9490,\"phone\":\"C0w\",\"start_time\":9485},{\"end_time\":9635,\"phone\":\"C0uan\",\"start_time\":9490},{\"end_time\":9695,\"phone\":\"C0j\",\"start_time\":9635},{\"end_time\":9800,\"phone\":\"C0ia\",\"start_time\":9695},{\"end_time\":9805,\"phone\":\"C0y\",\"start_time\":9800},{\"end_time\":9960,\"phone\":\"C0iao\",\"start_time\":9805},{\"end_time\":10015,\"phone\":\"C0d\",\"start_time\":9960},{\"end_time\":10165,\"phone\":\"C0ian\",\"start_time\":10015},{\"end_time\":10270,\"phone\":\"C0k\",\"start_time\":10165},{\"end_time\":10370,\"phone\":\"C0ai\",\"start_time\":10270},{\"end_time\":10430,\"phone\":\"C0t\",\"start_time\":10370},{\"end_time\":10535,\"phone\":\"C0ong\",\"start_time\":10430},{\"end_time\":10630,\"phone\":\"C0c\",\"start_time\":10535},{\"end_time\":10675,\"phone\":\"C0ii\",\"start_time\":10630},{\"end_time\":10760,\"phone\":\"C0x\",\"start_time\":10675},{\"end_time\":10840,\"phone\":\"C0iang\",\"start_time\":10760},{\"end_time\":10920,\"phone\":\"C0f\",\"start_time\":10840},{\"end_time\":11040,\"phone\":\"C0u\",\"start_time\":10920},{\"end_time\":11045,\"phone\":\"C0w\",\"start_time\":11040},{\"end_time\":11235,\"phone\":\"C0u\",\"start_time\":11045},{\"end_time\":11455,\"phone\":\"\",\"start_time\":11235},{\"end_time\":11515,\"phone\":\"C0l\",\"start_time\":11455},{\"end_time\":11645,\"phone\":\"C0ei\",\"start_time\":11515},{\"end_time\":11725,\"phone\":\"C0j\",\"start_time\":11645},{\"end_time\":11800,\"phone\":\"C0i\",\"start_time\":11725},{\"end_time\":11895,\"phone\":\"C0j\",\"start_time\":11800},{\"end_time\":11980,\"phone\":\"C0ie\",\"start_time\":11895},{\"end_time\":12060,\"phone\":\"C0s\",\"start_time\":11980},{\"end_time\":12130,\"phone\":\"C0uan\",\"start_time\":12060},{\"end_time\":12195,\"phone\":\"C0r\",\"start_time\":12130},{\"end_time\":12315,\"phone\":\"C0en\",\"start_time\":12195},{\"end_time\":12420,\"phone\":\"C0c\",\"start_time\":12315},{\"end_time\":12540,\"phone\":\"C0ii\",\"start_time\":12420},{\"end_time\":12635,\"phone\":\"C0g\",\"start_time\":12540},{\"end_time\":12760,\"phone\":\"C0ao\",\"start_time\":12635},{\"end_time\":12810,\"phone\":\"C0d\",\"start_time\":12760},{\"end_time\":12930,\"phone\":\"C0a\",\"start_time\":12810},{\"end_time\":12935,\"phone\":\"C0y\",\"start_time\":12930},{\"end_time\":13040,\"phone\":\"C0i\",\"start_time\":12935},{\"end_time\":13090,\"phone\":\"C0d\",\"start_time\":13040},{\"end_time\":13210,\"phone\":\"C0ian\",\"start_time\":13090},{\"end_time\":13300,\"phone\":\"C0q\",\"start_time\":13210},{\"end_time\":13350,\"phone\":\"C0i\",\"start_time\":13300},{\"end_time\":13440,\"phone\":\"C0s\",\"start_time\":13350},{\"end_time\":13535,\"phone\":\"C0ii\",\"start_time\":13440},{\"end_time\":13540,\"phone\":\"C0y\",\"start_time\":13535},{\"end_time\":13745,\"phone\":\"C0i\",\"start_time\":13540},{\"end_time\":13960,\"phone\":\"\",\"start_time\":13745},{\"end_time\":14035,\"phone\":\"C0zh\",\"start_time\":13960},{\"end_time\":14100,\"phone\":\"C0e\",\"start_time\":14035},{\"end_time\":14105,\"phone\":\"C0y\",\"start_time\":14100},{\"end_time\":14180,\"phone\":\"C0i\",\"start_time\":14105},{\"end_time\":14265,\"phone\":\"C0zh\",\"start_time\":14180},{\"end_time\":14395,\"phone\":\"C0eng\",\"start_time\":14265},{\"end_time\":14470,\"phone\":\"C0c\",\"start_time\":14395},{\"end_time\":14600,\"phone\":\"C0e\",\"start_time\":14470},{\"end_time\":14720,\"phone\":\"C0b\",\"start_time\":14600},{\"end_time\":14820,\"phone\":\"C0u\",\"start_time\":14720},{\"end_time\":14900,\"phone\":\"C0j\",\"start_time\":14820},{\"end_time\":15005,\"phone\":\"C0in\",\"start_time\":14900},{\"end_time\":15100,\"phone\":\"C0t\",\"start_time\":15005},{\"end_time\":15165,\"phone\":\"C0i\",\"start_time\":15100},{\"end_time\":15250,\"phone\":\"C0sh\",\"start_time\":15165},{\"end_time\":15305,\"phone\":\"C0eng\",\"start_time\":15250},{\"end_time\":15345,\"phone\":\"C0l\",\"start_time\":15305},{\"end_time\":15440,\"phone\":\"C0e\",\"start_time\":15345},{\"end_time\":15445,\"phone\":\"C0y\",\"start_time\":15440},{\"end_time\":15590,\"phone\":\"C0iao\",\"start_time\":15445},{\"end_time\":15685,\"phone\":\"C0p\",\"start_time\":15590},{\"end_time\":15790,\"phone\":\"C0in\",\"start_time\":15685},{\"end_time\":15830,\"phone\":\"C0d\",\"start_time\":15790},{\"end_time\":15900,\"phone\":\"C0e\",\"start_time\":15830},{\"end_time\":16010,\"phone\":\"C0k\",\"start_time\":15900},{\"end_time\":16095,\"phone\":\"C0e\",\"start_time\":16010},{\"end_time\":16180,\"phone\":\"C0j\",\"start_time\":16095},{\"end_time\":16255,\"phone\":\"C0i\",\"start_time\":16180},{\"end_time\":16350,\"phone\":\"C0x\",\"start_time\":16255},{\"end_time\":16465,\"phone\":\"C0ing\",\"start_time\":16350},{\"end_time\":16590,\"phone\":\"C0h\",\"start_time\":16465},{\"end_time\":16650,\"phone\":\"C0e\",\"start_time\":16590},{\"end_time\":16720,\"phone\":\"C0b\",\"start_time\":16650},{\"end_time\":16810,\"phone\":\"C0ian\",\"start_time\":16720},{\"end_time\":16860,\"phone\":\"C0l\",\"start_time\":16810},{\"end_time\":16935,\"phone\":\"C0i\",\"start_time\":16860},{\"end_time\":17040,\"phone\":\"C0x\",\"start_time\":16935},{\"end_time\":17150,\"phone\":\"C0ing\",\"start_time\":17040},{\"end_time\":17330,\"phone\":\"\",\"start_time\":17150},{\"end_time\":17410,\"phone\":\"C0h\",\"start_time\":17330},{\"end_time\":17505,\"phone\":\"C0ai\",\"start_time\":17410},{\"end_time\":17510,\"phone\":\"C0y\",\"start_time\":17505},{\"end_time\":17680,\"phone\":\"C0iou\",\"start_time\":17510},{\"end_time\":17755,\"phone\":\"C0x\",\"start_time\":17680},{\"end_time\":17880,\"phone\":\"C0iao\",\"start_time\":17755},{\"end_time\":17965,\"phone\":\"C0j\",\"start_time\":17880},{\"end_time\":18085,\"phone\":\"C0ian\",\"start_time\":17965},{\"end_time\":18170,\"phone\":\"C0q\",\"start_time\":18085},{\"end_time\":18230,\"phone\":\"C0ing\",\"start_time\":18170},{\"end_time\":18275,\"phone\":\"C0l\",\"start_time\":18230},{\"end_time\":18330,\"phone\":\"C0e\",\"start_time\":18275},{\"end_time\":18415,\"phone\":\"C0h\",\"start_time\":18330},{\"end_time\":18555,\"phone\":\"C0uan\",\"start_time\":18415},{\"end_time\":18620,\"phone\":\"C0zh\",\"start_time\":18555},{\"end_time\":18685,\"phone\":\"C0e\",\"start_time\":18620},{\"end_time\":18730,\"phone\":\"C0d\",\"start_time\":18685},{\"end_time\":18785,\"phone\":\"C0e\",\"start_time\":18730},{\"end_time\":18870,\"phone\":\"C0j\",\"start_time\":18785},{\"end_time\":18980,\"phone\":\"C0ing\",\"start_time\":18870},{\"end_time\":19045,\"phone\":\"C0j\",\"start_time\":18980},{\"end_time\":19085,\"phone\":\"C0i\",\"start_time\":19045},{\"end_time\":19185,\"phone\":\"C0f\",\"start_time\":19085},{\"end_time\":19260,\"phone\":\"C0u\",\"start_time\":19185},{\"end_time\":19310,\"phone\":\"C0d\",\"start_time\":19260},{\"end_time\":19455,\"phone\":\"C0an\",\"start_time\":19310},{\"end_time\":19690,\"phone\":\"\",\"start_time\":19455},{\"end_time\":19745,\"phone\":\"C0t\",\"start_time\":19690},{\"end_time\":19880,\"phone\":\"C0ong\",\"start_time\":19745},{\"end_time\":19985,\"phone\":\"C0sh\",\"start_time\":19880},{\"end_time\":20140,\"phone\":\"C0iii\",\"start_time\":19985},{\"end_time\":20145,\"phone\":\"C0w\",\"start_time\":20140},{\"end_time\":20310,\"phone\":\"C0uei\",\"start_time\":20145},{\"end_time\":20315,\"phone\":\"C0y\",\"start_time\":20310},{\"end_time\":20480,\"phone\":\"C0iao\",\"start_time\":20315},{\"end_time\":20535,\"phone\":\"C0d\",\"start_time\":20480},{\"end_time\":20670,\"phone\":\"C0ian\",\"start_time\":20535},{\"end_time\":20730,\"phone\":\"C0d\",\"start_time\":20670},{\"end_time\":20845,\"phone\":\"C0ai\",\"start_time\":20730},{\"end_time\":20885,\"phone\":\"C0l\",\"start_time\":20845},{\"end_time\":20945,\"phone\":\"C0ai\",\"start_time\":20885},{\"end_time\":20985,\"phone\":\"C0l\",\"start_time\":20945},{\"end_time\":21045,\"phone\":\"C0e\",\"start_time\":20985},{\"end_time\":21160,\"phone\":\"C0k\",\"start_time\":21045},{\"end_time\":21240,\"phone\":\"C0e\",\"start_time\":21160},{\"end_time\":21300,\"phone\":\"C0l\",\"start_time\":21240},{\"end_time\":21470,\"phone\":\"C0iou\",\"start_time\":21300},{\"end_time\":21595,\"phone\":\"C0h\",\"start_time\":21470},{\"end_time\":21710,\"phone\":\"C0e\",\"start_time\":21595},{\"end_time\":21820,\"phone\":\"C0sh\",\"start_time\":21710},{\"end_time\":21935,\"phone\":\"C0ou\",\"start_time\":21820},{\"end_time\":21995,\"phone\":\"C0r\",\"start_time\":21935},{\"end_time\":22085,\"phone\":\"C0u\",\"start_time\":21995},{\"end_time\":22130,\"phone\":\"C0d\",\"start_time\":22085},{\"end_time\":22210,\"phone\":\"C0e\",\"start_time\":22130},{\"end_time\":22295,\"phone\":\"C0sh\",\"start_time\":22210},{\"end_time\":22410,\"phone\":\"C0uang\",\"start_time\":22295},{\"end_time\":22470,\"phone\":\"C0ch\",\"start_time\":22410},{\"end_time\":22560,\"phone\":\"C0ong\",\"start_time\":22470},{\"end_time\":22625,\"phone\":\"C0z\",\"start_time\":22560},{\"end_time\":22735,\"phone\":\"C0eng\",\"start_time\":22625},{\"end_time\":22795,\"phone\":\"C0zh\",\"start_time\":22735},{\"end_time\":22935,\"phone\":\"C0ang\",\"start_time\":22795},{\"end_time\":23105,\"phone\":\"。\",\"start_time\":22935}],\"ssml_json\":null,\"words\":[{\"end_time\":590,\"start_time\":0,\"word\":\"2023\"},{\"end_time\":805,\"start_time\":590,\"word\":\"年\"},{\"end_time\":995,\"start_time\":805,\"word\":\"2\"},{\"end_time\":1090,\"start_time\":995,\"word\":\"月\"},{\"end_time\":1370,\"start_time\":1090,\"word\":\"15\"},{\"end_time\":1595,\"start_time\":1370,\"word\":\"日\"},{\"end_time\":1820,\"start_time\":1595,\"word\":\"，\"},{\"end_time\":1930,\"start_time\":1820,\"word\":\"医\"},{\"end_time\":2115,\"start_time\":1930,\"word\":\"保\"},{\"end_time\":2280,\"start_time\":2115,\"word\":\"局\"},{\"end_time\":2475,\"start_time\":2280,\"word\":\"发\"},{\"end_time\":2635,\"start_time\":2475,\"word\":\"布\"},{\"end_time\":2830,\"start_time\":2635,\"word\":\"重\"},{\"end_time\":2965,\"start_time\":2830,\"word\":\"大\"},{\"end_time\":3195,\"start_time\":2965,\"word\":\"通\"},{\"end_time\":3410,\"start_time\":3195,\"word\":\"知\"},{\"end_time\":3630,\"start_time\":3410,\"word\":\"，\"},{\"end_time\":3785,\"start_time\":3630,\"word\":\"推\"},{\"end_time\":3980,\"start_time\":3785,\"word\":\"动\"},{\"end_time\":4175,\"start_time\":3980,\"word\":\"定\"},{\"end_time\":4325,\"start_time\":4175,\"word\":\"点\"},{\"end_time\":4530,\"start_time\":4325,\"word\":\"零\"},{\"end_time\":4690,\"start_time\":4530,\"word\":\"售\"},{\"end_time\":4850,\"start_time\":4690,\"word\":\"药\"},{\"end_time\":5045,\"start_time\":4850,\"word\":\"店\"},{\"end_time\":5300,\"start_time\":5045,\"word\":\"纳\"},{\"end_time\":5475,\"start_time\":5300,\"word\":\"入\"},{\"end_time\":5655,\"start_time\":5475,\"word\":\"门\"},{\"end_time\":5820,\"start_time\":5655,\"word\":\"诊\"},{\"end_time\":6005,\"start_time\":5820,\"word\":\"统\"},{\"end_time\":6140,\"start_time\":6005,\"word\":\"筹\"},{\"end_time\":6345,\"start_time\":6140,\"word\":\"管\"},{\"end_time\":6530,\"start_time\":6345,\"word\":\"理\"},{\"end_time\":6750,\"start_time\":6530,\"word\":\"。\"},{\"end_time\":6885,\"start_time\":6750,\"word\":\"至\"},{\"end_time\":7105,\"start_time\":6885,\"word\":\"8\"},{\"end_time\":7295,\"start_time\":7105,\"word\":\"月\"},{\"end_time\":7550,\"start_time\":7295,\"word\":\"，\"},{\"end_time\":7650,\"start_time\":7550,\"word\":\"已\"},{\"end_time\":7790,\"start_time\":7650,\"word\":\"有\"},{\"end_time\":8190,\"start_time\":7790,\"word\":\"25\"},{\"end_time\":8290,\"start_time\":8190,\"word\":\"个\"},{\"end_time\":8490,\"start_time\":8290,\"word\":\"省\"},{\"end_time\":8645,\"start_time\":8490,\"word\":\"份\"},{\"end_time\":8745,\"start_time\":8645,\"word\":\"的\"},{\"end_time\":8993,\"start_time\":8745,\"word\":\"14\"},{\"end_time\":9239,\"start_time\":8993,\"word\":\".\"},{\"end_time\":9485,\"start_time\":9239,\"word\":\"14\"},{\"end_time\":9635,\"start_time\":9485,\"word\":\"万\"},{\"end_time\":9800,\"start_time\":9635,\"word\":\"家\"},{\"end_time\":9960,\"start_time\":9800,\"word\":\"药\"},{\"end_time\":10165,\"start_time\":9960,\"word\":\"店\"},{\"end_time\":10370,\"start_time\":10165,\"word\":\"开\"},{\"end_time\":10535,\"start_time\":10370,\"word\":\"通\"},{\"end_time\":10675,\"start_time\":10535,\"word\":\"此\"},{\"end_time\":10840,\"start_time\":10675,\"word\":\"项\"},{\"end_time\":11040,\"start_time\":10840,\"word\":\"服\"},{\"end_time\":11235,\"start_time\":11040,\"word\":\"务\"},{\"end_time\":11455,\"start_time\":11235,\"word\":\"，\"},{\"end_time\":11645,\"start_time\":11455,\"word\":\"累\"},{\"end_time\":11800,\"start_time\":11645,\"word\":\"计\"},{\"end_time\":11980,\"start_time\":11800,\"word\":\"结\"},{\"end_time\":12130,\"start_time\":11980,\"word\":\"算\"},{\"end_time\":12315,\"start_time\":12130,\"word\":\"人\"},{\"end_time\":12540,\"start_time\":12315,\"word\":\"次\"},{\"end_time\":12760,\"start_time\":12540,\"word\":\"高\"},{\"end_time\":12930,\"start_time\":12760,\"word\":\"达\"},{\"end_time\":13133,\"start_time\":12930,\"word\":\"1\"},{\"end_time\":13334,\"start_time\":13133,\"word\":\".\"},{\"end_time\":13535,\"start_time\":13334,\"word\":\"74\"},{\"end_time\":13745,\"start_time\":13535,\"word\":\"亿\"},{\"end_time\":13960,\"start_time\":13745,\"word\":\"。\"},{\"end_time\":14100,\"start_time\":13960,\"word\":\"这\"},{\"end_time\":14180,\"start_time\":14100,\"word\":\"一\"},{\"end_time\":14395,\"start_time\":14180,\"word\":\"政\"},{\"end_time\":14600,\"start_time\":14395,\"word\":\"策\"},{\"end_time\":14820,\"start_time\":14600,\"word\":\"不\"},{\"end_time\":15005,\"start_time\":14820,\"word\":\"仅\"},{\"end_time\":15165,\"start_time\":15005,\"word\":\"提\"},{\"end_time\":15305,\"start_time\":15165,\"word\":\"升\"},{\"end_time\":15440,\"start_time\":15305,\"word\":\"了\"},{\"end_time\":15590,\"start_time\":15440,\"word\":\"药\"},{\"end_time\":15790,\"start_time\":15590,\"word\":\"品\"},{\"end_time\":15900,\"start_time\":15790,\"word\":\"的\"},{\"end_time\":16095,\"start_time\":15900,\"word\":\"可\"},{\"end_time\":16255,\"start_time\":16095,\"word\":\"及\"},{\"end_time\":16465,\"start_time\":16255,\"word\":\"性\"},{\"end_time\":16650,\"start_time\":16465,\"word\":\"和\"},{\"end_time\":16810,\"start_time\":16650,\"word\":\"便\"},{\"end_time\":16935,\"start_time\":16810,\"word\":\"利\"},{\"end_time\":17150,\"start_time\":16935,\"word\":\"性\"},{\"end_time\":17330,\"start_time\":17150,\"word\":\"，\"},{\"end_time\":17505,\"start_time\":17330,\"word\":\"还\"},{\"end_time\":17680,\"start_time\":17505,\"word\":\"有\"},{\"end_time\":17880,\"start_time\":17680,\"word\":\"效\"},{\"end_time\":18085,\"start_time\":17880,\"word\":\"减\"},{\"end_time\":18230,\"start_time\":18085,\"word\":\"轻\"},{\"end_time\":18330,\"start_time\":18230,\"word\":\"了\"},{\"end_time\":18555,\"start_time\":18330,\"word\":\"患\"},{\"end_time\":18685,\"start_time\":18555,\"word\":\"者\"},{\"end_time\":18785,\"start_time\":18685,\"word\":\"的\"},{\"end_time\":18980,\"start_time\":18785,\"word\":\"经\"},{\"end_time\":19085,\"start_time\":18980,\"word\":\"济\"},{\"end_time\":19260,\"start_time\":19085,\"word\":\"负\"},{\"end_time\":19455,\"start_time\":19260,\"word\":\"担\"},{\"end_time\":19690,\"start_time\":19455,\"word\":\"，\"},{\"end_time\":19880,\"start_time\":19690,\"word\":\"同\"},{\"end_time\":20140,\"start_time\":19880,\"word\":\"时\"},{\"end_time\":20310,\"start_time\":20140,\"word\":\"为\"},{\"end_time\":20480,\"start_time\":20310,\"word\":\"药\"},{\"end_time\":20670,\"start_time\":20480,\"word\":\"店\"},{\"end_time\":20845,\"start_time\":20670,\"word\":\"带\"},{\"end_time\":20945,\"start_time\":20845,\"word\":\"来\"},{\"end_time\":21045,\"start_time\":20945,\"word\":\"了\"},{\"end_time\":21240,\"start_time\":21045,\"word\":\"客\"},{\"end_time\":21470,\"start_time\":21240,\"word\":\"流\"},{\"end_time\":21710,\"start_time\":21470,\"word\":\"和\"},{\"end_time\":21935,\"start_time\":21710,\"word\":\"收\"},{\"end_time\":22085,\"start_time\":21935,\"word\":\"入\"},{\"end_time\":22210,\"start_time\":22085,\"word\":\"的\"},{\"end_time\":22410,\"start_time\":22210,\"word\":\"双\"},{\"end_time\":22560,\"start_time\":22410,\"word\":\"重\"},{\"end_time\":22735,\"start_time\":22560,\"word\":\"增\"},{\"end_time\":22935,\"start_time\":22735,\"word\":\"长\"},{\"end_time\":23105,\"start_time\":22935,\"word\":\"。\"}]}";
        //todo:case2.2-火山云-英语:
        //String str2 = "Success is not final,failure is not fatal,it's the courage to continue that counts.Courage doesn't always roar.Sometimes courage is the quiet voice at the end of the day saying,I will try again tomorrow.";
        //String jsonStr2 = "{\"phonemes\":[{\"end_time\":35,\"phone\":\"\",\"start_time\":0},{\"end_time\":125,\"phone\":\"E0s\",\"start_time\":35},{\"end_time\":170,\"phone\":\"E0ah\",\"start_time\":125},{\"end_time\":300,\"phone\":\"E0k\",\"start_time\":170},{\"end_time\":395,\"phone\":\"E0s\",\"start_time\":300},{\"end_time\":540,\"phone\":\"E0eh\",\"start_time\":395},{\"end_time\":640,\"phone\":\"E0s\",\"start_time\":540},{\"end_time\":775,\"phone\":\"E0ih\",\"start_time\":640},{\"end_time\":865,\"phone\":\"E0z\",\"start_time\":775},{\"end_time\":930,\"phone\":\"E0n\",\"start_time\":865},{\"end_time\":1060,\"phone\":\"E0aa\",\"start_time\":930},{\"end_time\":1150,\"phone\":\"E0t\",\"start_time\":1060},{\"end_time\":1250,\"phone\":\"E0f\",\"start_time\":1150},{\"end_time\":1380,\"phone\":\"E0ay\",\"start_time\":1250},{\"end_time\":1450,\"phone\":\"E0n\",\"start_time\":1380},{\"end_time\":1515,\"phone\":\"E0ah\",\"start_time\":1450},{\"end_time\":1645,\"phone\":\"E0l\",\"start_time\":1515},{\"end_time\":1840,\"phone\":\"\",\"start_time\":1645},{\"end_time\":1885,\"phone\":\"E0f\",\"start_time\":1840},{\"end_time\":1980,\"phone\":\"E0ey\",\"start_time\":1885},{\"end_time\":2060,\"phone\":\"E0l\",\"start_time\":1980},{\"end_time\":2140,\"phone\":\"E0y\",\"start_time\":2060},{\"end_time\":2275,\"phone\":\"E0er\",\"start_time\":2140},{\"end_time\":2410,\"phone\":\"E0ih\",\"start_time\":2275},{\"end_time\":2500,\"phone\":\"E0z\",\"start_time\":2410},{\"end_time\":2565,\"phone\":\"E0n\",\"start_time\":2500},{\"end_time\":2705,\"phone\":\"E0aa\",\"start_time\":2565},{\"end_time\":2790,\"phone\":\"E0t\",\"start_time\":2705},{\"end_time\":2870,\"phone\":\"E0f\",\"start_time\":2790},{\"end_time\":2960,\"phone\":\"E0ey\",\"start_time\":2870},{\"end_time\":3040,\"phone\":\"E0t\",\"start_time\":2960},{\"end_time\":3080,\"phone\":\"E0ah\",\"start_time\":3040},{\"end_time\":3245,\"phone\":\"E0l\",\"start_time\":3080},{\"end_time\":3470,\"phone\":\"\",\"start_time\":3245},{\"end_time\":3530,\"phone\":\"E0ih\",\"start_time\":3470},{\"end_time\":3590,\"phone\":\"E0t\",\"start_time\":3530},{\"end_time\":3695,\"phone\":\"E0s\",\"start_time\":3590},{\"end_time\":3750,\"phone\":\"E0dh\",\"start_time\":3695},{\"end_time\":3825,\"phone\":\"E0ah\",\"start_time\":3750},{\"end_time\":3935,\"phone\":\"E0k\",\"start_time\":3825},{\"end_time\":4065,\"phone\":\"E0er\",\"start_time\":3935},{\"end_time\":4130,\"phone\":\"E0ih\",\"start_time\":4065},{\"end_time\":4255,\"phone\":\"E0jh\",\"start_time\":4130},{\"end_time\":4340,\"phone\":\"E0t\",\"start_time\":4255},{\"end_time\":4395,\"phone\":\"E0uw\",\"start_time\":4340},{\"end_time\":4480,\"phone\":\"E0k\",\"start_time\":4395},{\"end_time\":4525,\"phone\":\"E0ah\",\"start_time\":4480},{\"end_time\":4590,\"phone\":\"E0n\",\"start_time\":4525},{\"end_time\":4680,\"phone\":\"E0t\",\"start_time\":4590},{\"end_time\":4745,\"phone\":\"E0ih\",\"start_time\":4680},{\"end_time\":4815,\"phone\":\"E0n\",\"start_time\":4745},{\"end_time\":4890,\"phone\":\"E0y\",\"start_time\":4815},{\"end_time\":5015,\"phone\":\"E0uw\",\"start_time\":4890},{\"end_time\":5060,\"phone\":\"E0dh\",\"start_time\":5015},{\"end_time\":5210,\"phone\":\"E0ae\",\"start_time\":5060},{\"end_time\":5325,\"phone\":\"E0t\",\"start_time\":5210},{\"end_time\":5450,\"phone\":\"E0k\",\"start_time\":5325},{\"end_time\":5600,\"phone\":\"E0aw\",\"start_time\":5450},{\"end_time\":5650,\"phone\":\"E0n\",\"start_time\":5600},{\"end_time\":5720,\"phone\":\"E0t\",\"start_time\":5650},{\"end_time\":5830,\"phone\":\"E0s\",\"start_time\":5720},{\"end_time\":6055,\"phone\":\"\",\"start_time\":5830},{\"end_time\":6135,\"phone\":\"E0k\",\"start_time\":6055},{\"end_time\":6265,\"phone\":\"E0er\",\"start_time\":6135},{\"end_time\":6340,\"phone\":\"E0ih\",\"start_time\":6265},{\"end_time\":6455,\"phone\":\"E0jh\",\"start_time\":6340},{\"end_time\":6525,\"phone\":\"E0d\",\"start_time\":6455},{\"end_time\":6635,\"phone\":\"E0ah\",\"start_time\":6525},{\"end_time\":6715,\"phone\":\"E0z\",\"start_time\":6635},{\"end_time\":6755,\"phone\":\"E0ah\",\"start_time\":6715},{\"end_time\":6820,\"phone\":\"E0n\",\"start_time\":6755},{\"end_time\":6915,\"phone\":\"E0t\",\"start_time\":6820},{\"end_time\":7065,\"phone\":\"E0ao\",\"start_time\":6915},{\"end_time\":7125,\"phone\":\"E0l\",\"start_time\":7065},{\"end_time\":7210,\"phone\":\"E0w\",\"start_time\":7125},{\"end_time\":7310,\"phone\":\"E0ey\",\"start_time\":7210},{\"end_time\":7405,\"phone\":\"E0z\",\"start_time\":7310},{\"end_time\":7495,\"phone\":\"E0r\",\"start_time\":7405},{\"end_time\":7620,\"phone\":\"E0ao\",\"start_time\":7495},{\"end_time\":7695,\"phone\":\"E0r\",\"start_time\":7620},{\"end_time\":7860,\"phone\":\"\",\"start_time\":7695},{\"end_time\":7940,\"phone\":\"E0s\",\"start_time\":7860},{\"end_time\":7980,\"phone\":\"E0ah\",\"start_time\":7940},{\"end_time\":8050,\"phone\":\"E0m\",\"start_time\":7980},{\"end_time\":8135,\"phone\":\"E0t\",\"start_time\":8050},{\"end_time\":8285,\"phone\":\"E0ay\",\"start_time\":8135},{\"end_time\":8355,\"phone\":\"E0m\",\"start_time\":8285},{\"end_time\":8435,\"phone\":\"E0z\",\"start_time\":8355},{\"end_time\":8550,\"phone\":\"E0k\",\"start_time\":8435},{\"end_time\":8695,\"phone\":\"E0er\",\"start_time\":8550},{\"end_time\":8795,\"phone\":\"E0ih\",\"start_time\":8695},{\"end_time\":8925,\"phone\":\"E0jh\",\"start_time\":8795},{\"end_time\":9030,\"phone\":\"E0ih\",\"start_time\":8925},{\"end_time\":9120,\"phone\":\"E0z\",\"start_time\":9030},{\"end_time\":9180,\"phone\":\"E0dh\",\"start_time\":9120},{\"end_time\":9290,\"phone\":\"E0ah\",\"start_time\":9180},{\"end_time\":9410,\"phone\":\"E0k\",\"start_time\":9290},{\"end_time\":9475,\"phone\":\"E0w\",\"start_time\":9410},{\"end_time\":9585,\"phone\":\"E0ay\",\"start_time\":9475},{\"end_time\":9640,\"phone\":\"E0ah\",\"start_time\":9585},{\"end_time\":9775,\"phone\":\"E0t\",\"start_time\":9640},{\"end_time\":9820,\"phone\":\"E0v\",\"start_time\":9775},{\"end_time\":10005,\"phone\":\"E0oy\",\"start_time\":9820},{\"end_time\":10110,\"phone\":\"E0s\",\"start_time\":10005},{\"end_time\":10220,\"phone\":\"E0ae\",\"start_time\":10110},{\"end_time\":10380,\"phone\":\"E0t\",\"start_time\":10220},{\"end_time\":10445,\"phone\":\"E0dh\",\"start_time\":10380},{\"end_time\":10625,\"phone\":\"E0iy\",\"start_time\":10445},{\"end_time\":10760,\"phone\":\"E0eh\",\"start_time\":10625},{\"end_time\":10830,\"phone\":\"E0n\",\"start_time\":10760},{\"end_time\":10870,\"phone\":\"E0d\",\"start_time\":10830},{\"end_time\":10965,\"phone\":\"E0ah\",\"start_time\":10870},{\"end_time\":11035,\"phone\":\"E0v\",\"start_time\":10965},{\"end_time\":11090,\"phone\":\"E0dh\",\"start_time\":11035},{\"end_time\":11220,\"phone\":\"E0ah\",\"start_time\":11090},{\"end_time\":11285,\"phone\":\"E0d\",\"start_time\":11220},{\"end_time\":11440,\"phone\":\"E0ey\",\"start_time\":11285},{\"end_time\":11555,\"phone\":\"E0s\",\"start_time\":11440},{\"end_time\":11710,\"phone\":\"E0ey\",\"start_time\":11555},{\"end_time\":11775,\"phone\":\"E0ih\",\"start_time\":11710},{\"end_time\":11910,\"phone\":\"E0ng\",\"start_time\":11775},{\"end_time\":12070,\"phone\":\"\",\"start_time\":11910},{\"end_time\":12240,\"phone\":\"E0ay\",\"start_time\":12070},{\"end_time\":12325,\"phone\":\"E0w\",\"start_time\":12240},{\"end_time\":12375,\"phone\":\"E0ih\",\"start_time\":12325},{\"end_time\":12525,\"phone\":\"E0l\",\"start_time\":12375},{\"end_time\":12615,\"phone\":\"E0t\",\"start_time\":12525},{\"end_time\":12680,\"phone\":\"E0r\",\"start_time\":12615},{\"end_time\":12835,\"phone\":\"E0ay\",\"start_time\":12680},{\"end_time\":12925,\"phone\":\"E0ah\",\"start_time\":12835},{\"end_time\":13000,\"phone\":\"E0g\",\"start_time\":12925},{\"end_time\":13125,\"phone\":\"E0eh\",\"start_time\":13000},{\"end_time\":13215,\"phone\":\"E0n\",\"start_time\":13125},{\"end_time\":13300,\"phone\":\"E0t\",\"start_time\":13215},{\"end_time\":13360,\"phone\":\"E0ah\",\"start_time\":13300},{\"end_time\":13425,\"phone\":\"E0m\",\"start_time\":13360},{\"end_time\":13560,\"phone\":\"E0aa\",\"start_time\":13425},{\"end_time\":13640,\"phone\":\"E0r\",\"start_time\":13560},{\"end_time\":13830,\"phone\":\"E0ow\",\"start_time\":13640},{\"end_time\":14000,\"phone\":\"。\",\"start_time\":13830}],\"ssml_json\":null,\"words\":[{\"end_time\":640,\"start_time\":0,\"word\":\"Success\"},{\"end_time\":865,\"start_time\":640,\"word\":\"is\"},{\"end_time\":1150,\"start_time\":865,\"word\":\"not\"},{\"end_time\":1645,\"start_time\":1150,\"word\":\"final\"},{\"end_time\":1840,\"start_time\":1645,\"word\":\",\"},{\"end_time\":2275,\"start_time\":1840,\"word\":\"failure\"},{\"end_time\":2500,\"start_time\":2275,\"word\":\"is\"},{\"end_time\":2790,\"start_time\":2500,\"word\":\"not\"},{\"end_time\":3245,\"start_time\":2790,\"word\":\"fatal\"},{\"end_time\":3470,\"start_time\":3245,\"word\":\",\"},{\"end_time\":3695,\"start_time\":3470,\"word\":\"it's\"},{\"end_time\":3825,\"start_time\":3695,\"word\":\"the\"},{\"end_time\":4255,\"start_time\":3825,\"word\":\"courage\"},{\"end_time\":4395,\"start_time\":4255,\"word\":\"to\"},{\"end_time\":5015,\"start_time\":4395,\"word\":\"continue\"},{\"end_time\":5325,\"start_time\":5015,\"word\":\"that\"},{\"end_time\":5830,\"start_time\":5325,\"word\":\"counts\"},{\"end_time\":6055,\"start_time\":5830,\"word\":\".\"},{\"end_time\":6455,\"start_time\":6055,\"word\":\"Courage\"},{\"end_time\":6915,\"start_time\":6455,\"word\":\"doesn't\"},{\"end_time\":7405,\"start_time\":6915,\"word\":\"always\"},{\"end_time\":7695,\"start_time\":7405,\"word\":\"roar\"},{\"end_time\":7860,\"start_time\":7695,\"word\":\".\"},{\"end_time\":8435,\"start_time\":7860,\"word\":\"Sometimes\"},{\"end_time\":8925,\"start_time\":8435,\"word\":\"courage\"},{\"end_time\":9120,\"start_time\":8925,\"word\":\"is\"},{\"end_time\":9290,\"start_time\":9120,\"word\":\"the\"},{\"end_time\":9775,\"start_time\":9290,\"word\":\"quiet\"},{\"end_time\":10110,\"start_time\":9775,\"word\":\"voice\"},{\"end_time\":10380,\"start_time\":10110,\"word\":\"at\"},{\"end_time\":10625,\"start_time\":10380,\"word\":\"the\"},{\"end_time\":10870,\"start_time\":10625,\"word\":\"end\"},{\"end_time\":11035,\"start_time\":10870,\"word\":\"of\"},{\"end_time\":11220,\"start_time\":11035,\"word\":\"the\"},{\"end_time\":11440,\"start_time\":11220,\"word\":\"day\"},{\"end_time\":11910,\"start_time\":11440,\"word\":\"saying\"},{\"end_time\":12070,\"start_time\":11910,\"word\":\",\"},{\"end_time\":12240,\"start_time\":12070,\"word\":\"I\"},{\"end_time\":12525,\"start_time\":12240,\"word\":\"will\"},{\"end_time\":12835,\"start_time\":12525,\"word\":\"try\"},{\"end_time\":13215,\"start_time\":12835,\"word\":\"again\"},{\"end_time\":13830,\"start_time\":13215,\"word\":\"tomorrow\"},{\"end_time\":14000,\"start_time\":13830,\"word\":\".\"}]}";
        //todo:case2.3-火山云-英语:
        //String str2 = "Obama called thecurrent downturn \"the worst financial crisis since the GreatDepression\" and said the government must assure that the financial rescuepackage signed into law worked as planned.";
        //String jsonStr2 = "{\"phonemes\":[{\"end_time\":10,\"phone\":\"\",\"start_time\":0},{\"end_time\":125,\"phone\":\"E0ow\",\"start_time\":10},{\"end_time\":180,\"phone\":\"E0b\",\"start_time\":125},{\"end_time\":290,\"phone\":\"E0aa\",\"start_time\":180},{\"end_time\":355,\"phone\":\"E0m\",\"start_time\":290},{\"end_time\":400,\"phone\":\"E0ah\",\"start_time\":355},{\"end_time\":525,\"phone\":\"E0k\",\"start_time\":400},{\"end_time\":590,\"phone\":\"E0ao\",\"start_time\":525},{\"end_time\":625,\"phone\":\"E0l\",\"start_time\":590},{\"end_time\":675,\"phone\":\"E0d\",\"start_time\":625},{\"end_time\":705,\"phone\":\"E0dh\",\"start_time\":675},{\"end_time\":735,\"phone\":\"E0ih\",\"start_time\":705},{\"end_time\":845,\"phone\":\"E0k\",\"start_time\":735},{\"end_time\":945,\"phone\":\"E0er\",\"start_time\":845},{\"end_time\":990,\"phone\":\"E0ah\",\"start_time\":945},{\"end_time\":1035,\"phone\":\"E0n\",\"start_time\":990},{\"end_time\":1065,\"phone\":\"E0t\",\"start_time\":1035},{\"end_time\":1115,\"phone\":\"E0d\",\"start_time\":1065},{\"end_time\":1220,\"phone\":\"E0aw\",\"start_time\":1115},{\"end_time\":1250,\"phone\":\"E0n\",\"start_time\":1220},{\"end_time\":1335,\"phone\":\"E0t\",\"start_time\":1250},{\"end_time\":1405,\"phone\":\"E0er\",\"start_time\":1335},{\"end_time\":1470,\"phone\":\"E0n\",\"start_time\":1405},{\"end_time\":1485,\"phone\":\"E0dh\",\"start_time\":1470},{\"end_time\":1525,\"phone\":\"E0ah\",\"start_time\":1485},{\"end_time\":1620,\"phone\":\"E0w\",\"start_time\":1525},{\"end_time\":1690,\"phone\":\"E0er\",\"start_time\":1620},{\"end_time\":1755,\"phone\":\"E0s\",\"start_time\":1690},{\"end_time\":1800,\"phone\":\"E0t\",\"start_time\":1755},{\"end_time\":1885,\"phone\":\"E0f\",\"start_time\":1800},{\"end_time\":1925,\"phone\":\"E0ah\",\"start_time\":1885},{\"end_time\":1980,\"phone\":\"E0n\",\"start_time\":1925},{\"end_time\":2080,\"phone\":\"E0ae\",\"start_time\":1980},{\"end_time\":2115,\"phone\":\"E0n\",\"start_time\":2080},{\"end_time\":2195,\"phone\":\"E0sh\",\"start_time\":2115},{\"end_time\":2235,\"phone\":\"E0ah\",\"start_time\":2195},{\"end_time\":2280,\"phone\":\"E0l\",\"start_time\":2235},{\"end_time\":2405,\"phone\":\"E0k\",\"start_time\":2280},{\"end_time\":2425,\"phone\":\"E0r\",\"start_time\":2405},{\"end_time\":2525,\"phone\":\"E0ay\",\"start_time\":2425},{\"end_time\":2625,\"phone\":\"E0s\",\"start_time\":2525},{\"end_time\":2655,\"phone\":\"E0ah\",\"start_time\":2625},{\"end_time\":2720,\"phone\":\"E0s\",\"start_time\":2655},{\"end_time\":2785,\"phone\":\"E0s\",\"start_time\":2720},{\"end_time\":2820,\"phone\":\"E0ih\",\"start_time\":2785},{\"end_time\":2875,\"phone\":\"E0n\",\"start_time\":2820},{\"end_time\":2955,\"phone\":\"E0s\",\"start_time\":2875},{\"end_time\":3000,\"phone\":\"E0dh\",\"start_time\":2955},{\"end_time\":3035,\"phone\":\"E0ah\",\"start_time\":3000},{\"end_time\":3140,\"phone\":\"E0g\",\"start_time\":3035},{\"end_time\":3180,\"phone\":\"E0r\",\"start_time\":3140},{\"end_time\":3265,\"phone\":\"E0ey\",\"start_time\":3180},{\"end_time\":3305,\"phone\":\"E0t\",\"start_time\":3265},{\"end_time\":3360,\"phone\":\"E0d\",\"start_time\":3305},{\"end_time\":3395,\"phone\":\"E0ih\",\"start_time\":3360},{\"end_time\":3530,\"phone\":\"E0p\",\"start_time\":3395},{\"end_time\":3555,\"phone\":\"E0r\",\"start_time\":3530},{\"end_time\":3610,\"phone\":\"E0eh\",\"start_time\":3555},{\"end_time\":3735,\"phone\":\"E0sh\",\"start_time\":3610},{\"end_time\":3790,\"phone\":\"E0ah\",\"start_time\":3735},{\"end_time\":3865,\"phone\":\"E0n\",\"start_time\":3790},{\"end_time\":3925,\"phone\":\"E0ae\",\"start_time\":3865},{\"end_time\":3980,\"phone\":\"E0n\",\"start_time\":3925},{\"end_time\":3995,\"phone\":\"E0d\",\"start_time\":3980},{\"end_time\":4080,\"phone\":\"E0s\",\"start_time\":3995},{\"end_time\":4140,\"phone\":\"E0eh\",\"start_time\":4080},{\"end_time\":4205,\"phone\":\"E0d\",\"start_time\":4140},{\"end_time\":4230,\"phone\":\"E0dh\",\"start_time\":4205},{\"end_time\":4265,\"phone\":\"E0ah\",\"start_time\":4230},{\"end_time\":4350,\"phone\":\"E0g\",\"start_time\":4265},{\"end_time\":4420,\"phone\":\"E0ah\",\"start_time\":4350},{\"end_time\":4480,\"phone\":\"E0v\",\"start_time\":4420},{\"end_time\":4535,\"phone\":\"E0er\",\"start_time\":4480},{\"end_time\":4610,\"phone\":\"E0m\",\"start_time\":4535},{\"end_time\":4645,\"phone\":\"E0ah\",\"start_time\":4610},{\"end_time\":4675,\"phone\":\"E0n\",\"start_time\":4645},{\"end_time\":4705,\"phone\":\"E0t\",\"start_time\":4675},{\"end_time\":4760,\"phone\":\"E0m\",\"start_time\":4705},{\"end_time\":4820,\"phone\":\"E0ah\",\"start_time\":4760},{\"end_time\":4895,\"phone\":\"E0s\",\"start_time\":4820},{\"end_time\":4950,\"phone\":\"E0t\",\"start_time\":4895},{\"end_time\":4975,\"phone\":\"E0ah\",\"start_time\":4950},{\"end_time\":5120,\"phone\":\"E0sh\",\"start_time\":4975},{\"end_time\":5210,\"phone\":\"E0uh\",\"start_time\":5120},{\"end_time\":5275,\"phone\":\"E0r\",\"start_time\":5210},{\"end_time\":5315,\"phone\":\"E0dh\",\"start_time\":5275},{\"end_time\":5350,\"phone\":\"E0ae\",\"start_time\":5315},{\"end_time\":5400,\"phone\":\"E0t\",\"start_time\":5350},{\"end_time\":5430,\"phone\":\"E0dh\",\"start_time\":5400},{\"end_time\":5465,\"phone\":\"E0ah\",\"start_time\":5430},{\"end_time\":5570,\"phone\":\"E0f\",\"start_time\":5465},{\"end_time\":5605,\"phone\":\"E0ah\",\"start_time\":5570},{\"end_time\":5665,\"phone\":\"E0n\",\"start_time\":5605},{\"end_time\":5760,\"phone\":\"E0ae\",\"start_time\":5665},{\"end_time\":5795,\"phone\":\"E0n\",\"start_time\":5760},{\"end_time\":5885,\"phone\":\"E0sh\",\"start_time\":5795},{\"end_time\":5920,\"phone\":\"E0ah\",\"start_time\":5885},{\"end_time\":6005,\"phone\":\"E0l\",\"start_time\":5920},{\"end_time\":6055,\"phone\":\"E0r\",\"start_time\":6005},{\"end_time\":6100,\"phone\":\"E0eh\",\"start_time\":6055},{\"end_time\":6195,\"phone\":\"E0s\",\"start_time\":6100},{\"end_time\":6260,\"phone\":\"E0k\",\"start_time\":6195},{\"end_time\":6300,\"phone\":\"E0y\",\"start_time\":6260},{\"end_time\":6325,\"phone\":\"E0uw\",\"start_time\":6300},{\"end_time\":6435,\"phone\":\"E0p\",\"start_time\":6325},{\"end_time\":6550,\"phone\":\"E0ae\",\"start_time\":6435},{\"end_time\":6650,\"phone\":\"E0k\",\"start_time\":6550},{\"end_time\":6695,\"phone\":\"E0ih\",\"start_time\":6650},{\"end_time\":6790,\"phone\":\"E0jh\",\"start_time\":6695},{\"end_time\":6875,\"phone\":\"E0s\",\"start_time\":6790},{\"end_time\":7010,\"phone\":\"E0ay\",\"start_time\":6875},{\"end_time\":7050,\"phone\":\"E0n\",\"start_time\":7010},{\"end_time\":7075,\"phone\":\"E0d\",\"start_time\":7050},{\"end_time\":7120,\"phone\":\"E0ih\",\"start_time\":7075},{\"end_time\":7175,\"phone\":\"E0n\",\"start_time\":7120},{\"end_time\":7240,\"phone\":\"E0t\",\"start_time\":7175},{\"end_time\":7270,\"phone\":\"E0uw\",\"start_time\":7240},{\"end_time\":7390,\"phone\":\"E0l\",\"start_time\":7270},{\"end_time\":7525,\"phone\":\"E0ao\",\"start_time\":7390},{\"end_time\":7640,\"phone\":\"E0w\",\"start_time\":7525},{\"end_time\":7730,\"phone\":\"E0er\",\"start_time\":7640},{\"end_time\":7805,\"phone\":\"E0k\",\"start_time\":7730},{\"end_time\":7855,\"phone\":\"E0t\",\"start_time\":7805},{\"end_time\":7915,\"phone\":\"E0ae\",\"start_time\":7855},{\"end_time\":7995,\"phone\":\"E0z\",\"start_time\":7915},{\"end_time\":8115,\"phone\":\"E0p\",\"start_time\":7995},{\"end_time\":8155,\"phone\":\"E0l\",\"start_time\":8115},{\"end_time\":8300,\"phone\":\"E0ae\",\"start_time\":8155},{\"end_time\":8375,\"phone\":\"E0n\",\"start_time\":8300},{\"end_time\":8420,\"phone\":\"E0d\",\"start_time\":8375},{\"end_time\":8580,\"phone\":\"。\",\"start_time\":8420}],\"ssml_json\":null,\"words\":[{\"end_time\":400,\"start_time\":0,\"word\":\"Obama\"},{\"end_time\":675,\"start_time\":400,\"word\":\"called\"},{\"end_time\":1065,\"start_time\":675,\"word\":\"thecurrent\"},{\"end_time\":1470,\"start_time\":1065,\"word\":\"downturn\"},{\"end_time\":1470,\"start_time\":1470,\"word\":\"\\\"\"},{\"end_time\":1525,\"start_time\":1470,\"word\":\"the\"},{\"end_time\":1800,\"start_time\":1525,\"word\":\"worst\"},{\"end_time\":2280,\"start_time\":1800,\"word\":\"financial\"},{\"end_time\":2720,\"start_time\":2280,\"word\":\"crisis\"},{\"end_time\":2955,\"start_time\":2720,\"word\":\"since\"},{\"end_time\":3035,\"start_time\":2955,\"word\":\"the\"},{\"end_time\":3305,\"start_time\":3035,\"word\":\"Great\"},{\"end_time\":3865,\"start_time\":3305,\"word\":\"Depression\"},{\"end_time\":3865,\"start_time\":3865,\"word\":\"\\\"\"},{\"end_time\":3995,\"start_time\":3865,\"word\":\"and\"},{\"end_time\":4205,\"start_time\":3995,\"word\":\"said\"},{\"end_time\":4265,\"start_time\":4205,\"word\":\"the\"},{\"end_time\":4705,\"start_time\":4265,\"word\":\"government\"},{\"end_time\":4950,\"start_time\":4705,\"word\":\"must\"},{\"end_time\":5275,\"start_time\":4950,\"word\":\"assure\"},{\"end_time\":5400,\"start_time\":5275,\"word\":\"that\"},{\"end_time\":5465,\"start_time\":5400,\"word\":\"the\"},{\"end_time\":6005,\"start_time\":5465,\"word\":\"financial\"},{\"end_time\":6790,\"start_time\":6005,\"word\":\"rescuepackage\"},{\"end_time\":7075,\"start_time\":6790,\"word\":\"signed\"},{\"end_time\":7270,\"start_time\":7075,\"word\":\"into\"},{\"end_time\":7525,\"start_time\":7270,\"word\":\"law\"},{\"end_time\":7855,\"start_time\":7525,\"word\":\"worked\"},{\"end_time\":7995,\"start_time\":7855,\"word\":\"as\"},{\"end_time\":8420,\"start_time\":7995,\"word\":\"planned\"},{\"end_time\":8580,\"start_time\":8420,\"word\":\".\"}]}";

        //火山云调用这个方法
        List<SentencesDTO> ttsResultSentnecesList2 = VolcEngineHandlerManager.buildTtsResultSentnecesList(1L, jsonStr2);

        System.out.println("====分词处理字幕的结果====");
        System.out.println(
                JSONUtil.toJsonStr(subtitleManager.genSubtitlesBySegmentor(str2, 20, ttsResultSentnecesList2)));
    }

}
