package com.dl.aiservice.biz.digitaljobhandler;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.RedisUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.register.DigitalHelper;
import com.dl.aiservice.biz.service.digital.DigitalDBService;
import com.dl.aiservice.biz.service.digital.bo.CreateVideoUpdateBO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-11 16:45
 */
@Component
public abstract class AbstractDigitalManJobHandler implements DigitalManJobHandlerInterface {
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractDigitalManJobHandler.class);

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private DigitalHelper digitalHelper;
    @Resource
    private DigitalDBService digitalDBService;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;

    /**
     * 将数字人视频合成请求放入redis队列中
     *
     * @param requestDTO
     */
    public void pushToRedis(CreateRequestDTO requestDTO) {
        redisUtil.leftPush(getQueueKey(), requestDTO);
    }

    /**
     * 从redis队列中获取一个数字人视频合成请求
     */
    public CreateRequestDTO popFromRedis() {
        return redisUtil.rightPop(getQueueKey());
    }

    /**
     * 调用数字人厂商进行数字人视频合成
     *
     * @param createRequestDTO
     */
    protected void doDigitalManVideoCreate(CreateRequestDTO createRequestDTO) {
        try {
            LOGGER.info("数字人视频开始合成,mediaJobId:{},createRequestDTO:{}", createRequestDTO.getUpdateId(),
                    JSONUtil.toJsonStr(createRequestDTO));
            channelUtil.init("", createRequestDTO.getChannel());
            Date requestDt = new Date();
            // 调用第三方数字人合成接口
            CreateResponseDTO responseDTO = digitalHelper
                    .get(ServiceChannelEnum.getByCode(createRequestDTO.getChannel())).videoCreate(createRequestDTO);
            // 更新任务状态为合成中
            if (Objects.nonNull(responseDTO)) {
                CreateVideoUpdateBO createVideoUpdateBO = new CreateVideoUpdateBO();
                createVideoUpdateBO.setId(createRequestDTO.getUpdateId());
                createVideoUpdateBO.setTaskId(responseDTO.getTaskId());
                createVideoUpdateBO.setStatus(Const.ONE);
                createVideoUpdateBO.setRequestDt(requestDt);
                digitalDBService.updateVideoById(createVideoUpdateBO);
            }
        } catch (Exception e) {
            LOGGER.error("三方数字人视频合成异常!param={}", JsonUtils.toJSON(createRequestDTO), e);
            MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.getById(createRequestDTO.getUpdateId());
            if (Objects.equals(mediaProduceJobPO.getStatus(), Const.TWO)) {
                mediaProduceJobPO.setStatus(-Const.ONE);
                mediaProduceJobPO.setResponseDt(new Date());
                mediaProduceJobManager.updateById(mediaProduceJobPO);
            }
            DigitalVideoCallbackDTO digitalCallbackDTO = new DigitalVideoCallbackDTO();
            BeanUtils.copyProperties(mediaProduceJobPO, digitalCallbackDTO);
            // 失败处理，回调业务
            digitalHelper.get(ServiceChannelEnum.getByCode(createRequestDTO.getChannel()))
                    .callBackBiz(mediaProduceJobPO.getTenantCode(), mediaProduceJobPO.getWorksBizId(),
                            mediaProduceJobPO.getCallbackUrl(), digitalCallbackDTO,
                            JSONUtil.toJsonStr(digitalCallbackDTO));
        }
    }

}
