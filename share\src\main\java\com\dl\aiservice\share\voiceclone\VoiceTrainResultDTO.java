package com.dl.aiservice.share.voiceclone;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-06 16:35
 */
@Data
public class VoiceTrainResultDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 训练名
     */
    private String name;

    /**
     * 厂商训练模型编号
     */
    private String extModelCode;

    /**
     * 厂商，3-深声科技（线上训练），6-火山引擎
     */
    private Integer channel;

    /**
     * 所属用户
     */
    private Long userId;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 训练类型：0 数字人训练；1 声音训练
     *
     * @See:com.dl.aiservice.biz.manager.train.enums.TrainTypeEnum
     */
    private Integer trainType;

    /**
     * 试听链接
     */
    private String sampleLink;

    /**
     * 训练状态：1 训练中；0 训练完成；-1 训练失败
     */
    private Integer status;

    /**
     * 已训练次数。训练成功+训练中
     */
    private Integer trainedNum;

    /**
     * 最新的失败原因
     */
    private String latestFailReason;

}
