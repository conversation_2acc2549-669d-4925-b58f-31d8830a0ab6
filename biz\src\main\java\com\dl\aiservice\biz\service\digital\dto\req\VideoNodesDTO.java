package com.dl.aiservice.biz.service.digital.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@NoArgsConstructor
@Data
public class VideoNodesDTO implements Serializable {

    private static final long serialVersionUID = -2957453243061050600L;
    /**
     * 素材类型，'1'-视频素材 '2'-图片素材
     */
    @ApiModelProperty(value = "硅基非必填：素材类型，'1'-视频素材 '2'-图片素材")
    private String type;
    /**
     * 素材地址
     */
    @ApiModelProperty(value = "硅基非必填：素材地址")
    private String url;

    /**
     * 控制素材位置
     */
    @ApiModelProperty(value = "硅基非必填：控制素材位置")
    private VideoStyleDTO style;

}