package com.dl.aiservice.biz.client.guiji.enums;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;

public enum GJCallBackStatusEnum {

    SUCCESS(0, "success"),

    FAIL(-1, "fail");


    private final Integer code;
    private final String desc;

    GJCallBackStatusEnum(Integer errorCode, String errorDesc) {
        this.code = errorCode;
        this.desc = errorDesc;
    }

    public static GJCallBackStatusEnum getEnum(String desc) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getDesc(), desc)).findFirst().orElseThrow(() -> BusinessServiceException.getInstance("枚举异常"));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
