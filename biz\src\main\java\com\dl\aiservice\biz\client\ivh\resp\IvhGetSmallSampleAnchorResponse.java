package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class IvhGetSmallSampleAnchorResponse implements Serializable {

    private static final long serialVersionUID = 2814318770465207077L;

    @JsonProperty(value = "Virtualmans")
    private List<IvhSmallSampleVirtualMan> virtualMans;

    @JsonProperty(value = "Total")
    private Integer total;
}
