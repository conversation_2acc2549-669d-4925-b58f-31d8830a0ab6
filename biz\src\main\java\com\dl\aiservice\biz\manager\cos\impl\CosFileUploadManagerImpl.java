package com.dl.aiservice.biz.manager.cos.impl;

import cn.hutool.core.io.FileUtil;
import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.enums.SymbolE;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.properties.cos.CosProperties;
import com.dl.aiservice.biz.properties.cos.TencentCloudProperties;
import com.dl.framework.common.idg.HostTimeIdg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
/**
 * @ClassName CosFileUploadManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 17:58
 * @Version 1.0
 **/
@Slf4j
@Service
public class CosFileUploadManagerImpl implements CosFileUploadManager {

    private final static String FILE_DIR_TEMPLATE = "temp/visual/tts/";
    private final static String FILE_URL = "https://%s.cos.%s.myqcloud.com/%s";

    @Autowired
    private CosProperties cosProperties;

    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Override
    public String uploadFile(File file, String type, String path) {
        if (Objects.isNull(file)) {
            log.error("腾讯云文件上传，入参file对象为空，不处理！");
            return null;
        }
        String fileName = file.getName();
        //处理文件类型
        if (StringUtils.hasLength(type) && !StringUtils.hasLength(FileUtil.extName(fileName))) {
            fileName = fileName + SymbolE.DOT.getValue() + type;
        }
        if (StringUtils.hasLength(path)) {
            fileName = path + Const.SLASH + fileName;
        } else {
            fileName = FILE_DIR_TEMPLATE + fileName;
        }
        OSS ossClient = createCli();
        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(cosProperties.getBucketId(), fileName, file);
            // 创建PutObject请求。
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            return  "https://pelotavatar.oss-cn-shanghai.aliyuncs.com/" + fileName;
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
        } catch (Exception e) {
            log.error("阿里云上传异常", e);
        }  finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }



    private OSS createCli() {
        String endpoint = "https://oss-cn-shanghai.aliyuncs.com";

        // 填写Endpoint对应的Region信息，例如cn-hangzhou。
        String region = "cn-shanghai";
        // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
        DefaultCredentialProvider credentialsProvider = null;
        try {
            credentialsProvider = CredentialsProviderFactory.newDefaultCredentialProvider(cosProperties.getAccessKeyId(), cosProperties.getAccessKeySecret());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 创建OSSClient实例。
        // 当OSSClient实例不再使用时，调用shutdown方法以释放资源。
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        OSS ossClient = OSSClientBuilder.create()
                .endpoint(endpoint)
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(region)
                .build();
        return ossClient;
    }
}
