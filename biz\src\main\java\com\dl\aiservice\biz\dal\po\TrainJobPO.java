package com.dl.aiservice.biz.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.aiservice.biz.common.po.BasePO;
import lombok.Data;

/**
 * 训练任务表
 *
 * @TableName train_job
 */
@TableName(value = "train_job")
@Data
public class TrainJobPO extends BasePO {
    private static final long serialVersionUID = -6144237972929898748L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户代码
     */
    @TableField(value = "tenant_code")
    private String tenantCode;

    /**
     * 渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    @TableField(value = "channel")
    private Integer channel;

    /**
     * 训练类型：0 数字人训练；1 声音训练
     *
     * @See:com.dl.aiservice.biz.manager.train.enums.TrainTypeEnum
     */
    @TableField(value = "job_type")
    private Integer jobType;

    /**
     * 性别：1 男 ；2 女
     */
    @TableField(value = "gender")
    private Integer gender;

    /**
     * 训练id 雪花算法
     */
    @TableField(value = "train_job_id")
    private Long trainJobId;

    /**
     * 第三方训练id
     */
    @TableField(value = "ext_job_id")
    private String extJobId;

    /**
     * 第三方训练人模型编号
     */
    @TableField(value = "ext_model_code")
    private String extModelCode;

    /**
     * 训练状态：1 训练中；0 训练完成；-1 训练失败
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 请求三方报文
     */
    @TableField(value = "job_content")
    private String jobContent;

    /**
     * 回调业务url
     */
    @TableField(value = "callback_url")
    private String callbackUrl;

    /**
     * 训练名称 深声：声讯编码
     */
    @TableField(value = "train_name")
    private String trainName;

    /**
     * 三方错误码
     */
    @TableField(value = "fail_code")
    private String failCode;

    /**
     * 失败原因
     */
    @TableField(value = "fail_reason")
    private String failReason;

    /**
     * 试听链接
     */
    @TableField(value = "sample_link")
    private String sampleLink;

    /**
     * 训练来源，1-A端，2-D端
     */
    @TableField(value = "source")
    private Integer source;
}