package com.dl.aiservice.biz.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.aiservice.biz.common.po.BasePO;
import lombok.Data;

/**
 * 数字资产-仿真人场景信息表
 *
 * @TableName da_virtual_man_scenes
 */
@TableName(value = "da_virtual_man_scenes")
@Data
public class DaVirtualManScenesPO extends BasePO {
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数字人唯一标识
     */
    @TableField(value = "vm_biz_id")
    private Long vmBizId;

    /**
     * 数字人形象代码
     */
    @TableField(value = "vm_code")
    private String vmCode;

    /**
     * 渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    @TableField(value = "channel")
    private Integer channel;

    /**
     * 场景id(重要，视频合成必填ID)
     */
    @TableField(value = "scene_id")
    private String sceneId;

    /**
     * 场景名称
     */
    @TableField(value = "scene_name")
    private String sceneName;

    /**
     * 服装信息：0 个人服饰 1 黑衣服 2 蓝礼服
     */
    @TableField(value = "cloth")
    private Integer cloth;

    /**
     * 姿态信息：1 坐姿; 2 半身站姿; 3 全身站姿
     */
    @TableField(value = "pose")
    private Integer pose;

    /**
     * 分辨率：1 1080x1920; 2 1920x1080
     */
    @TableField(value = "resolution")
    private Integer resolution;

    /**
     * 场景封面地址
     */
    @TableField(value = "cover_url")
    private String coverUrl;

    /**
     * 场景样例视频地址
     */
    @TableField(value = "example_url")
    private String exampleUrl;

    /**
     * 是否启用 0：否，1：是
     */
    @TableField(value = "is_enabled")
    private Integer isEnabled;

    /**
     * 是否删除 0：否，1：是
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;

    /**
     * 场景样例时长，单位 毫秒
     */
    @TableField(value = "example_duration")
    private Integer exampleDuration;

    /**
     * 场景样例文本
     */
    @TableField(value = "example_text")
    private String exampleText;
}