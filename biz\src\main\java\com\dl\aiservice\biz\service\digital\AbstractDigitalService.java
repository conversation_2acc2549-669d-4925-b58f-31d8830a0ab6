package com.dl.aiservice.biz.service.digital;

import com.dl.aiservice.biz.client.guiji.DigitalCallbackClient;
import com.dl.aiservice.biz.common.constant.CommonCallbackLockKey;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.aiservice.share.digitalman.DigitalManCallbackDTO;
import com.dl.aiservice.share.digitalman.DigitalManVideoGenResultDTO;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-13 17:27
 */
public abstract class AbstractDigitalService implements BaseDigitalService {

    @Resource
    private DigitalCallbackClient digitalCallbackClient;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public void callBackBiz(String tenantCode, Long worksBizId, String callBackUrl,
            DigitalVideoCallbackDTO digitalCallbackDTO, String extCallbackRespBody) {
        // 注意，后续所有厂商是异步回调报告合成结果的都要这样写
        String lockKey = CommonCallbackLockKey.MEDIA_PRODUCE_JOB_CALLBACK_KEY_PREFIX + worksBizId
                + digitalCallbackDTO.getVideoTaskJobId();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            while (lock.tryLock()) {
                List<MediaProduceJobPO> jobList = mediaProduceJobManager.lambdaQuery()
                        .eq(Objects.nonNull(worksBizId), MediaProduceJobPO::getWorksBizId, worksBizId)
                        .eq(Objects.nonNull(digitalCallbackDTO.getVideoTaskJobId()),
                                MediaProduceJobPO::getVideoTaskJobId, digitalCallbackDTO.getVideoTaskJobId())
                        .list();
                DigitalManCallbackDTO result = new DigitalManCallbackDTO();
                result.setVideoTaskJobId(digitalCallbackDTO.getVideoTaskJobId());
                result.setWorksBizId(worksBizId);
                result.setTenantCode(tenantCode);
                result.setVideoList(jobList.stream()
                        .map(this::cnvMediaProduceJobPO2DigitalManVideoGenResultDTO)
                        .collect(Collectors.toList()));
                digitalCallbackClient.createCallback(callBackUrl, digitalCallbackDTO, extCallbackRespBody, result);
                break;
            }
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    private DigitalManVideoGenResultDTO cnvMediaProduceJobPO2DigitalManVideoGenResultDTO(MediaProduceJobPO source) {
        DigitalManVideoGenResultDTO digitalManVideoGenResultDTO = new DigitalManVideoGenResultDTO();
        digitalManVideoGenResultDTO.setMediaJobId(source.getMediaJobId());
        digitalManVideoGenResultDTO.setStatus(source.getStatus());
        digitalManVideoGenResultDTO.setMediaUrl(source.getMediaUrl());
        digitalManVideoGenResultDTO.setChannel(source.getChannel());
        digitalManVideoGenResultDTO.setDuration(source.getDuration());
        digitalManVideoGenResultDTO.setFailReason(source.getFailReason());
        return digitalManVideoGenResultDTO;
    }
}
