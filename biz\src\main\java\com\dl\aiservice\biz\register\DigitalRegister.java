package com.dl.aiservice.biz.register;

import com.dl.aiservice.biz.service.digital.BaseDigitalService;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class DigitalRegister implements BeanFactoryPostProcessor {

    private ConfigurableListableBeanFactory beanFactory;

    private static final Map<ServiceChannelEnum, BaseDigitalService> DIGITAL_API = new ConcurrentHashMap<>();

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    void init() {
        String[] diagnosisApi = this.beanFactory.getBeanNamesForType(BaseDigitalService.class);
        if (diagnosisApi.length != 0) {
            for (String sendApi : diagnosisApi) {
                register((BaseDigitalService) beanFactory.getBean(sendApi));
            }
        }
    }

    private void register(BaseDigitalService baseDigitalService) {
        if (Objects.isNull(baseDigitalService)) {
            return;
        }

        Assert.notEmpty(baseDigitalService.getEnums(),
                "enum() cannot be empty beanName:" + baseDigitalService.getClass().getName());
        for (ServiceChannelEnum channelEnum : baseDigitalService.getEnums()) {
            DIGITAL_API.put(channelEnum, baseDigitalService);
        }
    }

    BaseDigitalService get(ServiceChannelEnum typeEnum) {
        return DIGITAL_API.get(typeEnum);
    }
}
