package com.dl.aiservice.web.controller.voiceclone;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.callback.VoiceCallbackClient;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.dal.po.CallbackLogPO;
import com.dl.aiservice.biz.dal.po.DaVirtualVoicePO;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.filter.RepeatedlyRequestWrapper;
import com.dl.aiservice.biz.manager.CallbackLogManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualVoiceManager;
import com.dl.aiservice.biz.manager.train.TrainJobManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.TrainCallbackResponseDTO;
import com.dl.aiservice.web.controller.voiceclone.param.DeepSoundTrainCallbackParam;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * @ClassName VoiceCloneServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/3/13 9:54
 * @Version 1.0
 **/
@Slf4j
@Component
public class TrainCallbackProcessor {

    @Resource
    private VoiceCallbackClient voiceCallbackClient;
    @Resource
    private TrainJobManager trainJobManager;
    @Resource
    private CallbackLogManager callbackLogManager;
    @Resource
    private DaVirtualVoiceManager daVirtualVoiceManager;
    @Resource
    private HostTimeIdg hostTimeIdg;

    public ResultModel<Boolean> trainCallback(HttpServletRequest request, ServiceChannelEnum channelEnum) {
        String requestBody = RepeatedlyRequestWrapper.getBodyString(request);
        log.info("trainCallback>>channel>>{};param>>{}", channelEnum.getCode(), requestBody);
        switch (channelEnum) {
        case DEEP_SOUND:
            DeepSoundTrainCallbackParam deepSoundTrainCallbackParam =
                    JsonUtils.fromJSON(requestBody, DeepSoundTrainCallbackParam.class);
            dsCallback(deepSoundTrainCallbackParam);
        }
        return ResultModel.success(Boolean.TRUE);
    }

    private void dsCallback(DeepSoundTrainCallbackParam callback) {
        if (Objects.isNull(callback)) {
            log.error("入参空！param={}", JSONUtil.toJsonStr(callback));
            return;
        }
        String recordId = callback.getRecordId();
        TrainJobPO trainJob = trainJobManager.lambdaQuery().eq(TrainJobPO::getTrainJobId, Long.valueOf(recordId)).one();
        if (Objects.isNull(trainJob)) {
            CallbackLogPO callbackLog =
                    initCallbackLog(ServiceChannelEnum.DEEP_SOUND, JsonUtils.toJSON(callback), recordId);
            callbackLog.setStatus(Const.TWO);
            callbackLogManager.save(callbackLog);
            return;
        }
        trainJob.setExtJobId(callback.getBusinessId());
        if (Objects.equals(Const.ZERO, callback.getErrorCode())) {
            // 训练成功
            trainJob.setStatus(Const.ZERO);
            trainJob.setExtModelCode(callback.getVoiceName());
            DaVirtualVoicePO daVirtualVoicePO = new DaVirtualVoicePO();
            daVirtualVoicePO.setGender(trainJob.getGender());
            daVirtualVoicePO.setChannel(trainJob.getChannel());
            daVirtualVoicePO.setVoiceName(trainJob.getTrainName());
            daVirtualVoicePO.setBizId(hostTimeIdg.generateId().longValue());
            daVirtualVoicePO.setVoiceKey(trainJob.getExtModelCode());
            daVirtualVoiceManager.save(daVirtualVoicePO);
        } else {
            // 训练失败
            trainJob.setStatus(-Const.ONE);
        }
        trainJobManager.lambdaUpdate().eq(TrainJobPO::getId, trainJob.getId()).update(trainJob);
        CallbackLogPO callbackLog =
                initCallbackLog(ServiceChannelEnum.DEEP_SOUND, JSONUtil.toJsonStr(callback), recordId);
        TrainCallbackResponseDTO trainCallbackResp = new TrainCallbackResponseDTO();
        trainCallbackResp.setTrainJobId(callback.getRecordId());
        trainCallbackResp.setCode(callback.getErrorCode());
        trainCallbackResp.setMsg(callback.getMsg());
        trainCallbackResp.setStatus(callback.getStatus());
        trainCallbackResp.setQuality(callback.getQuality());
        trainCallbackResp.setVoiceName(callback.getVoiceName());
        ResultModel callbackResult = voiceCallbackClient.callback(trainJob.getCallbackUrl(), trainCallbackResp);
        if (callbackResult.isSuccess()) {
            callbackLog.setStatus(Const.ONE);
        } else {
            callbackLog.setStatus(Const.TWO);
        }
        callbackLog.setCallbackRespBody(JSONUtil.toJsonStr(callbackResult));
        callbackLogManager.save(callbackLog);
    }

    private CallbackLogPO initCallbackLog(ServiceChannelEnum e, String extCallbackRespBody, String recordId) {
        CallbackLogPO callbackLog = new CallbackLogPO();
        callbackLog.setExtJobId(recordId);
        // 3声纹训练
        callbackLog.setCallbackType(Const.THREE);
        callbackLog.setChannel(e.getCode());
        callbackLog.setExtCallbackRespBody(extCallbackRespBody);
        return callbackLog;
    }
}
