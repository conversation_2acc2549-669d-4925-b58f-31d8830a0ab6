package com.dl.aiservice.share.videoproduce.dto.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @describe: OutputMediaConfig
 * @author: zhousx
 * @date: 2023/2/11 16:19
 */
@Data
public class OutputMediaConfigDTO {
    @JsonProperty("MediaURL")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String mediaUrl;

    @JsonProperty("Bitrate")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer bitrate;

    @JsonProperty("Height")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer height;

    @JsonProperty("Weight")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer weight;
}
