package com.dl.aiservice.biz.manager.voiceclone.volcengine;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-25 14:55
 */

public class VolcEngineVoiceCloneManager {

    public static void main(String[] args) {
        /*File audioFile = new File(
                "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/66a91a421b4f94af4a9da5e98bc16779/Message/MessageTemp/9e20f478899dc29eb19741386f9343c8/File/英飞特大厦B座.m4a");
*/

        // 文件路径
        /*String filePath = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/66a91a421b4f94af4a9da5e98bc16779/Message/MessageTemp/9e20f478899dc29eb19741386f9343c8/File/英飞特大厦B座.m4a";
        String base64Encoded;
        try {
            // 读取文件的字节
            byte[] fileContent = Files.readAllBytes(Paths.get(filePath));

            // 使用Base64进行编码
            Base64.Encoder encoder = Base64.getEncoder();
            base64Encoded = encoder.encodeToString(fileContent);

            // 输出Base64编码后的字符串
            System.out.println("Base64 Encoded Content: " + base64Encoded);

        } catch (Exception e) {
            e.printStackTrace();
            return;
        }

        VolcEngineVoiceTrainRequest request = new VolcEngineVoiceTrainRequest();
        request.setAppId("7578667403");
        request.setSpeakerId("S_RevojoiP");
        VolcEngineVoiceTrainAudio audio = new VolcEngineVoiceTrainAudio();
        audio.setAudioBytes(base64Encoded);
        audio.setAudioFormat("m4a");
        request.setAudios(Lists.newArrayList(audio));

        try {
            String resp1 = post(com.alibaba.fastjson2.JSONObject.toJSONString(request), TRAIN_API_URL);
            System.out.println("resp1:   " + resp1);
        } catch (Exception e) {
            e.printStackTrace();
        }*/

        System.out.println("============================");
        System.out.println("============================");
        System.out.println("============================");
        //查询状态
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appid", "7578667403");
        jsonObject.put("speaker_id", "S_RevojoiP");
        try {
            String resp2 = post(JSONUtil.toJsonStr(jsonObject), STATUS_API_URL);
            System.out.println("resp2:   " + resp2);
        } catch (Exception e) {
            e.printStackTrace();
        }

        System.out.println("============================");
        System.out.println("============================");
        System.out.println("============================");

        //激活
        /*JSONObject jsonObject3 = new JSONObject();
        jsonObject3.put("AppID", "7578667403");
        jsonObject3.put("Action", "query");
        jsonObject3.put("Version", "2023-11-07");
        jsonObject3.put("SpeakerIDs", "[S_RevojoiP]");
        try {
            String resp3 = post(JSONUtil.toJsonStr(jsonObject3), ACTIVATE_API_URL);
            System.out.println("resp3:   " + resp3);
        } catch (Exception e) {
            e.printStackTrace();
        }*/

        System.out.println("============================");
        System.out.println("============================");
        System.out.println("============================");

        /*TtsRequest req = new TtsRequest();
        String reqId = "1500304112";
        String codec = "mp3";
        TtsRequest.User user = new TtsRequest.User();
        user.setUid(reqId);
        TtsRequest.Request request = new TtsRequest.Request();
        request.setReqid(reqId);
        request.setText(
                "大家好，今天我们来聊聊小米SU7，这是一款在3月28日小米汽车上市发布会上亮相的智能电动车。这款C级高性能生态科技轿车，拥有海湾蓝、雅灰、橄榄绿三种配色。在智能驾驶方面，小米的目标是2024年进入行业第一阵营，明年年底开通100个城市的城市领航NOA。");
        TtsRequest.Audio audio = new TtsRequest.Audio();
        audio.setEncoding(codec);

        req.getApp().setAppid("7578667403");
        req.getApp().setCluster("volcano_mega");

        audio.setVoice_type("S_RevojoiP");
        req.setRequest(request);
        req.setAudio(audio);
        req.setUser(user);
        try {
            String resp4 = post(JsonUtils.toJSON(req), TTS_API_URL);
            TtsResponse ttsResponse = JSONUtil.toBean(resp4, TtsResponse.class);
            if (!Objects.equals(ttsResponse.getCode(), 3000)) {
                System.out.println("volcEngine tts resp:" + JsonUtils.toJSON(ttsResponse));
                return;
            }

            File audioFile = getAudioFile("/Users/<USER>/Desktop/测试火山引擎tts2", ttsResponse.getData(), codec);
        } catch (Exception e) {
            System.out.println("发生异常:\n" + e);
        }*/

    }

    public static final String HOST = "openspeech.bytedance.com";
    public static final String ACCESS_TOKEN = "QRyhHHq8ar0UyOwd_yv6xI4TWzxMbS7R";
    //public static final String API_URL = "https://" + HOST + "/tts_middle_layer/tts";
    public static final String TRAIN_API_URL = "https://" + HOST + "/api/v1/mega_tts/audio/upload";
    public static final String STATUS_API_URL = "https://" + HOST + "/api/v1/mega_tts/status";
    public static final String ACTIVATE_API_URL = "https://" + HOST + "/api/v1/mega_tts/activate";
    public static final String TTS_API_URL = "https://" + HOST + "/api/v1/tts";

    public static String post(String json, String url) throws IOException {
        OkHttpClient client = new OkHttpClient();
        RequestBody body = RequestBody.create(json, MediaType.get("application/json; charset=utf-8"));
        Request request = new Request.Builder().url(url).post(body).header("Authorization", "Bearer; " + ACCESS_TOKEN)
                .header("Resource-Id", "volc.megatts.voiceclone").build();
        try (Response response = client.newCall(request).execute()) {
            String bodyString = response.body().string();

            return bodyString;
        } catch (Exception e) {
            throw BusinessServiceException.getInstance("火山声音克隆请求失败");
        }
    }

    private static File getAudioFile(String sessionId, String audioData, String format) throws Exception {
        // 解码 Base64 数据
        byte[] data = Base64.getDecoder().decode(audioData);
        File file = new File(sessionId + "." + format); // 文件路径
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data); // 将字节数组写入文件
        fos.close();
        return file;
    }

}
