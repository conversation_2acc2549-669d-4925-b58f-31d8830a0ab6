package com.dl.aiservice.share.voiceclone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName DsAudioCheckResponse
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 15:47
 * @Version 1.0
 **/
@Data
public class AudioCheckResponseDTO implements Serializable {

    private static final long serialVersionUID = -255119982193376160L;

    @ApiModelProperty("语音识别文本")
    private String asrText;

    @ApiModelProperty("语音识别检测多漏错读提示信息")
    private List<AudioCheckDifDTO> diffInfoList;
}
