package com.dl.aiservice.share.enums;

/**
 * 媒体合成记录的状态枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-13 13:58
 */
public enum MediaProduceJobStatusEnum {

    ING(1,"合成中"),
    SUCCESS(0,"合成完成"),
    FAIL(-1,"合成失败"),
    WAITING(2,"待合成");


    private Integer status;

    private String desc;

    MediaProduceJobStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
