package com.dl.aiservice.share.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName BizIdRequestDTO
 * @Description
 * <AUTHOR>
 * @Date 2023/6/5 9:54
 * @Version 1.0
 **/
@Data
public class BizIdRequestDTO implements Serializable {

    private static final long serialVersionUID = 4040779280083857477L;

    @ApiModelProperty(value = "通用必填：上层业务唯一id")
    private Long bizId;

    @ApiModelProperty(value = "通用必填：上层业务唯一id")
    private List<Long> bizIds;
}
