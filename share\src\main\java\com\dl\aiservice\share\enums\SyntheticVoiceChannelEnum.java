package com.dl.aiservice.share.enums;

import java.util.Arrays;
import java.util.Objects;

@Deprecated
public enum SyntheticVoiceChannelEnum {
    /**
     * 新华智云
     */
    XHZY(0, "XHZY"),
    /**
     * 阿里云
     */
    ALIYUN(4, "ALIYUN"),
    /**
     * 腾讯云TTS
     */
    TENCENT_CLOUD(5, "TENCENT_CLOUD"),
    /**
     * 火山引擎
     */
    VOLC_ENGINE(6, "VOLC_ENGINE");

    private Integer code;
    private String desc;

    SyntheticVoiceChannelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SyntheticVoiceChannelEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> Objects.equals(code, e.getCode())).findAny().orElse(null);
    }
}
