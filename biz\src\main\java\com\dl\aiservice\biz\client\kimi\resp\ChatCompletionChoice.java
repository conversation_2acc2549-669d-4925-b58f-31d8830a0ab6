package com.dl.aiservice.biz.client.kimi.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 10:42
 */
public class ChatCompletionChoice {

    private int index;
    /**
     * 返回内容
     */
    private ChatCompletionMessage message;

    @JsonProperty("finish_reason")
    private String finishReason;

    public int getIndex() {
        return index;
    }

    public ChatCompletionMessage getMessage() {
        return message;
    }

    public String getFinishReason() {
        return finishReason;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public void setMessage(ChatCompletionMessage message) {
        this.message = message;
    }

    public void setFinishReason(String finishReason) {
        this.finishReason = finishReason;
    }
}
