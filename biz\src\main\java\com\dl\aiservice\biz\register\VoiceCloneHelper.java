package com.dl.aiservice.biz.register;

import com.dl.aiservice.biz.manager.voiceclone.VoiceCloneHandlerManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@AllArgsConstructor
public class VoiceCloneHelper {

    private final VoiceCloneRegister voiceCloneRegister;

    @PostConstruct
    public void init() {
        voiceCloneRegister.init();
    }

    public VoiceCloneHandlerManager get(ServiceChannelEnum typeEnum) {
        return voiceCloneRegister.get(typeEnum);
    }
}
