package com.dl.aiservice.biz.client.deepsound.enums;

import java.util.Objects;

public enum DsStandardErrCodeEnum {

    ERROR_CODE_0(0, "正常"),
    ERROR_CODE_10101010(10101010, "签名参数缺失-Sign"),
    ERROR_CODE_10101011(10101011, "签名参数缺失-Timestamp"),
    ERROR_CODE_10101012(10101012, "签名过期"),
    ERROR_CODE_10101013(10101013, "签名错误"),
    ERROR_CODE_10101014(10101014, "appid不存在"),
    ERROR_CODE_10101015(10101015, "资源已达上限"),
    ERROR_CODE_10101016(10101016, "服务繁忙"),
    ERROR_CODE_10101017(10101017, "请求错误（错误详情）"),
    ERROR_CODE_10101018(10101018, "音色无权限"),
    UNKNOWN(9999999, "未知异常");

    private final Integer errorCode;
    private final String errorDesc;

    DsStandardErrCodeEnum(Integer errorCode, String errorDesc) {
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }

    public static String getErrorDesc(Integer errorCode) {
        for (DsStandardErrCodeEnum wxErrorCodeEnum : values()) {
            if (Objects.equals(wxErrorCodeEnum.errorCode, errorCode)) {
                return wxErrorCodeEnum.errorDesc;
            }
        }
        return null;
    }

    public static DsStandardErrCodeEnum errorCode(Integer code) {
        for (DsStandardErrCodeEnum value : values()) {
            if (Objects.nonNull(code)) {
                if (value.getErrorCode().equals(code)) {
                    return value;
                }
            }
        }
        return UNKNOWN;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorDesc() {
        return errorDesc;
    }
}
