package com.dl.aiservice.biz.client.kimi.interceptor;

import cn.hutool.json.JSONObject;
import com.dl.aiservice.biz.client.kimi.consts.KimiConst;
import com.dl.aiservice.biz.client.kimi.enums.KimiErrorHttpCodeEnum;
import com.dl.aiservice.share.aichat.errorcode.AiChatErrorCodeEnum;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-08 13:51
 */
@Slf4j
public class KimiUploadFileInterceptor implements Interceptor {

    private static final String FILE_NO_CONTENT = "file.no_content";

    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        log.info("kimi文件上传成功!response:{}", response.getContent());
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        log.error("Kimi文件上传发生错误 :\nrequest: {},\nhttpStatus:{},\nresponse: {}",
                request.getBody().nameValuesMapWithObject(), response.getStatusCode(), response.getContent());

        Interceptor.super.onError(ex, request, response);

        //获取请求响应状态码
        int status = response.getStatusCode();
        KimiErrorHttpCodeEnum errorCodeEnum = KimiErrorHttpCodeEnum.parse(status);
        if (Objects.nonNull(errorCodeEnum)) {
            switch (errorCodeEnum) {
            case rate_limit_reached_error:
                throw BusinessServiceException.getInstance(AiChatErrorCodeEnum.OVERLOAD.getCode(), AiChatErrorCodeEnum.OVERLOAD.getMessage());
            case exceeded_current_quota_error:
                throw BusinessServiceException.getInstance(AiChatErrorCodeEnum.INSUFFICIENT_BALANCE.getCode(),
                        AiChatErrorCodeEnum.INSUFFICIENT_BALANCE.getMessage());
            default:
                throw BusinessServiceException.getInstance(errorCodeEnum.getDesc());
            }
        }

        //尝试提取错误信息
        String errorMsg = tryExtractErrorMessage(response.getContent());
        if (StringUtils.isNotBlank(errorMsg)) {
            throw BusinessServiceException.getInstance(errorMsg);
        }

        throw BusinessServiceException.getInstance(KimiConst.FILE_CONTENT_EXTRACT_ERROR_MSG);
    }

    /**
     * 尝试提取错误信息
     * {"error":{"message":"failed to extract file: unexpected status code: 400, body: {\"error_type\":\"file.no_content\",\"message\":\"没有解析出内容\"}","type":"server_error"}}
     *
     * @param respContent
     * @return
     */
    private String tryExtractErrorMessage(String respContent) {
        try {
            JSONObject jsonObject = new JSONObject(respContent);
            String extractedErrorType = null;
            JSONObject errorObject = jsonObject.getJSONObject("error");

            String message = errorObject.getStr("message");
            JSONObject bodyObject = new JSONObject(message.substring(message.indexOf("body: ") + "body: ".length()));
            extractedErrorType = bodyObject.getStr("error_type");
            String extractedErrorMsg = bodyObject.getStr("message");

            if (FILE_NO_CONTENT.equals(extractedErrorType)) {
                return KimiConst.FILE_NO_CONTENT_ERROR_MSG;
            }

            return extractedErrorMsg;
        } catch (Exception e) {
            //吃掉异常
            return null;
        }
    }
}
