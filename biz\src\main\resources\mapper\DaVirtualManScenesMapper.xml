<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.aiservice.biz.dal.mapper.DaVirtualManScenesMapper">

    <resultMap id="BaseResultMap" type="com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManScenesBO">
        <result property="id" column="id"/>
        <result property="bizId" column="biz_id"/>
        <result property="vmCode" column="vm_code"/>
        <result property="vmName" column="vm_name"/>
        <result property="vmVoiceKey" column="vm_voice_key"/>
        <result property="voiceBizId" column="voice_biz_id"/>
        <result property="gender" column="gender"/>
        <result property="headImg" column="head_img"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="positionName" column="position_name"/>
        <result property="vmType" column="vm_type"/>
        <result property="channel" column="channel"/>
        <result property="effectDt" column="effect_dt"/>
        <result property="expiryDt" column="expiry_dt"/>
        <result property="sceneId" column="scene_id"/>
        <result property="sceneName" column="scene_name"/>
        <result property="cloth" column="cloth"/>
        <result property="isEnabled" column="is_enabled"/>
        <result property="pose" column="pose"/>
        <result property="resolution" column="resolution"/>
        <result property="coverUrl" column="cover_url"/>
        <result property="exampleUrl" column="example_url"/>
        <result property="exampleText" column="example_text"/>
        <result property="exampleDuration" column="example_duration"/>
    </resultMap>

    <select id="listVirtualManScene" resultMap="BaseResultMap">
        SELECT dvm.biz_id, dvm.vm_code, dvm.vm_name, dvm.vm_voice_key, dvm.voice_biz_id, dvm.gender,
        dvm.head_img, dvm.vm_type, dvm.channel, dvm.effect_dt, dvm.expiry_dt, dvm.phone, dvm.email, dvm.position_name,
        dvms.scene_id, dvms.scene_name, dvms.cover_url, dvms.example_url, dvms.example_text, dvms.example_duration,
        dvms.cloth, dvms.pose, dvms.resolution, dvms.is_enabled, dvms.id
        FROM da_virtual_man dvm, da_virtual_man_scenes dvms
        WHERE dvm.biz_id = dvms.vm_biz_id
        AND dvm.is_enabled = 1
        AND dvm.is_deleted = 0
        AND dvms.is_deleted = 0
        <if test="enableFilter == null">
            AND dvms.is_enabled = 1
        </if>
        and dvm.biz_id in
        <foreach collection="bizIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY dvms.modify_dt ASC
    </select>


    <select id="listVirtualManSceneBySceneIds" resultMap="BaseResultMap">
        SELECT dvm.biz_id, dvm.vm_code, dvm.vm_name, dvm.vm_voice_key, dvm.voice_biz_id, dvm.gender,
        dvm.head_img, dvm.vm_type, dvm.channel, dvm.effect_dt, dvm.expiry_dt, dvm.phone, dvm.email, dvm.position_name,
        dvms.scene_id, dvms.scene_name, dvms.cover_url, dvms.example_url, dvms.example_text, dvms.example_duration,
        dvms.cloth, dvms.pose, dvms.resolution, dvms.is_enabled, dvms.id
        FROM da_virtual_man dvm, da_virtual_man_scenes dvms
        WHERE dvm.biz_id = dvms.vm_biz_id
        AND dvm.is_deleted = 0
        AND dvms.is_deleted = 0
        <if test="enableFilter == null">
            AND dvm.is_enabled = 1
            AND dvms.is_enabled = 1
        </if>
        <if test="sceneIdList != null">
            and dvms.scene_id in
            <foreach collection="sceneIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="channelList != null">
            and dvm.channel in
            <foreach collection="channelList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY dvms.modify_dt ASC
    </select>

    <select id="countVirtualManScene" parameterType="com.dl.aiservice.biz.dal.po.DaVirtualManSceneQueryPO"
            resultType="java.lang.Integer">
        SELECT count(1)
        FROM da_virtual_man_scenes dvms
				left join da_tenant_auth dta
				on dta.biz_id = dvms.vm_biz_id
				left join da_virtual_man dvm
				on dvm.biz_id = dvms.vm_biz_id
        WHERE
		dta.tenant_code = #{tenantCode}
		AND dta.biz_type = 1
		AND dta.is_deleted = 0
        AND dvm.is_enabled = 1
        AND dvm.is_deleted = 0
        AND dvms.is_deleted = 0
        AND dvms.is_enabled = 1
        AND dvm.tenant_code = #{tenantCode}
        ORDER BY dvms.modify_dt ASC
    </select>

    <select id="pageVirtualManScene" parameterType="com.dl.aiservice.biz.dal.po.DaVirtualManSceneQueryPO"
            resultMap="BaseResultMap">
        SELECT dvm.biz_id, dta.tenant_code, dvm.vm_code, dvm.vm_name, dvm.vm_voice_key, dvm.voice_biz_id, dvm.gender,
        dvm.head_img, dvm.vm_type, dvm.channel, dvm.effect_dt, dvm.expiry_dt, dvms.scene_id, dvms.scene_name,
        dvms.cover_url, dvms.example_url, dvms.cloth, dvms.pose, dvms.resolution, dvms.id
        FROM da_virtual_man_scenes dvms
				left join da_tenant_auth dta
				on dta.biz_id = dvms.vm_biz_id
				left join da_virtual_man dvm
				on dvm.biz_id = dvms.vm_biz_id
        WHERE
		dta.tenant_code = #{tenantCode}
		AND dta.biz_type = 1
		AND dta.is_deleted = 0
		AND	dvm.is_enabled = 1
        AND dvm.is_deleted = 0
        AND dvms.is_deleted = 0
        AND dvms.is_enabled = 1
        ORDER BY dvms.modify_dt ASC
        limit #{offset}, #{pageSize}
    </select>
</mapper>
