package com.dl.aiservice.biz.manager.train.enums;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;

public enum TrainStatusEnum {

    //训练状态：1 训练中；0 训练完成；-1 训练失败
    MAKING(1, "训练中"),
    SUCCESS(0, "训练成功"),
    FAIL(-1, "训练失败"),
    ;

    private final Integer code;
    private final String desc;

    TrainStatusEnum(Integer errorCode, String errorDesc) {
        this.code = errorCode;
        this.desc = errorDesc;
    }

    public static TrainStatusEnum getEnum(String desc) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getDesc(), desc)).findFirst()
                .orElseThrow(() -> BusinessServiceException.getInstance("枚举异常"));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    }
