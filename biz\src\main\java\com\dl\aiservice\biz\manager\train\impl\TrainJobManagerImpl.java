package com.dl.aiservice.biz.manager.train.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.aiservice.biz.dal.mapper.TrainJobMapper;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.manager.train.TrainJobManager;
import com.dl.aiservice.biz.manager.train.bo.TrainJobAddBO;
import com.dl.aiservice.biz.manager.train.bo.TrainJobPageBO;
import com.dl.framework.common.idg.HostTimeIdg;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【train_job(训练任务表)】的数据库操作Service实现
 * @createDate 2023-03-10 17:01:41
 */
@Service
public class TrainJobManagerImpl extends ServiceImpl<TrainJobMapper, TrainJobPO> implements TrainJobManager {
    @Resource
    private HostTimeIdg hostTimeIdg;

    @Override
    public TrainJobPO addJob(TrainJobAddBO bo) {
        TrainJobPO jobPO = new TrainJobPO();
        jobPO.setTenantCode(bo.getTenantCode());
        jobPO.setChannel(bo.getChannel());
        jobPO.setJobType(bo.getJobType());
        jobPO.setGender(bo.getGender());
        jobPO.setTrainJobId(hostTimeIdg.generateId().longValue());
        jobPO.setExtJobId(bo.getExtJobId());
        jobPO.setExtModelCode(bo.getExtModelCode());
        jobPO.setStatus(bo.getStatus());
        jobPO.setJobContent(bo.getJobContent());
        jobPO.setCallbackUrl(bo.getCallbackUrl());
        jobPO.setTrainName(bo.getTrainName());
        jobPO.setSource(bo.getSource());
        jobPO.setCreateDt(new Date());
        jobPO.setModifyDt(new Date());

        this.save(jobPO);
        return jobPO;
    }

    @Override
    public IPage<TrainJobPO> page(TrainJobPageBO bo) {
        LambdaQueryWrapper<TrainJobPO> queryWrapper = Wrappers.lambdaQuery(TrainJobPO.class)
                .eq(TrainJobPO::getChannel, bo.getChannel()).eq(TrainJobPO::getJobType, bo.getTrainType())
                .eq(TrainJobPO::getExtModelCode, bo.getExtModelCode()).orderByDesc(TrainJobPO::getCreateDt);

        return this.page(new Page<>(bo.getPageIndex(), bo.getPageSize()), queryWrapper);
    }
}




