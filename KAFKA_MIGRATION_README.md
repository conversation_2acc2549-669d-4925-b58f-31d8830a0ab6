# RocketMQ到Kafka迁移说明

## 概述
本项目已从RocketMQ消息队列迁移到Apache Kafka。本文档说明了迁移过程中的主要变更和注意事项。

## 主要变更

### 1. Maven依赖变更
- **移除**: `spring-cloud-starter-stream-rocketmq`
- **添加**: `spring-cloud-starter-stream-kafka`

### 2. 配置文件变更
在 `war/src/main/resources/application.yaml` 中：

#### 开发环境配置
```yaml
spring:
  profiles: dev
  cloud:
    stream:
      kafka:
        binder:
          brokers: localhost:9092
      bindings:
        digitalprogressconsumer:
          destination: digital-progress-topic
          group: digital-progress-consumer-group
        digitalprogressproducer:
          destination: digital-progress-topic
        voicetrainprogressconsumer:
          destination: voice-train-progress-topic
          group: voice-train-progress-consumer-group
        voicetrainprogressproducer:
          destination: voice-train-progress-topic
```

#### 生产环境配置
```yaml
spring:
  profiles: prod
  cloud:
    stream:
      kafka:
        binder:
          brokers: prod-kafka-host:9092
      bindings:
        # 同开发环境配置
```

### 3. 代码变更

#### Producer类变更
- **AiDigitalManProducer**: 移除RocketMQ延迟消息功能，使用新的DelayedMessageService
- **AiVoiceTrainProducer**: 移除RocketMQ延迟消息功能，使用新的DelayedMessageService

#### Consumer类变更
- **AiProgressDigitalManConsumer**: 移除RocketMQ特定的Header处理，使用Kafka Headers
- **AiVoiceTrainProgressConsumer**: 无需修改（未使用RocketMQ特定功能）

#### 新增类
- **DelayedMessageService**: 实现延迟消息功能的替代方案
- **AsyncConfig**: 异步任务配置，支持延迟消息功能

### 4. 延迟消息处理
由于Kafka不直接支持延迟消息，我们实现了基于异步任务的延迟消息服务：
- 使用 `DelayedMessageService` 来处理延迟消息
- 通过异步任务和 `Thread.sleep()` 实现延迟功能
- 保持与原RocketMQ延迟级别的兼容性

## 部署注意事项

### 1. Kafka环境准备
确保Kafka集群已正确部署并可访问：
- **开发环境**: localhost:9092
- **生产环境**: prod-kafka-host:9092

### 2. Topic创建
需要在Kafka中创建以下Topic：
- `digital-progress-topic`: 数字人进度消息
- `voice-train-progress-topic`: 声音训练进度消息

### 3. 消费者组
配置了以下消费者组：
- `digital-progress-consumer-group`: 数字人进度消息消费者组
- `voice-train-progress-consumer-group`: 声音训练进度消息消费者组

## 测试验证

### 运行测试
```bash
mvn test -Dtest=KafkaMessageTest
```

### 测试内容
- 数字人进度消息发送测试
- 声音训练进度消息发送测试
- 延迟消息服务功能测试

## 兼容性说明

### 保持兼容的功能
- 消息发送和接收的业务逻辑保持不变
- DelayLevelEnum枚举类保持不变
- 消费者处理逻辑保持不变

### 功能差异
- **延迟消息**: 从RocketMQ原生支持改为基于异步任务的实现
- **消息Header**: 从RocketMQ特定Header改为Kafka Headers
- **消息确认**: 使用Kafka的消息确认机制

## 监控和运维

### 日志监控
- 延迟消息发送日志包含延迟级别信息
- 消息发送失败会记录详细错误信息

### 性能考虑
- 延迟消息使用异步线程池，避免阻塞主线程
- 线程池配置：核心线程5个，最大线程20个

## 回滚方案
如需回滚到RocketMQ：
1. 恢复 `biz/pom.xml` 中的RocketMQ依赖
2. 恢复 `application.yaml` 中的RocketMQ配置
3. 恢复Producer和Consumer类中的RocketMQ特定代码
4. 移除DelayedMessageService和AsyncConfig类
