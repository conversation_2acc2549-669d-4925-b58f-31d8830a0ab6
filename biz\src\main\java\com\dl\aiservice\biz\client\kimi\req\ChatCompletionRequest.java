package com.dl.aiservice.biz.client.kimi.req;

import com.dl.aiservice.biz.client.kimi.resp.ChatCompletionMessage;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 10:44
 */
@Data
public class ChatCompletionRequest {

    /**
     * Model ID, 可以通过 List Models 获取
     * 默认moonshot-v1-8k
     * 必填
     */
    public String model = "moonshot-v1-8k";

    /**
     * 包含迄今为止对话的消息列表。
     * 必填
     */
    public List<ChatCompletionMessage> messages;

    /**
     * 聊天完成时生成的最大 token 数。如果到生成了最大 token 数个结果仍然没有结束，finish reason 会是 "length", 否则会是 "stop"
     * 非必填
     */
    @SerializedName("max_tokens")
    public int maxTokens;

    /**
     * 使用什么采样温度，介于 0 和 1 之间。较高的值（如 0.7）将使输出更加随机，而较低的值（如 0.2）将使其更加集中和确定性。
     * 非必填
     */
    @SerializedName("temperature")
    public float temperature = 0.2f;
    /**
     * 另一种采样温度
     * 非必填
     */
    public float topP = 1.0f;

    /**
     * 为每条输入消息生成多少个结果
     * 默认 1，不得大于 5 特别的，当 temperature 非常小靠近 0 的时候，我们只能返回 1 个结果，如果这个时候 n 设置并 > 1，我们服务会返回不合法的输入参数（ invalid_request_error ）。
     * 非必填
     */
    public Integer n = 1;
    /**
     * 是否流式返回
     * 非必填
     */
    public boolean stream = false;
    /**
     * 停止词，当全匹配这个（组）词后会停止输出，这个（组）词本身不会输出。最多不能超过 5 个字符串，每个字符串不得超过 32 字节。
     * 非必填
     */
    public List<String> stop;

    /**
     * 存在惩罚，介于-2.0到2.0之间的数字。正值会根据新生成的词汇是否出现在文本中来进行惩罚，增加模型讨论新话题的可能性。
     * 非必填
     */
    @SerializedName("presence_penalty")
    public float presencePenalty = 0;

    /**
     * 频率惩罚，介于-2.0到2.0之间的数字。正值会根据新生成的词汇在文本中现有的频率来进行惩罚，减少模型一字不差重复同样话语的可能性。
     * 非必填
     */
    @SerializedName("frequency_penalty")
    public float frequencyPenalty = 0;

    public List<ChatCompletionMessage> getMessages() {
        return messages;
    }

}
