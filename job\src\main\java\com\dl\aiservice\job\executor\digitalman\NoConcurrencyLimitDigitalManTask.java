package com.dl.aiservice.job.executor.digitalman;

import com.dl.aiservice.biz.digitaljobhandler.NoConcurrencyLimitDigitalManJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-13 15:58
 */
@Component
public class NoConcurrencyLimitDigitalManTask {

    @Resource
    private NoConcurrencyLimitDigitalManJobHandler noConcurrencyLimitDigitalManJobHandler;

//    @XxlJob("noConcurrencyLimitDmVideoJob")
    public void noConcurrencyLimitDmVideoJob() {
        noConcurrencyLimitDigitalManJobHandler.handleDmVideoCreate();
    }
}
