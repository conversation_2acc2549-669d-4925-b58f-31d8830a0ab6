package com.dl.aiservice.share.resourceconfig.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @describe: ResourcePriceConfigResponseDTO
 * @author: zhousx
 * @date: 2023/3/16 15:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ResourcePriceConfigResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 资源类型 1-视频合成，2-数字人，3-克隆音，4-合成音
     */
    private Integer resourceType;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 渠道 0-新华智云 1-硅基 2-腾讯云 3-深声科技 4-阿里云 5-腾讯云 6-火山引擎
     */
    private Integer channel;

    /**
     * 每分钟单价，单位分
     */
    private Long price;

    private Integer order;
}
