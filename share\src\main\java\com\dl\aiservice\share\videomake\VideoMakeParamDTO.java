package com.dl.aiservice.share.videomake;

import com.dl.aiservice.share.enums.MediaProduceChannelEnum;
import com.dl.aiservice.share.videomake.virtualman.VirtualManInfoDTO;
import com.dl.aiservice.share.videomake.virtualvoice.VirtualVoiceInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * @ClassName VideoMakeParamDTO
 * @Description 视频合成入参对象
 * <AUTHOR>
 * @Date 2023/4/14 13:52
 * @Version 1.0
 **/
@Data
public class VideoMakeParamDTO {

    @ApiModelProperty(value = "视频合成供应商渠道")
    @NotNull(message = "请传入合成商渠道")
    private MediaProduceChannelEnum mediaProduceChannel;

    @ApiModelProperty(value = "录制模式：1 数字分身 2 轻拍摄 3 拍全程 4 照片版")
    @NotNull(message = "请传入录制模式")
    private Integer videoMode;

    @ApiModelProperty(value = "合成视频报文变量信息", required = true)
    @Valid
    @NotNull(message = "视频合成变量数据必填")
    private BaseMessageDTO message;

    @ApiModelProperty(value = "合成视频时所需的数字人信息")
    private VirtualManInfoDTO virtualManInfo;

    @ApiModelProperty(value = "合成视频时所需的数字声音信息")
    private VirtualVoiceInfoDTO virtualVoiceInfo;

    @ApiModelProperty(value = "上层业务作品唯一标识")
    private Long worksBizId;
}
