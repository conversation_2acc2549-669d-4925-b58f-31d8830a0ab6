package com.dl.aiservice.share.videoproduce.dto.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @describe: FontFace
 * @author: zhousx
 * @date: 2023/2/11 11:26
 */
@Data
public class FontFaceDTO {
    @JsonProperty("Bold")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean bold;

    @JsonProperty("Italic")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean italic;

    @JsonProperty("Underline")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean underline;
}
