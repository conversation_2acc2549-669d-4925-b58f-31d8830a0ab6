package com.dl.aiservice.biz.service.digital.dto.req;

import com.dl.aiservice.biz.common.po.UpdateBasePO;
import com.dl.aiservice.biz.service.digital.dto.req.ivh.IvhAnchorDTO;
import com.dl.aiservice.biz.service.digital.dto.req.ivh.IvhLogoParamsDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@NoArgsConstructor
@Data
@ApiModel("视频合成请求参数DTO")
public class CreateRequestDTO extends UpdateBasePO implements Serializable {
    private static final long serialVersionUID = -6727712564242004694L;

    /**
     * 上层业务唯一id
     */
    @ApiModelProperty(value = "通用必填：上层业务唯一id", required = true)
    private Long worksBizId;

    @ApiModelProperty(value = "快视频合成任务内部唯一标识")
    private Long videoTaskJobId;

    /**
     * 必填 合成结果回调地址
     */
    @ApiModelProperty(value = "通用必填：合成结果回调地址", required = true)
    @NotEmpty(message = "合成结果回调地址必填")
    private String callbackUrl;

    /**
     * 必填 0音频 1文本
     */

    @ApiModelProperty(value = "通用必填：必填 0音频 1文本", required = true)
    @NotNull(message = "0音频 1文本 必填")
    private Integer type;


    /**
     * 合成的文本
     */
    @ApiModelProperty(value = "硅基：合成的文本 type为1 必填")
    private String text;

    //**************************************硅基必填参数**********************************************

    /**
     * 必填 场景ID，从模特列表接口获取
     */
    @ApiModelProperty(value = "硅基必填： 场景ID，从模特列表接口获取")
    private String sceneId;

    /**
     * 音频地址 与文本二选一
     */
    @ApiModelProperty(value = "硅基：音频地址 与文本二选一 type为0 必填")
    private String audioUrl;

    /**
     * 发音人id 合成文本时必填
     */
    @ApiModelProperty(value = "硅基：发音人id type为1 必填")
    private String speakerId;



    //**************************************腾讯云必填参数**********************************************

    /**
     * 必填 定义播报的⻆⾊、服装、姿态、分辨率等信息，参数为枚举值。
     * -- 统一使用sceneId
     */
    @ApiModelProperty(value = "腾讯云必填：定义播报的⻆⾊、服装、姿态、分辨率等信息，参数为枚举值")
    @Deprecated
    private String virtualmanKey;


    /**
     * 必填 语速（1.0为正常语速，范围[0.5-1.5]，值为0.5时播报语速最慢，值为1.5时播报语速最快，DriverType为⾳频驱动类型时，语速控制不⽣效）
     */
    @ApiModelProperty(value = "腾讯云必填： 语速")
    private Double speed;


    //**************************************硅基非必填参数**********************************************


    /**
     * 合成作品名
     */
    @ApiModelProperty(value = "硅基非必填：合成作品名")
    private String videoName;

    /**
     * 自定义背景，需公网可访问
     */
    @ApiModelProperty(value = "硅基非必填：自定义背景，需公网可访问")
    private String backgroundUrl;


    /**
     * 视频码率 可选值：1M、1.5M、3M、3.5M、5M、7M、8M、10M、12M、16M
     */
    @ApiModelProperty(value = "硅基非必填：视频码率 可选值：1M、1.5M、3M、3.5M、5M、7M、8M、10M、12M、16M")
    private String bitRate;
    /**
     * 横向分辨率（默认’1080’）
     */
    @ApiModelProperty(value = "硅基非必填：横向分辨率(默认’1080’)")
    private String width;
    /**
     * 纵向分辨率（默认’1920’）
     */
    @ApiModelProperty(value = "硅基非必填：纵向分辨率（默认’1920’）")
    private String height;
    /**
     * 是否启用mask ‘0’不起用 ’1’: 启用
     */
    @ApiModelProperty(value = "硅基非必填：是否启用mask ‘0’不起用 ’1’: 启用")
    private String mask;
    /**
     * fps 可选值："15"、 "20"、 "25"、 "30"
     */
    @ApiModelProperty(value = "硅基非必填：fps 可选值")
    private String fps;
    /**
     * 语速，取值区间：[0-1.0]
     */
    @ApiModelProperty(value = "硅基非必填：语速，取值区间：[0-1.0]")
    private String speechRate;
    /**
     * 是否生成字幕文件，0-不生成 1-生成
     */
    @ApiModelProperty(value = "硅基非必填：是否生成字幕文件，0-不生成 1-生成")
    private String srtFlag;
    /**
     * 是否抠图，0-不抠图 1-抠图；如果选择抠图，请同时设置‘backgroundUrl’值
     */
    @ApiModelProperty(value = "硅基非必填：是否抠图，0-不抠图 1-抠图；如果选择抠图，请同时设置‘backgroundUrl’值")
    private String matting;
    /**
     * 色彩度, 如‘yuv444p’
     */
    @ApiModelProperty(value = "硅基非必填：色彩度, 如‘yuv444p’")
    private String pixFmt;
    /**
     * 合成结果视频格式 可选值：mp4、webm，默认为mp4
     */
    @ApiModelProperty(value = "硅基非必填：合成结果视频格式 可选值：mp4、webm，默认为mp4")
    private String videoFormat;

    /**
     * 控制模特大小及位置(具体位置说明参考第四章位置说明, 注意：目前该功能只支持输出mp4格式视频)
     */
    @ApiModelProperty(value = "硅基非必填：控制模特大小及位置")
    private VideoStyleDTO style;

    /**
     * 在画面中添加素材(注意：目前该功能只支持输出mp4格式视频)
     */
    @ApiModelProperty(value = "硅基非必填：在画面中添加素材")
    private VideoNodesDTO nodes;


    //**************************************腾讯非必填参数**********************************************


    /**
     * ⾳⾊key，默认使⽤形象⾃有⾳⾊ - 腾讯云数字人不再需要该字段
     */
    @ApiModelProperty(value = "腾讯非必填：⾳⾊key，默认使⽤形象⾃有⾳⾊")
    @Deprecated
    private String timbreKey;

    /**
     * 视频输出格式，默认值：Mp4TransparentWebm:透明背景webm格式视频，⽀持部分微剪辑能⼒（主播参数⽀持）GreenScreenMp4:绿幕mp4格式视频，不⽀持微剪辑能⼒f:⽀持微剪辑能⼒的mp4格式视频
     */
    @ApiModelProperty(value = "腾讯非必填：视频输出格式")
    private String format;
    /**
     * 视频背景图⽚下载路径，⽀持jpg、png格式，图⽚分辨率需要与视频分辨率相同，不传默认为绿幕视频。
     */
    @ApiModelProperty(value = "腾讯非必填：视频背景图⽚下载路径")
    private String backgroundFileUrl;
    /**
     * ⽚头视频，⽀持mp4格式，分辨率需要与视频分辨率相同，⽂件⼤⼩限制200M
     */
    @ApiModelProperty(value = "腾讯非必填：⽚头视频")
    private String videoHeadFileUrl;
    /**
     * ⽚尾视频，⽀持mp4格式，分辨率需要与视频分辨率相同，⽂件⼤⼩限制200M
     */
    @ApiModelProperty(value = "腾讯非必填：⽚尾视频")
    private String videoTailFileUrl;
    /**
     * 是否在视频内显示字幕，默认不展示
     */
    @ApiModelProperty(value = "腾讯非必填：是否在视频内显示字幕，默认不展示")
    private Boolean showSubtitles;
    /**
     * 定义视频中logo相关参数
     */
    @ApiModelProperty(value = "腾讯非必填：定义视频中logo相关参数")
    private List<IvhLogoParamsDTO> logoParams;
    /**
     * 定义视频中主播相关参数
     */
    @ApiModelProperty(value = "腾讯非必填：定义视频中主播相关参数")
    private IvhAnchorDTO anchorParam;
    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道id")
    private Integer channel;

    /**
     * 语调，50正常语调（默认），0对应默认语速的1/2，100对应默认语速的2倍，最小值:0, 最大值:100
     */
    @ApiModelProperty(value = "语调，50正常语调（默认），0对应默认语速的1/2，100对应默认语速的2倍，最小值:0, 最大值:100")
    public String pitch;

    /**
     * 音量，50正常音量（默认），0是静音，1对应默认音量1/2，100对应默认音量的2倍，最小值:0, 最大值:100
     */
    @ApiModelProperty(value = "音量，50正常音量（默认），0是静音，1对应默认音量1/2，100对应默认音量的2倍，最小值:0, 最大值:100")
    public String volume;

    @ApiModelProperty(value = "是否使用自定义地址存储音频 0-否 1-是")
    private Integer customStoreUrl;
}