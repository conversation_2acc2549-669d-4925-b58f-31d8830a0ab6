package com.dl.aiservice.biz.client.deepsound.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName DsAudioTrainSource
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 18:17
 * @Version 1.0
 **/
@Data
public class DsTtsServiceConfig implements Serializable {

    private static final long serialVersionUID = -4213730735414879102L;

    /**
     * 服务版本号 固定为 1.0
     */
    @JsonProperty(value = "version", defaultValue = "1.0")
    private String version = "1.0";

    /**
     * 服务模式 固定为 cloud
     */
    @JsonProperty(value = "service-mode", defaultValue = "cloud")
    private String serviceMode = "cloud";

    /**
     * 请求超时时间 单位毫秒
     */
    @JsonProperty(value = "net-timeout", defaultValue = "10000")
    private Integer netTimeout = 10000;

}
