package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IvhServiceAssetInfo {

    /**
     * 服务资产类型
     */
    @JsonProperty(value = "ServiceType")
    private String serviceType;

    /**
     * 有效期
     */
    @JsonProperty(value = "ExpireDate")
    private String expireDate;

    @JsonProperty(value = "InteractConcurrencyInfoDetail")
    private InteractConcurrencyInfoDetail interactConcurrencyInfoDetail;

    /**
     * 播报小时包资产详情
     */
    @JsonProperty(value = "BroadcastHourInfoDetail")
    private BroadcastHourInfoDetail broadcastHourInfoDetail;

    /**
     * 播报并发数资产详情
     */
    @JsonProperty(value = "BroadcastConcurrencyInfoDetail")
    private BroadcastConcurrencyInfoDetail broadcastConcurrencyInfoDetail;

    /**
     * 形象资产详情
     */
    @JsonProperty(value = "ImageAssetInfoDetail")
    private ImageAssetInfoDetail imageAssetInfoDetail;

    /**
     * 音色资产详情
     */
    @JsonProperty(value = "TimbreAssetInfoDetail")
    private TimbreAssetInfoDetail timbreAssetInfoDetail;
}

/**
 * 交互并发资产详情
 */
@NoArgsConstructor
@Data
class InteractConcurrencyInfoDetail {

    /**
     * 并发数
     */
    @JsonProperty(value = "Concurrency")
    private int concurrency;

}

@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
class BroadcastHourInfoDetail {

    /**
     * 播报小时包时长
     */
    @JsonProperty(value = "OrderMilliseconds")
    private int orderMilliseconds;

    /**
     * 播报小时包剩余时长
     */
    @JsonProperty(value = "RemainMilliseconds")
    private int remainMilliseconds;
}

@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
class BroadcastConcurrencyInfoDetail {
    /**
     * 并发数
     */
    @JsonProperty(value = "Concurrency")
    private int concurrency;
}

@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
class ImageAssetInfoDetail {
    /**
     * 定制数量
     */
    @JsonProperty(value = "CustomizationNum")
    private int customizationNum;

    /**
     * 剩余数量
     */
    @JsonProperty(value = "RemainNum")
    private int remainNum;
}

@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
class TimbreAssetInfoDetail {
    /**
     * 定制数量
     */
    @JsonProperty(value = "CustomizationNum")
    private int customizationNum;

    /**
     * 剩余数量
     */
    @JsonProperty(value = "RemainNum")
    private int remainNum;
}