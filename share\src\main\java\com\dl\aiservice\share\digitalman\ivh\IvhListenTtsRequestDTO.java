package com.dl.aiservice.share.digitalman.ivh;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IvhListenTtsRequestDTO implements Serializable {

    private static final long serialVersionUID = -1301231693693196145L;
    /**
     * ⾳⾊key，默认使⽤形象⾃有⾳⾊
     */
    private String timbreKey;
    /**
     * 定义播报的⻆⾊、服装、姿态、分辨率等信息，参数为枚举值。
     */
    private String virtualmanKey;
    /**
     * 需要播报的⽂本内容，⽀持ssml标签，上限500个字
     */
    private String inputSsml;
    /**
     * 语速（1.0为正常语速，范围[0.5-1.5]，值为0.5时播报语速最慢，值为1.5时播报语速最快）
     */
    private Double speed = 1.0D;
    /**
     * 可传⼊含鉴权s3协议存储url，⾳频成品会上传⾄该url
     */
    private String audioStorageS3Url;

}
