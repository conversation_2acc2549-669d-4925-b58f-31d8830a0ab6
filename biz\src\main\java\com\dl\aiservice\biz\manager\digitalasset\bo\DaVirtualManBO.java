package com.dl.aiservice.biz.manager.digitalasset.bo;

import lombok.Data;

import java.util.Date;

/**
 * 数字资产-仿真人信息表
 */
@Data
public class DaVirtualManBO {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 数字人唯一标识
     */
    private Long bizId;

    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 数字人形象代码
     */
    private String vmCode;

    /**
     * 数字人名称
     */
    private String vmName;

    /**
     * 仿真人自有声音代码
     */
    private String vmVoiceKey;

    /**
     * 关联合成音/克隆音的声音代码
     */
    private Long voiceBizId;

    /**
     * 性别：1 男; 2 女
     */
    private Integer gender;

    /**
     * 数字人头像地址url
     */
    private String headImg;

    /**
     * 数智⼈类型：1 2d真⼈;2 3d真⼈
     */
    private Integer vmType;

    /**
     * 生效日期
     */
    private Date effectDt;

    /**
     * 仿真人失效期
     */
    private Date expiryDt;

    /**
     * 渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    private Integer channel;

    /**
     * 是否启用 0：否，1：是
     */
    private Integer isEnabled;

    /**
     * 是否删除 0：否，1：是
     */
    private Integer isDeleted;

    private Date createDt;

    private Date modifyDt;

    /**
     * 是否支持语速调节，0 否；1 是
     */
    private Integer isEnableSpeed;

    /**
     * 默认语速 1.0；范围 0.5 ~ 1.5
     */
    private Float defaultSpeed;

}