package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class IvhLogoParams {

    /**
     * logo图⽚⽂件下载路径，⽀持jpg、png格式
     */
    @JsonProperty(value = "logoFileUrl")
    public String logoFileUrl;
    /**
     * logo图⽚左上⻆的X坐标（坐标范围和视频分辨率相关）
     */
    @JsonProperty(value = "PositionX")
    public Integer positionX;
    /**
     * logo图⽚左上⻆的Y坐标（坐标范围和视频分辨率相关）
     */
    @JsonProperty(value = "PositionY")
    public Integer positionY;
    /**
     * logo图⽚缩放⽐例（1.0为原始图⽚⼤⼩）
     */
    @JsonProperty(value = "Scale")
    public Double scale;
}