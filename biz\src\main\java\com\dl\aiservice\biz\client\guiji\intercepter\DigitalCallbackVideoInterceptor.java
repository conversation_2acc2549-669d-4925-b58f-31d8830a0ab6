package com.dl.aiservice.biz.client.guiji.intercepter;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.dl.aiservice.biz.client.ivh.enums.IvhErrCodeEnum;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.biz.dal.po.CallbackLogPO;
import com.dl.aiservice.biz.manager.CallbackLogManager;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName DigitalMethodInterceptor
 * @Description 方法级别的拦截器
 * <AUTHOR>
 * @Date 2023/3/22 17:23
 * @Version 1.0
 **/
@Slf4j
public class DigitalCallbackVideoInterceptor implements Interceptor {


    @SneakyThrows
    @Override
    public boolean beforeExecute(ForestRequest request) {
        CallbackLogManager callbackLogManager = ApplicationContextUtils.getContext().getBean(CallbackLogManager.class);

        DigitalVideoCallbackDTO digitalVideoCallbackDTO = (DigitalVideoCallbackDTO) request.getArgument(Const.ONE);
        String callBackVO = (String) request.getArgument(Const.TWO);
        //触发回调
        CallbackLogPO callbackLog = new CallbackLogPO();
        callbackLog.setExtJobId(digitalVideoCallbackDTO.getExtJobId());
        callbackLog.setCallbackType(Const.ONE);
        callbackLog.setChannel(digitalVideoCallbackDTO.getChannel());
        callbackLog.setExtCallbackRespBody(callBackVO);
        callbackLog.setStatus(Const.ZERO);
        callbackLogManager.save(callbackLog);
        digitalVideoCallbackDTO.setCallbackId(callbackLog.getId());
        log.info("before execute:\nrequest: {}", request.getBody().nameValuesMapWithObject());
        return Boolean.TRUE;
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("after execute:\nrequest: {}\nresponse: {}", request.getBody().nameValuesMapWithObject(), response.getContent());
    }


    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        Interceptor.super.onSuccess(data, request, response);
        CallbackLogManager callbackLogManager = ApplicationContextUtils.getContext().getBean(CallbackLogManager.class);
        DigitalVideoCallbackDTO digitalVideoCallbackDTO = (DigitalVideoCallbackDTO) request.getArgument(Const.ONE);
        ResultModel callbackResult = JSONObject.parseObject(response.getContent(), ResultModel.class);
        CallbackLogPO callbackLog = new CallbackLogPO();
        callbackLog.setId(digitalVideoCallbackDTO.getCallbackId());
        if (callbackResult.isSuccess()) {
            callbackLog.setStatus(Const.ONE);
        } else {
            callbackLog.setStatus(Const.TWO);
        }

        log.info("业务回调信息：{}", callbackResult);
        callbackLog.setCallbackRespBody(JSONUtil.toJsonStr(callbackResult));
        callbackLogManager.updateById(callbackLog);

    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        Interceptor.super.onError(ex, request, response);
        DigitalVideoCallbackDTO digitalVideoCallbackDTO = (DigitalVideoCallbackDTO) request.getArgument(Const.ONE);
        CallbackLogManager callbackLogManager = ApplicationContextUtils.getContext().getBean(CallbackLogManager.class);
        CallbackLogPO callbackLog = new CallbackLogPO();
        callbackLog.setId(digitalVideoCallbackDTO.getCallbackId());
        callbackLog.setStatus(Const.TWO);
        callbackLogManager.updateById(callbackLog);
        log.error("回调业务异常", ex);
        throw BusinessServiceException.getInstance(IvhErrCodeEnum.UNKNOWN.getErrorCode().toString(), ex.getMessage());
    }

}
