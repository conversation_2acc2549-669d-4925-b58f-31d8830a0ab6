package com.dl.aiservice.share.digitalman.ivh;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class IvhListenTtsResponseDTO {

    /**
     * 腾讯云任务id
     */
    private String ivhTaskId;

    /**
     * ⾳视频结果地址
     */
    private String mediaUrl;
    /**
     * 制作状态"COMMIT"：已提交需要排队"MAKING"：制作中"SUCCESS"：制作成功"FAIL"：制作失败
     */
    private String status;
    /**
     * 制作失败返回的失败原因，便于排查问题
     */
    private String failMessage;

    /**
     * 制作失败返回的失败编码
     */
    private String failCode;

    /**
     * 字幕
     */
    private List<SentencesDTO> textTimestampResult;

}