package com.dl.aiservice.biz.client.guiji.convert;

import com.dl.aiservice.biz.client.guiji.resp.GJPageResponse;
import com.dl.framework.common.model.ResultPageModel;

import java.util.function.Function;
import java.util.stream.Collectors;

public interface PageConvertService {

    default <S, T> ResultPageModel<T> convert(GJPageResponse<S> pageResponse, Function<S, T> function) {
        ResultPageModel<T> pageResponseDTO = new ResultPageModel<>();
        pageResponseDTO.setPageIndex(pageResponse.getPageNo().longValue());
        pageResponseDTO.setPageSize(pageResponse.getPageSize().longValue());
        pageResponseDTO.setTotal(pageResponse.getTotalRecord().longValue());
        pageResponseDTO.setDataResult(pageResponse.getRecords().stream().map(function).collect(Collectors.toList()));
        return pageResponseDTO;
    }

}
