package com.dl.aiservice.share.voiceclone;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-06 17:33
 */
@Data
public class VoiceTrainJobDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    private Integer channel;

    /**
     * 训练类型：0 数字人训练；1 声音训练
     *
     * @See:com.dl.aiservice.biz.manager.train.enums.TrainTypeEnum
     */
    private Integer jobType;

    /**
     * 性别：1 男 ；2 女
     */
    private Integer gender;

    /**
     * 训练id 雪花算法
     */
    private Long trainJobId;

    /**
     * 第三方训练id
     */
    private String extJobId;

    /**
     * 第三方训练人模型编号
     */
    private String extModelCode;

    /**
     * 训练状态：1 训练中；0 训练完成；-1 训练失败
     */
    private Integer status;

    /**
     * 训练名称 深声：声讯编码
     */
    private String trainName;

    /**
     * 三方错误码
     */
    private String failCode;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 试听链接
     */
    private String sampleLink;

    /**
     * 训练来源，1-A端，2-D端
     */
    private Integer source;

    /**
     * 创建时间
     */
    private Date createDt;
}
