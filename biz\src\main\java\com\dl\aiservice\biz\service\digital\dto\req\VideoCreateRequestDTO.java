package com.dl.aiservice.biz.service.digital.dto.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@NoArgsConstructor
@Data
public class VideoCreateRequestDTO implements Serializable {
    private static final long serialVersionUID = -6727712564242004694L;
    /**
     * 音频地址，需公网可访问
     */
    private String audioUrl;
    /**
     * 场景ID，从模特列表接口获取
     */
    private String sceneId;
    /**
     * 自定义背景，需公网可访问
     */
    private String backgroundUrl;
    /**
     * 合成作品名称
     */
    private String videoName;
    /**
     * 视频码率 可选值：1M、1.5M、3M、3.5M、5M、7M、8M、10M、12M、16M
     */
    private String bitRate;
    /**
     * 横向分辨率（默认’1080’）
     */
    private String width;
    /**
     * 纵向分辨率（默认’1920’）
     */
    private String height;
    /**
     * 是否启用mask ‘0’不起用 ’1’: 启用
     */
    private String mask;
    /**
     * fps 可选值："15"、 "20"、 "25"、 "30"
     */
    private String fps;
    /**
     * 合成结果回调地址
     */
    private String callbackUrl;
    /**
     * 是否抠图，0-不抠图 1-抠图；如果选择抠图，请同时设置‘backgroundUrl’值
     */
    private String matting;
    /**
     * 色彩度, 如‘yuv444p’
     */
    private String pixFmt;
    /**
     * 合成结果视频格式 可选值：mp4、webm，默认为mp4
     */
    private String videoFormat;

    /**
     * 控制模特大小及位置(具体位置说明参考第四章位置说明, 注意：目前该功能只支持输出mp4格式视频)
     */
    private VideoStyleDTO style;

    /**
     * 在画面中添加素材(注意：目前该功能只支持输出mp4格式视频)
     */
    private VideoNodesDTO nodes;


}