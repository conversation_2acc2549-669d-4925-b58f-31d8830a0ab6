package com.dl.aiservice.share.voiceclone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class EnvCheckParamDTO implements Serializable {

    private static final long serialVersionUID = 6396820559007386813L;

    @ApiModelProperty(value = "可公网访问音频文件链接",required = true)
    @NotBlank(message = "音频文件url必填")
    private String url;
}
