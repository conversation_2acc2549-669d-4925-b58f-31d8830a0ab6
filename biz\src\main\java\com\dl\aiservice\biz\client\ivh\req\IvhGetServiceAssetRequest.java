package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IvhGetServiceAssetRequest implements Serializable {

    private static final long serialVersionUID = -2276146704803014832L;

    /**
     * 否
     * 服务资产类型，默认查询所有类型
     * InteractConcurrency: 交互并发
     * BroadcastHour: 播报小时包
     * BroadcastConcurrency: 播报并发
     * ImageAsset: 形象定制
     * TimbreAsset: 音色定制
     */
    @JsonProperty(value = "ServiceType")
    private String serviceType;

    @JsonProperty(value = "PageIndex")
    private int pageIndex = 1;

    /**
     * 页面大小（最大不超过100）
     */
    @JsonProperty(value = "PageSize")
    private int pageSize;

}
