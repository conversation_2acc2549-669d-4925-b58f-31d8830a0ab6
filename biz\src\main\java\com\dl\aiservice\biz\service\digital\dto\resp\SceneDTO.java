package com.dl.aiservice.biz.service.digital.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SceneDTO implements Serializable {

    private static final long serialVersionUID = -7489182217568683768L;
    /**
     * 场景id(重要，视频合成必填ID)
     */
    @ApiModelProperty(value = "硅基：场景id(重要，视频合成必填ID)")
    private Integer id;
    /**
     * 场景名称
     */
    @ApiModelProperty(value = "硅基：场景名称")
    private String sceneName;
    /**
     * 示例视频
     */
    @ApiModelProperty(value = "硅基：示例视频")
    private String exampleUrl;
    /**
     * 封面地址
     */
    @ApiModelProperty(value = "硅基：封面地址")
    private String coverUrl;
    /**
     * 示例图片
     */
    @ApiModelProperty(value = "硅基：示例图片")
    private String samplePictureUrl;

}