package com.dl.aiservice.biz.manager.subtitle.tencent.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-08-29 17:26
 */
public enum TencentAsrFormatEnum {

    WAV("wav", "wav"),
    OGG_OPUS("ogg-opus", "ogg-opus"),
    AAC("aac", "aac"),
    MP3("mp3", "mp3"),
    PCM("pcm", "pcm"),
    SPEEX("speex", "speex"),
    SILK("silk", "silk"),
    M4A("m4a", "m4a"),
    AMR("amr", "amr");

    /**
     * 音频编码格式。
     */
    private String format;

    /**
     * 文件后缀
     */
    private String suffix;

    TencentAsrFormatEnum(String format, String suffix) {
        this.format = format;
        this.suffix = suffix;
    }

    public String getFormat() {
        return format;
    }

    public String getSuffix() {
        return suffix;
    }

    public static TencentAsrFormatEnum parse(String suffix) {
        if (StringUtils.isBlank(suffix)) {
            return null;
        }
        for (TencentAsrFormatEnum formatEnum : TencentAsrFormatEnum.values()) {
            if (suffix.equals(formatEnum.getSuffix())) {
                return formatEnum;
            }
        }
        return null;
    }
}
