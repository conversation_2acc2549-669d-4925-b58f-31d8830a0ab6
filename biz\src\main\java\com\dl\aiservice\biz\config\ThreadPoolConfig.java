package com.dl.aiservice.biz.config;

import com.dl.aiservice.biz.common.constant.Const;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

    private static final Integer MAX_POOL_SIZE = 20;

    @Bean
    public ExecutorService ttsCloneVoiceTaskExecutor() {
        ExecutorService ttsCloneVoiceTaskExecutor =
                new ThreadPoolExecutor(Const.FIVE, MAX_POOL_SIZE, Const.TEN, TimeUnit.SECONDS,
                        new LinkedBlockingQueue<>(Const.ONE_HUNDRED),
                        new ThreadFactoryBuilder().setNameFormat("tts-clone-voice-pool-%d").build(),
                        new ThreadPoolExecutor.CallerRunsPolicy());
        return ttsCloneVoiceTaskExecutor;
    }

    @Bean
    public ExecutorService syncVideoGenExecutor() {
        ExecutorService syncVideoGenExecutor =
                new ThreadPoolExecutor(Const.ONE, MAX_POOL_SIZE, Const.TWO * Const.TEN, TimeUnit.SECONDS,
                        new LinkedBlockingQueue<>(Const.ONE_HUNDRED),
                        new ThreadFactoryBuilder().setNameFormat("sync-video-gen-pool-%d").build(),
                        new ThreadPoolExecutor.CallerRunsPolicy());
        return syncVideoGenExecutor;
    }

    @Bean
    public ExecutorService syncVirtualManVideoGenExecutor() {
        ExecutorService syncVideoGenExecutor =
                new ThreadPoolExecutor(Const.FIVE, MAX_POOL_SIZE, Const.TWO * Const.TEN, TimeUnit.SECONDS,
                        new LinkedBlockingQueue<>(Const.ONE_HUNDRED),
                        new ThreadFactoryBuilder().setNameFormat("sync-vm-video-gen-pool-%d").build(),
                        new ThreadPoolExecutor.CallerRunsPolicy());
        return syncVideoGenExecutor;
    }
}
