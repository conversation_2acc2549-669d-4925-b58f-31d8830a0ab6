package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class IvhSmallSampleVirtualMan {

    /**
     * 数智⼈virtualmanKey
     */
    @JsonProperty(value = "VirtualmanKey")
    private String virtualManKey;
    /**
     * 主播名称
     */
    @JsonProperty(value = "AnchorName")
    private String anchorName;
    /**
     * 主播code
     */
    @JsonProperty(value = "AnchorCode")
    private String anchorCode;
    /**
     * 数智⼈头像图⽚url
     */
    @JsonProperty(value = "HeaderImage")
    private String headerImage;
    /**
     * 数智⼈⽀持的驱动类型 1. Text: ⽂本驱动 2. OriginalVoice: 原声⾳频驱动 3. ModulatedVoice: 变声⾳频驱动
     */
    @JsonProperty(value = "SupportDriverTypes")
    private List<String> supportDriverTypes;

    /**
     * 数智⼈服装
     */
    @JsonProperty(value = "ClothesName")
    private String clothesName;

    /**
     * 数智⼈姿态
     */
    @JsonProperty(value = "PoseName")
    private String poseName;

    /**
     * 数智⼈分辨率
     */
    @JsonProperty(value = "Resolution")
    private String resolution;

    /**
     * 数智⼈姿态图⽚url
     */
    @JsonProperty(value = "PoseImage")
    private String poseImage;

    /**
     * 数智⼈服装图⽚url
     */
    @JsonProperty(value = "ClothesImage")
    private String clothesImage;

    /**
     * 失效时间戳："2023-04-01 00:00:00"
     */
    @JsonProperty(value = "ExpireDate")
    private String expireDate;

}