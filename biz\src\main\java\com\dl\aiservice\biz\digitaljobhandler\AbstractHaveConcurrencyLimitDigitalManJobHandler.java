package com.dl.aiservice.biz.digitaljobhandler;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.RedisUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.share.enums.MediaProduceJobStatusEnum;
import com.dl.framework.common.utils.JsonUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 有并发限制的数字人任务处理器抽象类
 * <p>
 * 这里的"有并发限制"指的是我方需要做并发限制，需要控制提交给数字人厂商的任务。（因为数字人厂商内部无队列，超过他给我方开的并发路数的任务是直接拒绝的）
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-12 15:58
 */
@Component
public abstract class AbstractHaveConcurrencyLimitDigitalManJobHandler extends AbstractDigitalManJobHandler {
    private static final Logger LOGGER = LoggerFactory
            .getLogger(AbstractHaveConcurrencyLimitDigitalManJobHandler.class);

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;

    /**
     * 获取分布式锁key
     *
     * @return
     */
    abstract String getHandlerLockKey();

    /**
     * 获取分布式锁超时时间
     *
     * @return
     */
    abstract long getHandlerLockTimeout();

    /**
     * 获取并发限制
     *
     * @return
     */
    abstract int getConcurrency();

    /**
     * 上分布式锁
     * 目的：确保单位时间内只有一个处理器在处理
     *
     * @return
     */
    protected boolean lockHandler() {
        return redisUtil.tryLockAndSetTimeout(getHandlerLockKey(), getHandlerLockTimeout());
    }

    /**
     * 处理数字人视频合成。
     */
    @Override
    public void handleDmVideoCreate() {
        //1.上分布式锁。目的：确保单位时间内只有一个处理器在处理
        if (!lockHandler()) {
            LOGGER.info("当前厂商:{},数字人任务处理器，未抢到分布式锁。", supportChannel().getCode());
            return;
        }

        //2.查询60分钟内合成中的数字人任务数量
        Integer processingCount = mediaProduceJobManager.lambdaQuery()
                .eq(MediaProduceJobPO::getChannel, this.supportChannel().getCode())
                .eq(MediaProduceJobPO::getJobType, Const.ONE)
                .eq(MediaProduceJobPO::getStatus, MediaProduceJobStatusEnum.ING.getStatus())
                .ge(MediaProduceJobPO::getModifyDt, DateUtils.addMinutes(new Date(), -60)).count();
        //合成中的数量超过并发数
        if (processingCount >= getConcurrency()) {
            LOGGER.warn("当前厂商:{},数字人合成中的数量:{},超过并发数:{}", supportChannel().getCode(), processingCount, getConcurrency());
            return;
        }

        //3.从队列中获取数字人合成请求
        CreateRequestDTO dmRequestDTO = super.popFromRedis();
        if (Objects.isNull(dmRequestDTO)) {
            LOGGER.info("当前厂商:{},数字人任务队列中暂无合成请求。", supportChannel().getCode());
            return;
        }
        LOGGER.info("获取到数字人合成请求dmRequestDTO:{}", JSONUtil.toJsonStr(dmRequestDTO));

        //4.校验数字人任务状态，非待合成状态不处理
        MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.lambdaQuery()
                .eq(MediaProduceJobPO::getId, dmRequestDTO.getUpdateId()).one();
        if (!Objects.equals(mediaProduceJobPO.getStatus(), MediaProduceJobStatusEnum.WAITING.getStatus())) {
            LOGGER.warn("当前厂商:{},数字人合成任务非待合成状态，不处理。dmRequestDTO={}", supportChannel().getCode(),
                    JsonUtils.toJSON(dmRequestDTO));
            return;
        }

        //5.合成数字人视频
        super.doDigitalManVideoCreate(dmRequestDTO);
    }
}
