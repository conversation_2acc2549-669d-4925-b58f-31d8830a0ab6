package com.dl.aiservice.biz.digitaljobhandler;

import com.dl.aiservice.share.enums.ServiceChannelEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 孚嘉科技的腾讯云数字人任务处理器
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-12 16:22
 */
@Component
public class FuJiaIvhDigitalManJobHandler extends AbstractHaveConcurrencyLimitDigitalManJobHandler {

    /**
     * 任务队列的key
     */
    private static final String FUJIA_IVH_DM_JOB_QUEUE_KEY = "Fujia_Ivh_DM_Job_Queue";

    /**
     * 分布式锁的key
     */
    private static final String FUJIA_IVH_DM_JOB_HANDLER_LOCK_KEY = "Fujia_Ivh_Dm_Job_Handler_Lock";

    /**
     * 分布式锁的失效时间 单位：秒
     */
    private static final long FUJIA_IVH_DM_JOB_HANDLER_LOCK_TIMEOUT = 4;

    @Value("${digtal.fujia.ivh.video.concurrency}")
    private Integer fujiaIvhConcurrency;

    @Override
    public ServiceChannelEnum supportChannel() {
        return ServiceChannelEnum.FUJIA_IVH;
    }

    @Override
    public String getQueueKey() {
        return FUJIA_IVH_DM_JOB_QUEUE_KEY;
    }

    @Override
    String getHandlerLockKey() {
        return FUJIA_IVH_DM_JOB_HANDLER_LOCK_KEY;
    }

    @Override
    long getHandlerLockTimeout() {
        return FUJIA_IVH_DM_JOB_HANDLER_LOCK_TIMEOUT;
    }

    @Override
    int getConcurrency() {
        return fujiaIvhConcurrency;
    }

}
