package com.dl.aiservice.biz.service.digital.bytedance.impl;

import com.dl.aiservice.biz.client.bytedance.ByteDanceDigitalClient;
import com.dl.aiservice.biz.client.bytedance.req.ByteDanceDmQueryRequest;
import com.dl.aiservice.biz.client.bytedance.req.ByteDanceDmSubmitRequest;
import com.dl.aiservice.biz.client.bytedance.resp.ByteDanceDmQueryResponse;
import com.dl.aiservice.biz.client.bytedance.resp.ByteDanceDmSubmitResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.biz.service.digital.AbstractDigitalService;
import com.dl.aiservice.biz.service.digital.bytedance.ByteDanceDigitalService;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateTrainingRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.RobotDetailRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TrainRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateTrainingResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotDetailResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.TrainResponseDTO;
import com.dl.aiservice.biz.service.digital.enums.SynthesisStatusEnum;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.model.ResultPageModel;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @describe: ByteDanceDigitalServiceImpl
 * @author: zhousx
 * @date: 2023/10/8 14:03
 */
@Slf4j
@Service
public class ByteDanceDigitalServiceImpl extends AbstractDigitalService implements ByteDanceDigitalService {
    private static final String CALL_BACK_URL = "%s/bytedance/callback";
    @Value("${digtal.bytedance.appId}")
    private String appId;
    @Value("${digtal.bytedance.accessToken}")
    private String accessToken;
    @Resource
    private ByteDanceDigitalClient byteDanceDigitalClient;
    @Resource
    private AiConfig aiConfig;

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.VOLC_ENGINE);
    }

    @Override
    public CreateResponseDTO videoCreate(CreateRequestDTO videoCreateRequestDTO) {
        ByteDanceDmSubmitRequest request = new ByteDanceDmSubmitRequest();
        request.setCallbackUrl(String.format(CALL_BACK_URL, getCallbackPrefix()));
        request.setAppId(appId);
        request.setAccessToken(accessToken);
        request.setText(videoCreateRequestDTO.getText());
        request.setRole(videoCreateRequestDTO.getSceneId());
        ByteDanceDmSubmitResponse response = byteDanceDigitalClient.submit(request);
        CreateResponseDTO responseDTO = new CreateResponseDTO();
        if(Objects.nonNull(response) && response.isSuccess()) {
            responseDTO.setTaskId(response.getData().getTaskId());
        }
        return responseDTO;
    }

    @Override
    public ResultPageModel<RobotResponseDTO> robotPageList(PageRequestDTO pageRequestDTO) {
        return null;
    }

    @Override
    public RobotDetailResponseDTO robotDetail(RobotDetailRequestDTO requestDTO) {
        return null;
    }

    @Override
    public TrainResponseDTO getTrain(TrainRequestDTO gjTrainRequestDTO) {
        return null;
    }

    @Override
    public ProgressResponseDTO getProgress(TaskRequestDTO taskRequestDTO) {
        ByteDanceDmQueryRequest request = new ByteDanceDmQueryRequest();
        request.setTaskId(taskRequestDTO.getTaskId());
        ByteDanceDmQueryResponse resp = byteDanceDigitalClient.query(request);
        ProgressResponseDTO respDTO = new ProgressResponseDTO();
        if (resp.isSuccess()) {
            ByteDanceDmQueryResponse.RespData respData = resp.getData();
            respDTO.setSynthesisStatus(Objects.equals(respData.getTaskStatus(), Const.ONE)? SynthesisStatusEnum.SUCCESS.getCode():SynthesisStatusEnum.FAIL.getCode());
            respDTO.setVideoUrl(respData.getVideoUrl());
            respDTO.setProgress(respData.getTaskStatus());
            respDTO.setFailMessage(respData.getFailureReason());
            if (Objects.nonNull(respData.getDuration())) {
                BigDecimal durationDecimal = new BigDecimal(respData.getDuration());
                respDTO.setDuration(durationDecimal.divide(new BigDecimal(1000)).toString());
            }
        }
        return respDTO;
    }

    @Override
    public CreateTrainingResponseDTO createTraining(CreateTrainingRequestDTO request) {
        return null;
    }

    private String getCallbackPrefix() {
        String callback = aiConfig.getCallbackPrefix();
        if (StringUtils.lastIndexOf(callback, Const.SLASH) == (callback.length() - Const.ONE)) {
            return StringUtils.substring(callback, Const.ZERO, callback.length() - Const.ONE);
        }
        return callback;
    }
}
