package com.dl.aiservice.web.controller.delayq;

import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

@Slf4j
public final class BatchAuthTask<E> implements Delayed {

    private String name;
    private long startTime; // 任务开始时间
    private final Consumer<E> s;

    public BatchAuthTask(Consumer<E> s, String name, long startTime) {
        this.startTime = startTime;
        this.name = name;
        this.s = s;
    }

    @Override
    public long getDelay(@NotNull TimeUnit unit) {
        long diff = startTime - System.currentTimeMillis();
        return unit.convert(diff, TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(@NotNull Delayed o) {
        return Long.compare(this.startTime, ((BatchAuthTask) o).startTime);
    }

    public void doUpload() {
        log.info("BatchAuthTask thread>>" + name + ">>" + System.currentTimeMillis());
        s.accept(null);
    }
}