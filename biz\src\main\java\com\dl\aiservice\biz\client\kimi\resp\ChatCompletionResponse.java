package com.dl.aiservice.biz.client.kimi.resp;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 10:43
 */
@Data
public class ChatCompletionResponse {

    /**
     * 请求id
     */
    private String id;
    /**
     *
     */
    private String object;
    /**
     * 请求时间的秒级时间戳，如1698999496
     */
    private long created;
    /**
     * 对应ChatCompletionRequest的model
     */
    private String model;
    /**
     * 生成的结果列表  取决于ChatCompletionRequest中的n
     */
    private List<ChatCompletionChoice> choices;
    /**
     * 使用情况
     */
    private Usage usage;

}
