package com.dl.aiservice.biz.manager.subtitle.aliyun.convert;

import com.dl.aiservice.biz.client.aliyun.resp.AliyunFlashAsrReult;
import com.dl.aiservice.biz.client.aliyun.resp.AliyunFlashAsrSentences;
import com.dl.aiservice.biz.client.aliyun.resp.AliyunFlashAsrWords;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleDTO;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleWordsDTO;
import com.dl.aiservice.share.subtitle.dto.ReviseAsrRequestDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-31 17:46
 */
public class AliyunAsrConvert {

    public static List<AsrSubtitleDTO> buildAsrSubtitleDTOList(AliyunFlashAsrReult aliyunFlashAsrReult) {
        List<AliyunFlashAsrSentences> inputList = aliyunFlashAsrReult.getSentences();
        List<AsrSubtitleDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(inputList)) {
            return resultList;
        }
        //找出最后一个句子
        AliyunFlashAsrSentences lastSentences = inputList.stream()
                .max(Comparator.comparing(AliyunFlashAsrSentences::getEndTime)).get();

        resultList = inputList.stream().map(input -> {
            AsrSubtitleDTO result = new AsrSubtitleDTO();
            result.setSubtitle(input.getText());
            result.setTimePointStart(input.getBeginTime());
            //最后一个句子的结束时间取整个音频的时长
            if (input.getBeginTime() == lastSentences.getBeginTime()) {
                result.setTimePointEnd(aliyunFlashAsrReult.getDuration());
            } else {
                result.setTimePointEnd(input.getEndTime());
            }

            result.setWords(buildAsrSubtitleWordsDTOList(input.getWords()));
            return result;
        }).collect(Collectors.toList());
        return resultList;
    }

    public static List<AsrSubtitleWordsDTO> buildAsrSubtitleWordsDTOList(List<AliyunFlashAsrWords> inputWordsList) {
        if (CollectionUtils.isEmpty(inputWordsList)) {
            return Collections.emptyList();
        }

        return inputWordsList.stream().map(input -> {
            AsrSubtitleWordsDTO result = new AsrSubtitleWordsDTO();
            result.setWord(input.getText());
            result.setTimePointStart(input.getBeginTime());
            result.setTimePointEnd(input.getEndTime());
            return result;
        }).collect(Collectors.toList());
    }

    public static ReviseAsrRequestDTO buildReviseAsrRequestDTO(String originalScript,
            List<AsrSubtitleDTO> asrSubtitleDTOS) {
        ReviseAsrRequestDTO reviseAsrRequestDTO = new ReviseAsrRequestDTO();
        reviseAsrRequestDTO.setOriginalScript(originalScript);
        reviseAsrRequestDTO.setSubtitles(asrSubtitleDTOS);
        return reviseAsrRequestDTO;
    }
}
