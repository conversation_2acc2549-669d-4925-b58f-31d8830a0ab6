package com.dl.aiservice.biz.manager.voiceclone.grammartrans.impl.guiji;

import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-07 15:17
 */
public class NumberToNumberConvertor {
    private static final String[] NUMBER_WORDS = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九" };

    public static String convertNumberToWord(double number) {
        DecimalFormat decimalFormat = new DecimalFormat();
        String formattedNumber = decimalFormat.format(number);

        StringBuilder result = new StringBuilder();

        // 处理整数部分
        long integerPart = (long) number;
        if (integerPart == 0) {
            result.append(NUMBER_WORDS[0]);  // 如果整数部分为0，则直接添加"零"
        } else {
            result.append(convertIntegerToWord(integerPart));
        }

        if (formattedNumber.contains(".")) {
            result.append("点");

            // 处理小数部分
            String decimalPart = formattedNumber.substring(formattedNumber.indexOf('.') + 1);
            result.append(convertDecimalToWord(decimalPart));
        }

        return result.toString();
    }

    private static String convertIntegerToWord(long number) {
        StringBuilder result = new StringBuilder();

        if (number < 0) {
            result.append("负");
            number *= -1;  // 取绝对值处理
        }

        String numberString = Long.toString(number);
        for (int i = 0; i < numberString.length(); i++) {
            int digit = Character.getNumericValue(numberString.charAt(i));
            result.append(NUMBER_WORDS[digit]);
        }

        return result.toString();
    }

    private static String convertDecimalToWord(String decimalPart) {
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < decimalPart.length(); i++) {
            int digit = Character.getNumericValue(decimalPart.charAt(i));
            result.append(NUMBER_WORDS[digit]);
        }

        return result.toString();
    }

    public static void main(String[] args) {
        double number1 = 123;
        System.out.println("输入：" + number1 + "，输出：" + convertNumberToWord(number1));

        double number2 = 10.11;
        System.out.println("输入：" + number2 + "，输出：" + convertNumberToWord(number2));

        double number3 = 987654321.09;
        System.out.println("输入：" + number3 + "，输出：" + convertNumberToWord(number3));

        System.out.println("输入：100，输出：" + convertNumberToWord(100));
        System.out.println("输入：1000，输出：" + convertNumberToWord(1000));
        System.out.println("输入：10000，输出：" + convertNumberToWord(10000));
        System.out.println("输入：100000，输出：" + convertNumberToWord(100000));
        System.out.println("输入：1000000，输出：" + convertNumberToWord(1000000));

        System.out.println("输入：1000009，输出：" + convertNumberToWord(1000009));
        System.out.println("输入：1000080，输出：" + convertNumberToWord(1000080));
        System.out.println("输入：1000700，输出：" + convertNumberToWord(1000700));
        System.out.println("输入：1006000，输出：" + convertNumberToWord(1006000));
        System.out.println("输入：1986000，输出：" + convertNumberToWord(1986000));
        System.out.println("输入：1050000，输出：" + convertNumberToWord(1050000));
        System.out.println("输入：1400000，输出：" + convertNumberToWord(1400000));
        System.out.println("输入：921.98123，输出：" + convertNumberToWord(921.98123));

        System.out.println("输入：50，输出：" + convertNumberToWord(50));

    }
}
