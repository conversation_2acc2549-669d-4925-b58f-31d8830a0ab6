package com.dl.aiservice.biz.client.bytedance.resp;

import com.dl.aiservice.biz.common.constant.Const;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Objects;

/**
 * @describe: ByteDanceDmSubmitResponse
 * @author: zhousx
 * @date: 2023/10/8 14:34
 */
@Data
public class ByteDanceDmSubmitResponse {
    public boolean isSuccess() {
        return Objects.equals(Const.ZERO, code);
    }
    private Integer code;

    private String message;

    private String logid;

    private RespData data;

    @Data
    public static class RespData {
        @JsonProperty("task_id")
        private String taskId;
    }
}
