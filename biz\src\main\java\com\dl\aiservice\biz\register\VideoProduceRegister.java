package com.dl.aiservice.biz.register;

import com.dl.aiservice.biz.manager.videoproduce.VideoProduceHandleManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class VideoProduceRegister implements BeanFactoryPostProcessor {

    private ConfigurableListableBeanFactory beanFactory;

    // 声纹克隆接口
    private static final Map<ServiceChannelEnum, VideoProduceHandleManager> VIDEO_PRODUCE_API = new ConcurrentHashMap<>();

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    void init() {
        String[] videoProduceApi = this.beanFactory.getBeanNamesForType(VideoProduceHandleManager.class);
        if (videoProduceApi.length != 0) {
            for (String sendApi : videoProduceApi) {
                register((VideoProduceHandleManager) beanFactory.getBean(sendApi));
            }
        }
    }

    private void register(VideoProduceHandleManager videoProduceHandleManger) {
        if (Objects.nonNull(videoProduceHandleManger)) {
            Assert.notNull(videoProduceHandleManger.getEnum(),
                    "enum() cannot be empty beanName:" + videoProduceHandleManger.getClass().getName());
            VIDEO_PRODUCE_API.put(videoProduceHandleManger.getEnum(), videoProduceHandleManger);
        }
    }

    VideoProduceHandleManager get(ServiceChannelEnum typeEnum) {
        return VIDEO_PRODUCE_API.get(typeEnum);
    }
}
