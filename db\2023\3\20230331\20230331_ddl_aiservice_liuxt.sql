-- 结构化存储数字人信息
CREATE TABLE `digital_man_list` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `digital_man_id` bigint NOT NULL COMMENT '数字人唯一标识',
  `train_job_id` bigint NULL COMMENT '训练job_id 雪花算法',
  `tenant_code` varchar(100) NOT NULL COMMENT '租户代码',
  `dg_avatar` varchar(100) NOT NULL COMMENT '数字人形象代码',
  `dg_name` varchar(100) NULL DEFAULT '' COMMENT '数字人名称',
  `dg_per` varchar(100) NULL DEFAULT '' COMMENT '仿真人声音代码',
  `gender` tinyint(1) NOT NULL COMMENT '性别：1 男; 2 女',
  `validity_start_dt` datetime NULL COMMENT '仿真人有效期开始',
  `validity_end_dt` datetime NULL COMMENT '仿真人有效期结束',
  `channel` tinyint(1) NOT NULL COMMENT '渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云',
  `remark` varchar(100) NULL DEFAULT '' COMMENT '备注',
  `use_status` tinyint(1) NULL DEFAULT 0 COMMENT '状态：0 正常; 1 失效',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  INDEX `idx_tcode_dmid`(`tenant_code`, `digital_man_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='数字人列表';

CREATE TABLE `digital_man_scene_list`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `digital_man_id` bigint NOT NULL COMMENT '数字人唯一标识',
  `scene_id` varchar(100) NOT NULL COMMENT '场景id(重要，视频合成必填ID)',
  `scene_name` varchar(100) NULL COMMENT '场景名称',
  `cover_url` text NULL COMMENT '场景封面地址',
  `example_url` text NULL COMMENT '场景样例视频地址',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '修改时间',
  `scene_status` tinyint(1) NULL DEFAULT 0 COMMENT '状态：0 正常; 1 失效',
  PRIMARY KEY (`id`),
  INDEX `idx_dg_sc_man_id`(`digital_man_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='数字人关联场景信息表';
