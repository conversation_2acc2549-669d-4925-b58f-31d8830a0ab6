package com.dl.aiservice.web.controller.digital.bytedance.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @describe: ByteDanceCallbackVO
 * @author: zhousx
 * @date: 2023/10/8 15:58
 */
@Data
public class ByteDanceCallbackVO {
    @JsonProperty("task_id")
    private String taskId;

    @JsonProperty("task_status")
    private Integer taskStatus;

    @JsonProperty("video_url")
    private String videoUrl;

    @JsonProperty("failure_reason")
    private String failureReason;

    @JsonProperty("video_duration")
    private Long duration;
}
