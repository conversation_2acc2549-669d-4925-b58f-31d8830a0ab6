package com.dl.aiservice.biz.manager.voiceclone.deepsound.impl;

import cn.hutool.core.codec.Base64;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.client.deepsound.DeepSoundClient;
import com.dl.aiservice.biz.client.deepsound.enums.DsEvnCheckErrCodeEnum;
import com.dl.aiservice.biz.client.deepsound.req.DsAudioTrainRequest;
import com.dl.aiservice.biz.client.deepsound.req.DsAudioTrainSource;
import com.dl.aiservice.biz.client.deepsound.req.DsTtsAudioConfig;
import com.dl.aiservice.biz.client.deepsound.req.DsTtsInput;
import com.dl.aiservice.biz.client.deepsound.req.DsTtsRequest;
import com.dl.aiservice.biz.client.deepsound.req.DsTtsServiceConfig;
import com.dl.aiservice.biz.client.deepsound.req.DsTtsVoice;
import com.dl.aiservice.biz.client.deepsound.resp.DsAudioCheckResponse;
import com.dl.aiservice.biz.client.deepsound.resp.DsAudioTrainResponse;
import com.dl.aiservice.biz.client.deepsound.resp.DsBaseResponse;
import com.dl.aiservice.biz.client.deepsound.resp.DsDiffInfo;
import com.dl.aiservice.biz.client.deepsound.resp.DsQueryAudioTrainResponse;
import com.dl.aiservice.biz.client.deepsound.resp.DsTtsResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.Md5Util;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.train.TrainJobManager;
import com.dl.aiservice.biz.manager.voiceclone.VoiceCloneHandlerManager;
import com.dl.aiservice.biz.manager.voiceclone.grammartrans.impl.deepsound.DeepSoundGrammarTransformer;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.AudioCheckDifDTO;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainSourceDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DeepSoundHandlerManagerImpl implements VoiceCloneHandlerManager {

    private static final String CALL_BACK_URL = "%s/voice/clone/callback/%d";

    @Resource
    private DeepSoundClient deepSoundClient;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private TrainJobManager trainJobManager;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private AiConfig aiConfig;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private DeepSoundGrammarTransformer deepSoundGrammarTransformer;

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.DEEP_SOUND);
    }

    @Override
    public ResultModel envCheck(String url) {
        DsBaseResponse resp = deepSoundClient.envCheck(url);
        if (resp.isSuccess()) {
            return ResultModel.success(null);
        }
        return ResultModel.error(resp.getCode().toString(), resp.getErrMsg());
    }

    @Override
    public ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language) {
        DsAudioCheckResponse resp = deepSoundClient.audioCheck(url, text, language);
        if (resp.isSuccess()) {
            return ResultModel.success(null);
        }
        AudioCheckResponseDTO result = new AudioCheckResponseDTO();
        result.setAsrText(resp.getAsrText());
        if (CollectionUtils.isNotEmpty(resp.getDiffInfo())) {
            result.setDiffInfoList(resp.getDiffInfo().stream().map(this::convert).collect(Collectors.toList()));
        }
        ResultModel model = ResultModel.error(resp.getCode().toString(), resp.getErrMsg());
        model.setDataResult(result);
        return model;
    }

    private AudioCheckDifDTO convert(DsDiffInfo source) {
        AudioCheckDifDTO dsDiffInfoDTO = new AudioCheckDifDTO();
        dsDiffInfoDTO.setPos(source.getPos());
        dsDiffInfoDTO.setText(source.getText());
        dsDiffInfoDTO.setType(source.getType());
        return dsDiffInfoDTO;
    }

    @Override
    public ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request) {
        Assert.notNull(request, "入参不能为空");
        Assert.notEmpty(request.getSources(), "请输入录音文本及其录音链接");
        Assert.isTrue(request.getSources().size() == (Const.TWO * Const.TEN), "训练模型录音必须满20条");
        TrainJobPO trainJob = saveTrainJob(request);
        DsAudioTrainRequest trainRequest = convert(request);
        trainRequest.setRecordId(String.valueOf(trainJob.getTrainJobId()));
        trainRequest.setNotifyUrl(
                String.format(CALL_BACK_URL, getCallbackPrefix(), ServiceChannelEnum.DEEP_SOUND.getCode()));
        DsAudioTrainResponse resp = deepSoundClient.audioTrain(trainRequest);
        AudioTrainResponseDTO result = new AudioTrainResponseDTO();
        if (resp.isSuccess()) {
            result.setTrainJobId(resp.getData().getRecordId());
            return ResultModel.success(result);
        }
        return ResultModel.error(resp.getCode().toString(), resp.getErrMsg());
    }

    private String getCallbackPrefix() {
        String callback = aiConfig.getCallbackPrefix();
        if (StringUtils.lastIndexOf(callback, Const.SLASH) == (callback.length() - Const.ONE)) {
            return StringUtils.substring(callback, Const.ZERO, callback.length() - Const.ONE);
        }
        return callback;
    }

    private TrainJobPO saveTrainJob(AudioTrainParamDTO request) {
        TrainJobPO job = new TrainJobPO();
        job.setTrainJobId(hostTimeIdg.generateId().longValue());
        job.setTenantCode(channelUtil.getTenantCode());
        job.setChannel(ServiceChannelEnum.DEEP_SOUND.getCode());
        job.setCallbackUrl(request.getNotifyUrl());
        job.setJobType(Const.ONE);
        job.setStatus(Const.ONE);
        job.setGender(request.getGender());
        job.setTrainName(request.getSpeaker());
        trainJobManager.save(job);
        return job;
    }

    private DsAudioTrainRequest convert(AudioTrainParamDTO source) {
        DsAudioTrainRequest dsAudioTrainRequest = new DsAudioTrainRequest();
        dsAudioTrainRequest.setSpeaker(source.getSpeaker());
        dsAudioTrainRequest.setGender(source.getGender());
        String language = source.getLanguage();
        if (StringUtils.isNotBlank(language)) {
            dsAudioTrainRequest.setLanguage(language);
        }
        List<AudioTrainSourceDTO> sources = source.getSources();
        dsAudioTrainRequest.setSources(sources.stream().map(x -> {
            DsAudioTrainSource s = new DsAudioTrainSource();
            s.setText(x.getText());
            s.setLink(x.getLink());
            return s;
        }).collect(Collectors.toList()));
        dsAudioTrainRequest.setNotifyUrl(source.getNotifyUrl());
        return dsAudioTrainRequest;
    }

    @Override
    public ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId) {
        DsQueryAudioTrainResponse resp = deepSoundClient.queryAudioTrain(recordId);
        AudioTrainDetailResponseDTO result = new AudioTrainDetailResponseDTO();
        if (DsEvnCheckErrCodeEnum.ERROR_CODE_0.getErrorCode().equals(resp.getErrorCode())) {
            result.setTrainJobId(resp.getRecordId());
            result.setStatus(resp.getStatus());
            result.setStage(resp.getStage());
            result.setVoiceName(resp.getVoiceName());
            result.setEstimatedStartTime(resp.getEstimatedStartTime());
            result.setEstimatedFinishTime(resp.getEstimatedFinishTime());
            return ResultModel.success(result);
        }
        return ResultModel.error(resp.getCode().toString(), resp.getErrMsg());
    }

    @Override
    public ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO request) {
        Assert.notNull(request, "入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getVoiceName()), "voiceName入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getText()), "音频文本不能为空");
        String now = String.valueOf(System.currentTimeMillis() / Const.ONE_HUNDRED / Const.TEN);
        //语法转换
        String text = deepSoundGrammarTransformer.grammarTransform(request.getText());
        DsTtsRequest dsTtsRequest = getDsTtsRequest(request, text);
        Map<String, Object> headerMap = Maps.newHashMap();
        headerMap.put(DeepSoundClient.HEADER_X_CURTIME, now);
        headerMap.put(DeepSoundClient.HEADER_X_CHECKSUM, checksum(now, dsTtsRequest));
        headerMap.put(DeepSoundClient.HEADER_MEDIA_BIZ_ID, request.getWorksBizId());
        headerMap.put(DeepSoundClient.HEADER_TASK_JOB_ID, request.getVideoTaskJobId());
        headerMap.put(DeepSoundClient.HEADER_TENANT_CODE, channelUtil.getTenantCode());
        DsTtsResponse resp = deepSoundClient.tts(headerMap, dsTtsRequest);
        TTSResponseDTO respDto = new TTSResponseDTO();
        if (resp.isSuccess()) {
            MediaProduceJobPO mediaProduceJob = mediaProduceJobManager
                    .getOne(Wrappers.lambdaQuery(MediaProduceJobPO.class)
                            .eq(MediaProduceJobPO::getWorksBizId, request.getWorksBizId())
                            .eq(MediaProduceJobPO::getExtJobId, resp.getSid())
                            .eq(MediaProduceJobPO::getTenantCode, channelUtil.getTenantCode()));
            respDto.setSid(resp.getSid());
            respDto.setMediaJobId(mediaProduceJob.getMediaJobId());
            respDto.setAudioUrl(resp.getAudioUrl());
            respDto.setDuration(mediaProduceJob.getDuration());
            return ResultModel.success(respDto);
        }
        return ResultModel.error(resp.getErrCode().toString(), "语音合成失败:" + resp.getErrMsg());
    }

    /**
     * checksum 的计算方法为：
     * 先拼接字符串 AppId + Now（当前系统时间戳） + body参数的JSON字符 串，
     *
     * @return
     */
    private String checksum(String now, DsTtsRequest request) {
        String str = DeepSoundClient.X_APPID + now + JsonUtils.toJSON(request);
        return Md5Util.encrypt(str);
    }

    private DsTtsRequest getDsTtsRequest(TTSProduceParamDTO param, String text) {
        DsTtsRequest request = new DsTtsRequest();
        request.setServerConfig(new DsTtsServiceConfig());

        DsTtsAudioConfig dsTtsAudioConfig = new DsTtsAudioConfig();
        dsTtsAudioConfig.setAudioFormat(param.getAudioFormat());
        dsTtsAudioConfig.setAudioEncode(param.getAudioEncode());
        dsTtsAudioConfig.setOutputFormat(param.getOutputFormat());
        dsTtsAudioConfig.setPitch(param.getPitch());
        dsTtsAudioConfig.setSpeed(param.getSpeed());
        dsTtsAudioConfig.setVolume(param.getVolume());
        dsTtsAudioConfig.setQuality(param.getQuality());
        request.setAudioConfig(dsTtsAudioConfig);

        DsTtsVoice voiceConfig = new DsTtsVoice();
        voiceConfig.setVoiceName(param.getVoiceName());
        request.setVoice(voiceConfig);

        DsTtsInput inputConfig = new DsTtsInput();
        inputConfig.setText(this.encodeText(text));
        inputConfig.setNumberRead(param.getNumberRead());
        request.setInput(inputConfig);
        return request;
    }

    private String encodeText(String text) {
        return Base64.encodeUrlSafe(text, StandardCharsets.UTF_8);
    }

    public static void main(String[] args) {
        String s = Base64.decodeStr(
                "5Zyo6L-Z56eN6Zi25q6177yM5biC5Zy65Lya5a2Y5Zyo5Lik5Liq5pi-6JGX54m55b6B77yM5LiA5piv5p2_5Z2X6L2u5Yqo5b-r44CC5LqM5piv6KGM5oOF6LW35LyP5aSnWz1wNTAwXeOAgg",
                StandardCharsets.UTF_8);
        System.out.println(s);
    }

}