package com.dl.aiservice.biz.client.guiji;

import com.dl.aiservice.biz.client.guiji.intercepter.GJDigitalInterceptor;
import com.dl.aiservice.biz.client.guiji.intercepter.GJDigitalMethodInterceptor;
import com.dl.aiservice.biz.client.guiji.req.*;
import com.dl.aiservice.biz.client.guiji.resp.*;
import com.dtflys.forest.annotation.*;

import java.util.List;

@BaseRequest(baseURL = "https://meta.guiji.ai/openapi", interceptor = GJDigitalInterceptor.class)
public interface GJDigitalClient {

    String GET_TOKEN = "/oauth/token";
    String ACCESS_TOKEN = "access_token";

    String VIDEO_PATH = "/video/v2/create";
    String VIDEO_TSS_PATH = "/video/v2/create/tss";
    String TRAIN_PATH = "/video/v2/create/training";


    /**
     * 3.1获取token接口
     *
     * @param appId      Access key
     * @param timestamp  当前时间戳，精确到毫秒
     * @param sign       生成的签名
     * @param grant_type 认证类型(固定值‘sign’)
     * @return 返回结果
     */
    @Get(url = "/oauth/token")
    GJBaseResponse<GJTokenResponse> getToken(@Query("appId") String appId, @Query("timestamp") String timestamp, @Query("sign") String sign, @Query(value = "grant_type", defaultValue = "sign") String grant_type);


    /**
     * 3.2定制模特列表查询
     *
     * @return 返回结果
     */
    @Post(url = "/robot/v2/pageList")
    GJBaseResponse<GJPageResponse<GJRobotResponse>> robotPageList(@JSONBody GJPageRequest request);


    /**
     * 3.3免费模特列表查询
     *
     * @return 返回结果
     */
    @Get(url = "/robot/v2/freeList")
    GJBaseResponse<List<GJRobotResponse>> robotFreeList();


    /**
     * 3.4企业所有发音人列表
     *
     * @return 返回结果
     */
    @Get(url = "/speaker/v2/list")
    GJBaseResponse<List<GJAllSpeakersResponse>> allSpeaker();


    /**
     * 3.5 创建视频合成任务
     *
     * @return 返回结果
     */
    @Post(url = "/video/v2/create", interceptor = GJDigitalMethodInterceptor.class)
    GJBaseResponse<GJVideoCreateResponse> videoCreate(@JSONBody GjVideoCreateRequest gjVideoCreateRequest, Long id);

    /**
     * 3.6 创建合成视频任务-语音合成
     *
     * @return 返回结果
     */
    @Post(url = "/video/v2/create/tss", interceptor = GJDigitalMethodInterceptor.class)
    GJBaseResponse<GJVideoCreateResponse> videoCreateTss(@JSONBody GjVideoCreateRequest gjVideoCreateRequest, Long id);


    /**
     * 3.8查询合成视频作品列表
     *
     * @return 返回结果
     */
    @Post(url = "/video/v2/pageList")
    GJBaseResponse<GJPageResponse<GJVideoResponse>> videoPageList(@JSONBody GJPageRequest request);


    /**
     * 3.9 查询用户信息
     *
     * @return 返回结果
     */
    @Get(url = "/user/v2/get")
    GJBaseResponse<GJUserInfoResponse> getUser();

    /**
     * 3.10查询企业下所有用户列表
     *
     * @return 返回结果
     */
    @Post(url = "/admin/user/v2/pageList")
    GJBaseResponse<GJPageResponse<GJUserResponse>> allUserList(@JSONBody GJPageRequest request);

    /**
     * 3.1创建3D视频合成任务
     *
     * @return 返回结果
     */
    @Post(url = "/video/v2/create3D")
    GJBaseResponse<GJVideoCreateResponse> create3D(@JSONBody GjVideoCreate3DRequest request);


    /**
     * 3.1创建3D视频合成任务
     *
     * @return 返回结果
     */
    @Post(url = "/video/v2/create3D/tss")
    GJBaseResponse<GJVideoCreateResponse> create3Dtss(@JSONBody GjVideoCreate3DTssRequest request);


    /**
     * 3.13训练任务列表查询
     *
     * @return 返回结果
     */
    @Post(url = "/video/v2/training/pageList")
    GJBaseResponse<GJPageResponse<GJTrainResponse>> trainPageList(@JSONBody GJPageRequest request);


    /**
     * 3.14训练任务信息查询
     *
     * @param id 提交的训练ID(路径参数)
     * @return 返回结果
     */
    @Get(url = "/video/v2/training/{0}")
    GJBaseResponse<GJTrainResponse> getTrain(String id);

    /**
     * 3.15训合成任务信息查询
     *
     * @param id 提交的合成任务ID(路径参数)
     * @return 返回结果
     */
    @Get(url = "/video/v2/get/{0}")
    GJBaseResponse<GJVideoResponse> getVideo(String id);


    /**
     * 4.1 训练视频提交
     *
     * @return 返回结果
     */
    @Post(url = "/video/v2/create/training", interceptor = GJDigitalMethodInterceptor.class)
    GJBaseResponse<GJCreateTrainingResponse> createTraining(@JSONBody GjCreateTrainingRequest request, Long id);


    /**
     * TTS试听接口
     *
     * @return 返回结果
     */
    @Post(url = "/speaker/v2/tts")
    GJBaseResponse<GJVoiceTTSResponse> tts(@JSONBody GjVoiceTTSRequest request);


}
