package com.dl.aiservice.biz.manager.train.bo;

import com.dl.framework.core.controller.param.AbstractPageParam;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-29 17:15
 */
@Data
public class TrainResultPageBO extends AbstractPageParam {

    /**
     * 训练名
     */
    private String name;

    /**
     * 厂商训练模型编号
     */
    private String extModelCode;

    /**
     * 厂商，3-深声科技（线上训练），6-火山引擎
     */
    private Integer channel;

    /**
     * 训练类型：0 数字人训练；1 声音训练
     *
     * @See:com.dl.aiservice.biz.manager.train.enums.TrainTypeEnum
     */
    private Integer trainType;

    /**
     * 训练状态：1 训练中；0 训练完成；-1 训练失败
     */
    private Integer status;
}
