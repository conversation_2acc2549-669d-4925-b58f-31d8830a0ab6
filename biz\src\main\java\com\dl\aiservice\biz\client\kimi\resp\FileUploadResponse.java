package com.dl.aiservice.biz.client.kimi.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 15:37
 */
@Data
public class FileUploadResponse {

    /**
     * 文件id
     */
    private String id;

    private String object;

    /**
     * 请求时间的秒级时间戳，如1698999496
     */
    @JsonProperty("created_at")
    private long createdDt;

    private Long bytes;

    @JsonProperty("filename")
    private String fileName;

    private String purpose;

    /**
     * ok
     */
    private String status;

    @JsonProperty("status_details")
    private String statusDetails;

}
