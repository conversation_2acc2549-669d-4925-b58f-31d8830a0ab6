package com.dl.aiservice.biz.client.bytedance.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @describe: ByteDanceDmSubmitRequest
 * @author: zhousx
 * @date: 2023/10/8 14:28
 */
@Data
public class ByteDanceDmSubmitRequest {
    @JsonProperty("appid")
    private String appId;

    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("avatar_type")
    private String avatarType = "2d";

    @JsonProperty("text")
    private String text;

    @JsonProperty("role")
    private String role;

    @JsonProperty("input_mode")
    private String inputMode = "text";

    @JsonProperty("codec")
    private String codec = "webm";

    @JsonProperty("callback_url")
    private String callbackUrl;
}
