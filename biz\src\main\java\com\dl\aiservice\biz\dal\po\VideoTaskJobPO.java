package com.dl.aiservice.biz.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.aiservice.biz.common.po.BasePO;
import lombok.Data;

/**
 * 快视频合成任务记录表
 *
 * @TableName video_task_job
 */
@TableName(value = "video_task_job")
@Data
public class VideoTaskJobPO extends BasePO {
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 快视频合成任务唯一标识id 雪花算法
     */
    @TableField(value = "job_id")
    private Long jobId;

    /**
     * 租户代码
     */
    @TableField(value = "tenant_code")
    private String tenantCode;

    /**
     * 上层业务作品唯一标识
     */
    @TableField(value = "works_biz_id")
    private Long worksBizId;

    /**
     * 视频合成商渠道：0 新华智云 4 阿里云
     */
    @TableField(value = "channel")
    private Integer channel;

    /**
     * 回调业务url
     */
    @TableField(value = "callback_url")
    private String callbackUrl;

    @TableField(value = "temp_msg")
    private String tempMsg;
}