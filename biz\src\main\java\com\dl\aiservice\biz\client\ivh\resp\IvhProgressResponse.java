package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/7 17:02
 */
@Data
@NoArgsConstructor
public class IvhProgressResponse {

    /**
     * 制作进度，-1~100，-1代表⽣成失败，100代表⽣成成功（预留字段，⽬前不具备参考意义）
     */
    @JsonProperty(value = "Progress")
    private Integer progress;
    /**
     * ⾳视频结果地址
     */
    @JsonProperty(value = "MediaUrl")
    private String mediaUrl;
    /**
     * 当制作视频时，返回视频对应的SRT字幕地址
     */
    @JsonProperty(value = "SubtitlesUrl")
    private String subtitlesUrl;
    /**
     * 制作状态"COMMIT"：已提交需要排队"MAKING"：制作中"SUCCESS"：制作成功"FAIL"：制作失败
     */
    @JsonProperty(value = "Status")
    private String status;
    /**
     * Status为"COMMIT"状态时在该任务之前排队的任务数量
     */
    @JsonProperty(value = "ArrayCount")
    private Integer arrayCount;
    /**
     * 制作失败返回的失败原因，便于排查问题
     */
    @JsonProperty(value = "FailMessage")
    private String failMessage;

    /**
     * 视频时⻓,单位ms
     */
    @JsonProperty(value = "Duration")
    private Integer duration;

    /**
     * 该字段返回tts试听任务的文本时间戳信息
     */
    @JsonProperty(value = "TextTimestampResult")
    private List<IvhSentences> textTimestampResult;

}