package com.dl.aiservice.share.digitalasset;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class DaVirtualManAuthDTO implements Serializable {

    private static final long serialVersionUID = 751685860281677438L;

    @NotBlank(message = "数字人形象代码必填")
    @ApiModelProperty(value = "数字人形象代码")
    private String vmCode;

    @NotNull(message = "数字人渠道必填")
    @ApiModelProperty(value = "渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云")
    private Integer channel;

    @NotEmpty(message = "被授权租户编码必填")
    @ApiModelProperty(value = "被授权租户编码", required = true)
    List<String> authTenantCodeList;
}