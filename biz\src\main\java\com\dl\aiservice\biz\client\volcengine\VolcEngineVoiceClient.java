package com.dl.aiservice.biz.client.volcengine;

import com.dl.aiservice.biz.client.volcengine.interceptor.VolcEngineApiInterceptor;
import com.dl.aiservice.biz.client.volcengine.interceptor.VolcEngineVoiceMegaTtsInterceptor;
import com.dl.aiservice.biz.client.volcengine.interceptor.VolcEngineVoiceTtsInterceptor;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineTtsReq;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineVoiceTrainActivateReq;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineVoiceTrainReq;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineVoiceTrainStatusReq;
import com.dl.aiservice.biz.client.volcengine.resp.VolcEngineTtsResp;
import com.dl.aiservice.biz.client.volcengine.resp.VolcEngineVoiceTrainActivateResp;
import com.dl.aiservice.biz.client.volcengine.resp.VolcEngineVoiceTrainResp;
import com.dl.aiservice.biz.client.volcengine.resp.VolcEngineVoiceTrainStatusResp;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-26 13:55
 */
@BaseRequest
public interface VolcEngineVoiceClient {

    String AUDIO_TRAIN_PATH = "https://openspeech.bytedance.com/api/v1/mega_tts/audio/upload";

    /**
     * 声音复刻-训练
     *
     * @param request
     * @return
     */
    @Post(url = "https://openspeech.bytedance.com/api/v1/mega_tts/audio/upload",
            interceptor = VolcEngineVoiceMegaTtsInterceptor.class)
    VolcEngineVoiceTrainResp voiceTrain(Long trainResultBizId, Long trainJobId,
            @JSONBody VolcEngineVoiceTrainReq request);

    /**
     * 声音复刻-状态查询
     * 该接口仅在音色激活前可用，激活一段时间后使用将无法获取音色状态。
     *
     * @param request
     * @return
     */
    @Post(url = "https://openspeech.bytedance.com/api/v1/mega_tts/status",
            interceptor = VolcEngineVoiceMegaTtsInterceptor.class)
    VolcEngineVoiceTrainStatusResp voiceTrainStatus(@JSONBody VolcEngineVoiceTrainStatusReq request);

    /**
     * 激活（启用）音色
     * 目前已无需调用该接口即可进行tts合成。调用该接口后将无法继续训练，无论是否还有剩余的训练次数。
     * 调用该接口相当于【锁定】
     *
     * @param action
     * @param version
     * @param request
     * @return
     */
    @Post(url = "https://open.volcengineapi.com", interceptor = VolcEngineApiInterceptor.class)
    VolcEngineVoiceTrainActivateResp voiceTrainActivate(@Query("Action") String action,
            @Query("Version") String version, @Body VolcEngineVoiceTrainActivateReq request);

    /**
     * TTS
     * HTTP接口(一次性合成-非流式)
     *
     * @param request
     * @return
     */
    @Post(url = "https://openspeech.bytedance.com/api/v1/tts", interceptor = VolcEngineVoiceTtsInterceptor.class)
    VolcEngineTtsResp tts(@JSONBody VolcEngineTtsReq request);
}
