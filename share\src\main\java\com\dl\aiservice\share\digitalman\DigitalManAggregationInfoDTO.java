package com.dl.aiservice.share.digitalman;

import com.dl.aiservice.share.digitalasset.DaVirtualVoiceBaseInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-21 09:27
 */
@Data
@ApiModel("数字人聚合信息")
public class DigitalManAggregationInfoDTO extends DigitalManBaseInfoDTO implements Serializable {
    private static final long serialVersionUID = 7096115796284105106L;

    @ApiModelProperty("数字人场景信息")
    private List<DigitalManSceneBaseInfoDTO> sceneList;

    @ApiModelProperty("数字人声音信息")
    private DaVirtualVoiceBaseInfoDTO voiceInfo;
}
