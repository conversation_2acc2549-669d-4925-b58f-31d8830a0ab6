package com.dl.aiservice.biz.manager.voiceclone.guiji;

import com.dl.aiservice.biz.client.guiji.GJDigitalClient;
import com.dl.aiservice.biz.client.guiji.req.GjVoiceTTSRequest;
import com.dl.aiservice.biz.client.guiji.resp.GJBaseResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJVoiceTTSResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.voiceclone.VoiceCloneHandlerManager;
import com.dl.aiservice.biz.manager.voiceclone.grammartrans.impl.guiji.GuijiGrammerTransformer;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @describe: TencentCloudHandlerManager
 * @author: zhousx
 * @date: 2023/5/4 10:37
 */
@Component
@Slf4j
public class GjVoiceCloneHandlerManager implements VoiceCloneHandlerManager {

    @Resource
    private GJDigitalClient gjDigitalClient;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private GuijiGrammerTransformer guijiGrammerTransformer;

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.GUI_JI, ServiceChannelEnum.CJHX_GUIJI);
    }

    @Override
    public ResultModel envCheck(String url) {
        return null;
    }

    @Override
    public ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId) {
        return null;
    }

    @Override
    public ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO request) {
        Assert.notNull(request, "入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getVoiceName()), "voiceName入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getText()), "音频文本不能为空");
        GjVoiceTTSRequest voice = new GjVoiceTTSRequest();
        voice.setSpeakerId(Integer.valueOf(request.getVoiceName()));
        voice.setContent(guijiGrammerTransformer.grammarTransform(request.getText()));

        // 初始化请求数据
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(hostTimeIdg.generateId().longValue());
        job.setTenantCode(channelUtil.getTenantCode());
        job.setChannel(channelUtil.getChannel());
        job.setWorksBizId(request.getWorksBizId());
        job.setVideoTaskJobId(request.getVideoTaskJobId());
        job.setJobType(Const.TWO);
        job.setJobContent(JsonUtils.toJSON(voice));
        job.setStatus(Const.ONE);
        job.setRequestDt(new Date());
        mediaProduceJobManager.save(job);

        try {
            GJBaseResponse<GJVoiceTTSResponse> resp = gjDigitalClient.tts(voice);
            TTSResponseDTO respDto = new TTSResponseDTO();
            if (resp.isSuccess()) {
                respDto.setMediaJobId(job.getMediaJobId());
                respDto.setAudioUrl(resp.getData().getTtsUrl());
                respDto.setDuration(resp.getData().getDuration() / 1000);
                //目前sid为空，等深声后续接口返回
                job.setStatus(Const.ZERO);
                job.setMediaUrl(resp.getData().getTtsUrl());
                job.setDuration(resp.getData().getDuration() / 1000);
                job.setResponseDt(new Date());
                // 时长暂时不处理
                mediaProduceJobManager.updateById(job);
                return ResultModel.success(respDto);
            }
            return ResultModel.error(resp.getCode().toString(), "语音合成失败:" + resp.getMessage());
        } catch (Exception e) {
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            job.setStatus(-Const.ONE);
            // 失败原因
            job.setFailReason(e.getMessage());
            job.setResponseDt(new Date());
            mediaProduceJobManager.updateById(job);
            log.error(e.getMessage(), e);
            return ResultModel.error("-1", "语音合成失败:" + e.getMessage());
        }
    }
}
