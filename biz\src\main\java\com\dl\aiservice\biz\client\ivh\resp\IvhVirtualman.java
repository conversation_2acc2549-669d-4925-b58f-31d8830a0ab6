package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class IvhVirtualman {

    /**
     * 主播名称
     */
    @JsonProperty(value = "AnchorName")
    private String anchorName;
    /**
     * 主播code
     */
    @JsonProperty(value = "AnchorCode")
    private String anchorCode;
    /**
     * 数智⼈头像图⽚url
     */
    @JsonProperty(value = "HeaderImage")
    private String headerImage;
    /**
     * 数智⼈类型
     */
    @JsonProperty(value = "VirtualmanType")
    private String virtualmanType;
    /**
     * 数智⼈类型code
     */
    @JsonProperty(value = "VirtualmanTypeCode")
    private String virtualmanTypeCode;
    /**
     * 数智⼈模型tag，分三类：BasicStandardAdvanced⽬前tag仅影响了主播横纵向位置功能，⻅接⼝4.3。
     */
    @JsonProperty(value = "Tag")
    private String tag;
}