package com.dl.aiservice.biz.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 异步任务配置
 * 用于支持延迟消息功能
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * 延迟消息任务执行器
     */
    @Bean("delayedMessageExecutor")
    public Executor delayedMessageExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("DelayedMessage-");
        executor.setKeepAliveSeconds(60);
        executor.initialize();
        return executor;
    }
}
