package com.dl.aiservice.biz.client.kimi.consts;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-08 14:27
 */
public class KimiConst {

    public static final String COMMON_ERROR_MSG = "服务器开小差了，请稍后再试";

    public static final String FILE_NO_CONTENT_ERROR_MSG = "未解析出内容，请更换附件后再试";

    public static final String FILE_CONTENT_EXTRACT_ERROR_MSG = "文件解析失败，请稍后再试";

    public static final String CONTENT_LENGTH_TOO_LONG_ERROR_MSG = "内容过长，请稍作删减";

    public static final String INTERFACE_ERROR_MSG = "接口发生异常";

    public static final String FILE_UPLOAD_ERROR_MSG = "文件上传失败";

    public static final String FILE_DOWNLOAD_ERROR_MSG = "文件下载出现异常";

    public static final String ESTIMATE_TOKEN_COUNT_ERROR_MSG = "预估耗费token数量失败";
}
