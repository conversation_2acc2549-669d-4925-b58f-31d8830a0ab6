package com.dl.aiservice.biz.client.ivh.enums;

import java.util.Objects;

public enum IvhErrCodeEnum {

    ERROR_CODE_0(0, "正常"),
    ERROR_CODE_100000(100000, "系统内部错误"),
    ERROR_CODE_100001(100001, "请求参数错误（包括参数格式、类型等错误，缺少参数错误，必传参数没填）"),
    ERROR_CODE_100003(100003, "请求参数错误（包括参数格式、类型等错误，缺少参数错误，必传参数没填）"),
    ERROR_CODE_100002(100002, "参数取值错误"),
    ERROR_CODE_100005(100005, "未授权操作"),
    ERROR_CODE_100008(100008, "超过配额限制"),
    ERROR_CODE_100009(100009, "资源不存在"),
    ERROR_CODE_110005(110005, "记录已经存在"),
    ERROR_CODE_110006(110006, "记录不存在"),
    ERROR_CODE_110007(110007, "virtualmankey已过期"),
    ERROR_CODE_801000(801000, "调用远程服务异常"),
    ERROR_CODE_801002(801002, "调用远程服务，返回状态码异常"),
    ERROR_CODE_801004(801004, "操作频繁，请稍后重试"),
    ERROR_CODE_801005(801005, "文本安全校验未通过"),
    UNKNOWN(9999999, "未知异常");

    private final Integer errorCode;
    private final String errorDesc;

    IvhErrCodeEnum(Integer errorCode, String errorDesc) {
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }

    public static String getErrorDesc(Integer errorCode) {
        for (IvhErrCodeEnum wxErrorCodeEnum : values()) {
            if (Objects.equals(wxErrorCodeEnum.errorCode, errorCode)) {
                return wxErrorCodeEnum.errorDesc;
            }
        }
        return null;
    }

    public static IvhErrCodeEnum errorCode(Integer code) {
        for (IvhErrCodeEnum value : values()) {
            if (Objects.nonNull(code)) {
                if (value.getErrorCode().equals(code)) {
                    return value;
                }
            }
        }
        return UNKNOWN;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorDesc() {
        return errorDesc;
    }
}
