package com.dl.aiservice.web.controller.media;

import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.ResourcePriceConfigManager;
import com.dl.aiservice.biz.manager.dto.ResourcePriceConfigDTO;
import com.dl.aiservice.share.enums.ResourceTypeEnum;
import com.dl.aiservice.share.media.dto.MediaProduceJobRequestDTO;
import com.dl.aiservice.share.media.dto.MediaProduceJobResponseDTO;
import com.dl.framework.common.model.ResultModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @describe: MediaController
 * @author: zhousx
 * @date: 2023/3/29 17:25
 */
@Slf4j
@RestController
@RequestMapping("/media")
public class MediaController {
    @Autowired
    private ChannelUtil channelUtil;
    @Autowired
    private MediaProduceJobManager mediaProduceJobManager;
    @Autowired
    private ResourcePriceConfigManager resourcePriceConfigManager;

    @PostMapping("/joblist")
    public ResultModel<List<MediaProduceJobResponseDTO>> getJobList(@RequestBody @Validated MediaProduceJobRequestDTO request) {
        List<MediaProduceJobPO> mediaProduceJobPOS = mediaProduceJobManager.lambdaQuery().eq(MediaProduceJobPO::getWorksBizId, request.getWorksBizId()).list();
        if(CollectionUtils.isEmpty(mediaProduceJobPOS)) {
            return ResultModel.success(Collections.EMPTY_LIST);
        }
        List<ResourcePriceConfigDTO> configDTOS = resourcePriceConfigManager.listPriceConfigsByTenant(channelUtil.getTenantCode(), Const.ZERO);
        Assert.notEmpty(configDTOS, "服务尚未配置完成，请联系管理员");

        return ResultModel.success(mediaProduceJobPOS.stream().map(mediaProduceJobPO -> {
            MediaProduceJobResponseDTO mediaProduceJobResponseDTO = new MediaProduceJobResponseDTO();
            mediaProduceJobResponseDTO.setMediaJobId(mediaProduceJobPO.getMediaJobId());
            mediaProduceJobResponseDTO.setChannel(mediaProduceJobPO.getChannel());
            mediaProduceJobResponseDTO.setMediaUrl(mediaProduceJobPO.getMediaUrl());
            mediaProduceJobResponseDTO.setMediaName(mediaProduceJobPO.getMediaName());
            mediaProduceJobResponseDTO.setJobType(mediaProduceJobPO.getJobType());
            mediaProduceJobResponseDTO.setCoverUrl(mediaProduceJobPO.getCoverUrl());
            mediaProduceJobResponseDTO.setStatus(mediaProduceJobPO.getStatus());
            mediaProduceJobResponseDTO.setDuration(mediaProduceJobPO.getDuration());
            if(Objects.equals(mediaProduceJobPO.getStatus(), Const.ZERO)) {
                Map<Integer, ResourcePriceConfigDTO> priceConfigMap = configDTOS.stream()
                        .sorted(Comparator.comparing(ResourcePriceConfigDTO::getOrder))
                        .collect(Collectors.toMap(ResourcePriceConfigDTO::getResourceType, Function.identity(),
                                (v1, v2) -> v1));
                ResourcePriceConfigDTO resourcePriceConfigDTO = null;
                switch (mediaProduceJobPO.getJobType()) {
                    case 0:
                        resourcePriceConfigDTO = priceConfigMap.get(ResourceTypeEnum.MEDIA_PRODUCE.getCode());
                        break;
                    case 1:
                        resourcePriceConfigDTO = priceConfigMap.get(ResourceTypeEnum.AVATAR.getCode());
                        break;
                    case 2:
                        resourcePriceConfigDTO = priceConfigMap.get(ResourceTypeEnum.VOICE_CLONE.getCode());
                        break;
                    default:
                        break;
                }
                Assert.notNull(resourcePriceConfigDTO, "服务尚未配置完成，请联系管理员");
                mediaProduceJobResponseDTO.setCost(Double.valueOf(Math.ceil(mediaProduceJobPO.getDuration().doubleValue() / 60)).longValue()
                        * resourcePriceConfigDTO.getPrice());
            }
            return mediaProduceJobResponseDTO;
        }).collect(Collectors.toList()));
    }

    @PostMapping("/jobdetail")
    public ResultModel<List<MediaProduceJobResponseDTO>> getJobDetail(@RequestBody @Validated MediaProduceJobRequestDTO request) {
        List<MediaProduceJobPO> mediaProduceJobPOS = mediaProduceJobManager.lambdaQuery().in(MediaProduceJobPO::getMediaJobId, request.getMediaJobIds()).list();
        if(CollectionUtils.isEmpty(mediaProduceJobPOS)) {
            return ResultModel.success(Collections.EMPTY_LIST);
        }
        return ResultModel.success(mediaProduceJobPOS.stream().map(mediaProduceJobPO -> {
            MediaProduceJobResponseDTO mediaProduceJobResponseDTO = new MediaProduceJobResponseDTO();
            mediaProduceJobResponseDTO.setMediaJobId(mediaProduceJobPO.getMediaJobId());
            mediaProduceJobResponseDTO.setChannel(mediaProduceJobPO.getChannel());
            mediaProduceJobResponseDTO.setMediaUrl(mediaProduceJobPO.getMediaUrl());
            mediaProduceJobResponseDTO.setMediaName(mediaProduceJobPO.getMediaName());
            mediaProduceJobResponseDTO.setJobType(mediaProduceJobPO.getJobType());
            mediaProduceJobResponseDTO.setCoverUrl(mediaProduceJobPO.getCoverUrl());
            mediaProduceJobResponseDTO.setStatus(mediaProduceJobPO.getStatus());
            mediaProduceJobResponseDTO.setDuration(mediaProduceJobPO.getDuration());
            return mediaProduceJobResponseDTO;
        }).collect(Collectors.toList()));
    }

}
