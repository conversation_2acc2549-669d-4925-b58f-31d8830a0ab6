package com.dl.aiservice.biz.client.volcengine.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-26 16:14
 */
@Data
public class VolcEngineVoiceTrainStatusResp {

    @JsonProperty(value = "BaseResp")
    private VolcEngineVoiceBaseResp baseResp;

    @JsonProperty(value = "speaker_id")
    private String speakerId;

    private Integer status;

    @JsonProperty(value = "create_time")
    private Long createTime;

    private String version;

    @JsonProperty(value = "demo_audio")
    private String demoAudio;
}
