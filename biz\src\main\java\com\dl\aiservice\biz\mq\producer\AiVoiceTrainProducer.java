package com.dl.aiservice.biz.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.mq.AiChannels;
import com.dl.aiservice.biz.mq.delay.DelayedMessageService;
import com.dl.aiservice.biz.mq.dto.VoiceTrainProgressDTO;
import com.dl.aiservice.biz.mq.enums.DelayLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-29 10:42
 */
@Slf4j
@Component
public class AiVoiceTrainProducer {

    @Autowired
    private AiChannels aiChannels;

    @Autowired
    private DelayedMessageService delayedMessageService;

    public void sendVoiceTrainProgressMQ(VoiceTrainProgressDTO progressRequestDTO, DelayLevelEnum delayLevelEnum) {
        try {
            // 使用延迟消息服务来实现延迟功能
            delayedMessageService.sendDelayedVoiceTrainProgressMessage(progressRequestDTO, delayLevelEnum);
            log.info("提交延迟发送声音训练查询进度请求的消息,progressRequestDTO:{},delayLevel:{}",
                    JSONUtil.toJsonStr(progressRequestDTO), delayLevelEnum);
        } catch (Exception e) {
            log.error("提交延迟发送声音训练查询进度请求的消息发生异常,progressRequestDTO:{},delayLevel:{},e:{}",
                    JSONUtil.toJsonStr(progressRequestDTO), delayLevelEnum, e);
        }
    }
}
