package com.dl.aiservice.biz.client.guiji.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class GJPageResponse<T> {

    /**
     * 页尺寸
     */
    private Integer pageSize = 0;
    /**
     * 页号
     */
    private Integer pageNo = 0;
    /**
     * 总记录数
     */
    private Integer totalRecord = 0;
    /**
     * 模特列表
     */
    private List<T> records;

}

