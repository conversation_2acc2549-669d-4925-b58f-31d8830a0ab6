package com.dl.aiservice.biz.service.digital.dto.req.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AliyunVideoEditingRequestDTO {

    /**
     * 虚拟人形象Id，必填 ，取值：数字人官方形象
     */
    @JsonProperty("AvatarId")
    private String AvatarId;
    /**
     * 发音人，仅输入为Text有效，选填，取值：智能语音效果示例
     */
    @JsonProperty("Voice")
    private String Voice;
    /**
     * 语速，仅当输入为文本类型有效，取值范围：-500～500，默认值：0
     */
    @JsonProperty("SpeechRate")
    private Integer SpeechRate;

    /**
     * 音调，仅当输入为文本类型有效，取值范围：-500～500，默认值：0
     */
    @JsonProperty("PitchRate")
    private Integer PitchRate;

    /**
     * 音量，仅当输入为文本类型有效，取值范围：0~100，默认值50
     */
    @JsonProperty("Volume")
    private Integer Volume;

    /**
     * 背景图，默认是绿幕，选填，仅支持jpg、png格式
     */
    @JsonProperty("BackgroundUrl")
    private String BackgroundUrl;

}