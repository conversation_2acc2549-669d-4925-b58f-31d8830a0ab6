package com.dl.aiservice.share.digitalasset;

import com.dl.aiservice.share.common.req.PageRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DaVirtualManPageRequestDTO extends PageRequestDTO {

    @ApiModelProperty(value = "数字人形象代码")
    private String vmCode;

    @ApiModelProperty(value = "数字人名称")
    private String vmName;

    @ApiModelProperty(value = "数字人厂商渠道")
    private Integer channel;
}
