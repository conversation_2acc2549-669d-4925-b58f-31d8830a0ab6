package com.dl.aiservice.biz.manager.voiceclone.deepsound.impl;

import cn.hutool.core.codec.Base64;
import com.dl.aiservice.biz.client.deepsound.DeepSoundStandardClient;
import com.dl.aiservice.biz.client.deepsound.req.DsStandardTtsRequest;
import com.dl.aiservice.biz.client.deepsound.resp.DsStandardBaseResponse;
import com.dl.aiservice.biz.client.deepsound.resp.DsStandardTTS;
import com.dl.aiservice.biz.client.deepsound.resp.DsStandardTTSResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.voiceclone.deepsound.DeepSoundStandardHandlerManager;
import com.dl.aiservice.biz.manager.voiceclone.grammartrans.impl.deepsound.DeepSoundGrammarTransformer;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class DeepSoundStandardHandlerManagerImpl implements DeepSoundStandardHandlerManager {

    @Resource
    private DeepSoundStandardClient deepSoundStandardClient;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private DeepSoundGrammarTransformer deepSoundGrammarTransformer;

    @Override
    public ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO request) {
        Assert.notNull(request, "入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getVoiceName()), "voiceName入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getText()), "音频文本不能为空");
        //语法转换
        String text = deepSoundGrammarTransformer.grammarTransform(request.getText());
        DsStandardTtsRequest dsTtsRequest = getDsTtsRequest(request, text);

        // 初始化请求数据
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(hostTimeIdg.generateId().longValue());
        job.setTenantCode(channelUtil.getTenantCode());
        job.setWorksBizId(request.getWorksBizId());
        job.setVideoTaskJobId(request.getVideoTaskJobId());
        job.setChannel(ServiceChannelEnum.DEEP_SOUND_STANDARD.getCode());
        job.setJobType(Const.TWO);
        job.setJobContent(JsonUtils.toJSON(dsTtsRequest));
        job.setStatus(Const.ONE);
        job.setRequestDt(new Date());
        mediaProduceJobManager.save(job);

        try {
            DsStandardBaseResponse<DsStandardTTSResponse> resp = deepSoundStandardClient.tts(dsTtsRequest);
            TTSResponseDTO respDto = new TTSResponseDTO();
            if (resp.isSuccess()) {
                DsStandardTTS tts = resp.getData().getTts();
                respDto.setSid(resp.getData().getTts().getSid());
                respDto.setMediaJobId(job.getMediaJobId());
                respDto.setAudioUrl(resp.getData().getTts().getUrl());
                respDto.setDuration(tts.getDuration() / 1000);
                //目前sid为空，等深声后续接口返回
                job.setExtJobId(tts.getSid());
                job.setStatus(Const.ZERO);
                job.setMediaUrl(tts.getUrl());
                job.setDuration(tts.getDuration() / 1000);
                job.setResponseDt(new Date());
                // 时长暂时不处理
                mediaProduceJobManager.updateById(job);
                return ResultModel.success(respDto);
            }
            return ResultModel.error(resp.getCode().toString(), "语音合成失败:" + resp.getMessage());
        } catch (Exception e) {
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            job.setStatus(-Const.ONE);
            // 失败原因
            job.setFailReason(e.getMessage());
            job.setResponseDt(new Date());
            mediaProduceJobManager.updateById(job);
            log.error(e.getMessage(), e);
            return ResultModel.error("-1", "语音合成失败:" + e.getMessage());
        }
    }

    private DsStandardTtsRequest getDsTtsRequest(TTSProduceParamDTO param, String text) {
        DsStandardTtsRequest request = new DsStandardTtsRequest();
        request.setVoiceName(param.getVoiceName());
        request.setText(text);
        request.setFormat(param.getAudioEncode());
        request.setOutput(param.getOutputFormat());
        request.setSampleRate(
                StringUtils.isNotBlank(param.getAudioFormat()) ? Integer.valueOf(param.getAudioFormat()) : null);
        request.setSpeed(StringUtils.isNotBlank(param.getSpeed()) ? Double.valueOf(param.getSpeed()) : null);
        request.setPitch(StringUtils.isNotBlank(param.getPitch()) ? Double.valueOf(param.getPitch()) : null);
        return request;
    }

    private String decodeText(String encodeText) {
        return Base64.decodeStr(encodeText, StandardCharsets.UTF_8);
    }

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.DEEP_SOUND_STANDARD);
    }

    @Override
    public ResultModel envCheck(String url) {
        return null;
    }

    @Override
    public ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId) {
        return null;
    }

}