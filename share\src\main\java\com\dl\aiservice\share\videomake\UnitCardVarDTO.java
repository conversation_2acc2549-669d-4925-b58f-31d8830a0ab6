package com.dl.aiservice.share.videomake;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UnitCardVarDTO {

    String key;

    /**
     * 别名，目前用不到；当出现第二个视频合成供应商时使用
     */
    String alias;

    String unitKey;

    Object value;

    String replaceType;
}
