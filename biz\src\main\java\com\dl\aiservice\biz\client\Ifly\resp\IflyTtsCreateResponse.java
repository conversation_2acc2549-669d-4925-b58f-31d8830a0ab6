package com.dl.aiservice.biz.client.Ifly.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IflyTtsCreateResponse {

    /**
     * 合成音频地址
     */
    private String url;

    /**
     * 字幕文件
     */
    private String srtText;
    /**
     * 动作、情感时间节点
     */
    private String extendJson;

}
