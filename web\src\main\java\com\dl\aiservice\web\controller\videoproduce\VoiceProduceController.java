package com.dl.aiservice.web.controller.videoproduce;

import com.dl.aiservice.biz.common.annotation.NotAuth;
import com.dl.aiservice.share.videoproduce.dto.aliyun.ProduceMediaCompleteParamDTO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/video/produce")
@Slf4j
public class VoiceProduceController {

    @Resource
    private VoiceProduceProcessor voiceProduceProcessor;

    @Deprecated
    @ApiOperation("阿里云合成结果回调接口")
    @PostMapping("/notify/aliyun")
    @NotAuth
    public ResultModel<Boolean> handleAliyunCallback(@RequestBody ProduceMediaCompleteParamDTO param) {
        return voiceProduceProcessor.handleAliyunCallback(param);
    }

}