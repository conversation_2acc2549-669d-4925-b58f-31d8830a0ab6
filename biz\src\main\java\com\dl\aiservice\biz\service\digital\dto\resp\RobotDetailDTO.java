package com.dl.aiservice.biz.service.digital.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class RobotDetailDTO implements Serializable {

    private static final long serialVersionUID = 6660662421497170627L;

    /**
     * 硅基：场景Id：  腾讯：virtualmanKey
     */
    @ApiModelProperty(value = "硅基：场景Id：  腾讯：virtualmanKey")
    private String sceneId;
    //**************************************硅基参数参数**********************************************


    /**
     * 场景名称
     */
    @ApiModelProperty(value = "硅基：场景名称")
    private String sceneName;
    /**
     * 示例视频
     */
    @ApiModelProperty(value = "硅基：示例视频")
    private String exampleUrl;
    /**
     * 封面地址
     */
    @ApiModelProperty(value = "硅基：封面地址")
    private String coverUrl;
    /**
     * 示例图片
     */
    @ApiModelProperty(value = "硅基：示例图片")
    private String samplePictureUrl;


    //**************************************腾讯云参数参数**********************************************

    /**
     * ⻆⾊名称
     */
    @ApiModelProperty(value = "腾讯：⻆⾊名称")
    private String name;
    /**
     * ⻆⾊code
     */
    @ApiModelProperty(value = "腾讯：⻆⾊code")
    private String code;
    /**
     * 数智⼈服装
     */
    @ApiModelProperty(value = "腾讯：数智⼈服装")
    private String clothesName;
    /**
     * 数智⼈姿态
     */
    @ApiModelProperty(value = "腾讯：数智⼈姿态")
    private String poseName;
    /**
     * 数智⼈分辨率
     */
    @ApiModelProperty(value = "腾讯：数智⼈分辨率")
    private String resolution;
    /**
     * 数智⼈头像图⽚url
     */
    @ApiModelProperty(value = "腾讯：数智⼈头像图⽚url")
    private String headerImage;
    /**
     * 数智⼈姿态图⽚url
     */
    @ApiModelProperty(value = "腾讯：数智⼈姿态图⽚url")
    private String poseImage;
    /**
     * 数智⼈服装图⽚url
     */
    @ApiModelProperty(value = "腾讯：数智⼈服装图⽚url")
    private String clothesImage;
    /**
     * 数智⼈⽀持的驱动类型1. Text: ⽂本驱动2. OriginalVoice: 原声⾳频驱动3. ModulatedVoice: 变声⾳频驱动
     */
    @ApiModelProperty(value = "腾讯：数智⼈⽀持的驱动类型")
    private List<String> supportDriverTypes;


}

