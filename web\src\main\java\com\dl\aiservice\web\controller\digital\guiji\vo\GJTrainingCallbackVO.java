package com.dl.aiservice.web.controller.digital.guiji.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class GJTrainingCallbackVO implements Serializable {
    private static final long serialVersionUID = -5040616107365918691L;
    /**
     * 提交的任务ID
     */
    private Integer id;
    /**
     * success, fail
     */
    private String result;
    /**
     * 失败原因
     */
    private String reason;
    /**
     * 模特ID
     */
    private Integer robotId;
    /**
     * 模特下场景ID
     */
    private Integer sceneId;


}
