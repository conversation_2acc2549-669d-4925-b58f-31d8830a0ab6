package com.dl.aiservice.biz.service.digital.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
@ApiModel("数字人视频合成回调DTO")
public class DigitalVideoCallbackDTO implements Serializable {

    private static final long serialVersionUID = 6660662421497170627L;

    /**
     * 媒体id 雪花算法
     */
    @ApiModelProperty(value = "媒体id 雪花算法")
    private Long mediaJobId;

    /**
     * 第三方媒体id
     */
    @ApiModelProperty(value = "第三方媒体id")
    private String extJobId;

    /**
     * 租户代码
     */
    @ApiModelProperty(value = "租户代码")
    private String tenantCode;

    /**
     * 渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    @ApiModelProperty(value = "渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云")
    private Integer channel;

    /**
     * 合成类型：0-视频合成 1-数字人 2-TTS音频
     */
    @ApiModelProperty(value = "合成类型：0-视频合成 1-数字人 2-TTS音频")
    private Integer jobType;

    /**
     * 任务状态：1 合成中；0 合成完成；-1 合成失败
     */
    @ApiModelProperty(value = "任务状态：1 合成中；0 合成完成；-1 合成失败")
    private Integer status;

    /**
     * 合成后url
     */
    @ApiModelProperty(value = "合成后url")
    private String mediaUrl;

    /**
     * 封面图url
     */
    @ApiModelProperty(value = "封面图url")
    private String coverUrl;

    /**
     * 时长(单位：秒，如11.32)
     */
    @ApiModelProperty(value = "时长(单位：秒，如11.32)")
    private Double duration;

    /**
     * 作品名称
     */
    @ApiModelProperty(value = "作品名称")
    private String mediaName;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String failReason;

    /**
     * 上层业务作品唯一标识
     */
    @ApiModelProperty(value = "上层业务作品唯一标识")
    private Long worksBizId;

    /**
     * 快视频合成任务记录表唯一标识 - job_id
     */
    private Long videoTaskJobId;

    /**
     * 回调日志id
     */
    private Long callbackId;

}

