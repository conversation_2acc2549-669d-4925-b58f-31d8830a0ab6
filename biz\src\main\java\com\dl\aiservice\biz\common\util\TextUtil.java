package com.dl.aiservice.biz.common.util;

import com.dl.aiservice.biz.common.enums.SymbolE;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-06-19 10:29
 */
public class TextUtil {

    public static String rmHtml(String s) {
        if (StringUtils.isBlank(s)) {
            return SymbolE.BLANK.getValue();
        }

        String str = s.replaceAll("<[.[^<]]*>", "");
        return str;
    }

}
