package com.dl.aiservice.biz.manager.voiceclone.tencentcloud;

import cn.hutool.core.io.FileUtil;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.MediaUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.manager.voiceclone.VoiceCloneHandlerManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.google.common.collect.Lists;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.tts.v20190823.TtsClient;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceRequest;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileOutputStream;
import java.util.Base64;
import java.util.Date;
import java.util.List;

/**
 * @describe: TencentCloudHandlerManager
 * @author: zhousx
 * @date: 2023/5/4 10:37
 */
@Component
@Slf4j
public class TencentCloudHandlerManager implements VoiceCloneHandlerManager {
    private static final String TENCENTCLOUD_ENDPOINT = "tts.tencentcloudapi.com";
    private TtsClient client;
    @Value("${dl.tencentcloud.tts.region}")
    private String region;
    @Value("${dl.tencentcloud.tts.secretId}")
    private String secretId;
    @Value("${dl.tencentcloud.tts.secretKey}")
    private String secretKey;
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private CosFileUploadManager cosFileUploadManager;
    @Autowired
    private MediaProduceJobManager mediaProduceJobManager;
    @Autowired
    private ChannelUtil channelUtil;

    @PostConstruct
    private void createClient() {
        Credential cred = new Credential(secretId, secretKey);
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(TENCENTCLOUD_ENDPOINT);
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        client = new TtsClient(cred, region, clientProfile);
    }

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.TENCENT_CLOUD);
    }

    @Override
    public ResultModel envCheck(String url) {
        return null;
    }

    @Override
    public ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId) {
        return null;
    }

    @Override
    public ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO param) {
        Assert.notNull(param, "入参不能为空");
        Assert.isTrue(StringUtils.isNumeric(param.getVoiceName()), "voiceName入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getText()), "音频文本不能为空");

        // 实例化一个请求对象,每个接口都会对应一个request对象
        TextToVoiceRequest req = new TextToVoiceRequest();
        String sessionId = String.valueOf(hostTimeIdg.generateId().longValue());
        String codec = formatCodec(param.getAudioEncode());
        req.setSessionId(sessionId);
        req.setText(param.getText());
        req.setCodec(codec);
        req.setVolume(formatVolume(param.getVolume()));
        req.setEnableSubtitle(true);
        req.setVoiceType(Long.valueOf(param.getVoiceName()));
        req.setEmotionCategory(param.getEmotionCategory());
        req.setEmotionIntensity(param.getEmotionIntensity());
        if(StringUtils.isNotBlank(param.getSpeed())) {
            req.setSpeed(Float.valueOf(param.getSpeed()));
        }

        // 初始化请求数据
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(Long.valueOf(sessionId));
        job.setTenantCode(channelUtil.getTenantCode());
        job.setWorksBizId(param.getWorksBizId());
        job.setVideoTaskJobId(param.getVideoTaskJobId());
        job.setChannel(ServiceChannelEnum.TENCENT_CLOUD.getCode());
        job.setJobType(Const.TWO);
        job.setJobContent(JsonUtils.toJSON(req));
        job.setStatus(Const.ONE);
        job.setRequestDt(new Date());
        mediaProduceJobManager.save(job);

        try {
            TextToVoiceResponse resp = client.TextToVoice(req);
            job.setResponseDt(new Date());
            File audioFile = getAudioFile(sessionId, resp.getAudio(), codec);
            String audioUrl = cosFileUploadManager.uploadFile(audioFile, null, null);
            Double duration = MediaUtil.getAudioDuration(audioFile);
            FileUtil.del(audioFile);

            job.setExtJobId(resp.getRequestId());
            job.setStatus(Const.ZERO);
            job.setMediaUrl(audioUrl);
            job.setDuration(duration);
            mediaProduceJobManager.updateById(job);

            TTSResponseDTO ttsResponseDTO = new TTSResponseDTO();
            ttsResponseDTO.setSid(resp.getRequestId());
            ttsResponseDTO.setMediaJobId(job.getMediaJobId());
            ttsResponseDTO.setAudioUrl(audioUrl);
            ttsResponseDTO.setDuration(duration);
            return ResultModel.success(ttsResponseDTO);
        } catch (Exception e) {
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            job.setStatus(-Const.ONE);
            // 失败原因
            job.setFailReason(e.getMessage());
            job.setResponseDt(new Date());
            mediaProduceJobManager.updateById(job);
            log.error(e.getMessage(), e);
            return ResultModel.error("-1", "语音合成失败:" + e.getMessage());
        }
    }

    private String formatCodec(String audioEncode) {
        if(StringUtils.isNotBlank(audioEncode) && audioEncode.contains("wav")) {
            return "wav";
        }
        if(StringUtils.isNotBlank(audioEncode) && audioEncode.contains("mp3")) {
            return "mp3";
        }
        if(StringUtils.isNotBlank(audioEncode) && audioEncode.contains("pcm")) {
            return "pcm";
        }
        return "mp3";
    }

    private Float formatVolume(String vol) {
        if(StringUtils.isNotBlank(vol)) {
            return Float.parseFloat(vol) * 10;
        }
        return null;
    }

    private File getAudioFile(String sessionId, String audioData, String format) throws Exception {
        // 解码 Base64 数据
        byte[] data = Base64.getDecoder().decode(audioData);
        File file = new File(sessionId + "." + format); // 文件路径
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data); // 将字节数组写入文件
        fos.close();
        return file;
    }

}
