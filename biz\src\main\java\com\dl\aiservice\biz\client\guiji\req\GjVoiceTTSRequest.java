package com.dl.aiservice.biz.client.guiji.req;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@NoArgsConstructor
@Data
public class GjVoiceTTSRequest {

    /**
     * 发言人ID不能为空
     */
    private Integer speakerId;
    /**
     * 语速，取值区间：[0-1]
     */
    private String speechRate;
    /**
     * 文本内容不能为空
     */
    private String content;
    /**
     * 否需要字幕文件 0-不需要 1-需要
     */
    private String srtFlag;
    /**
     * 音量，取值区间：[0-1]
     */
    private String volume;
}