package com.dl.aiservice.biz.manager.voiceclone.grammartrans.impl.ivh;

import com.dl.aiservice.biz.common.enums.SymbolE;
import com.dl.aiservice.biz.manager.voiceclone.grammartrans.BaseGrammarTransformer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-12-15 10:02
 */
@Component
public class IvhGrammarTransformer extends BaseGrammarTransformer {
    private static final Logger LOGGER = LoggerFactory.getLogger(IvhGrammarTransformer.class);

    public static String BREAK_TIME_START = "<break time=\"";
    public static String RIGHT = ">";
    public static String PINYIN_START = "<phoneme alphabet=\"py\" ph=\"";
    public static String PINYIN_END = "</phoneme>";
    public static String SAY_AS_START = "<say-as interpret-as=\"";
    public static String SAY_AS_END = "</say-as>";
    public static String DOUBLE_QUOTATION = "\"";
    public static String MS = "ms";

    @Override
    protected String doTransform(Element root) {
        IvhTransfromTempResult tempResult = new IvhTransfromTempResult();
        //递归处理Dom节点
        this.parseNode(root, tempResult);
        return tempResult.getResult();
    }

    private void parseNode(Node node, IvhTransfromTempResult tempResult) {
        if (node.getNodeType() == Node.ELEMENT_NODE) {
            LOGGER.info("Tag: " + node.getNodeName());

            NamedNodeMap attributes = node.getAttributes();
            for (int i = 0; i < attributes.getLength(); i++) {
                Node attribute = attributes.item(i);
                String attributeName = attribute.getNodeName();
                String attributeValue = attribute.getNodeValue();
                LOGGER.info("Attribute: " + attributeName + ", Value: " + attributeValue);

                if ("break".equals(node.getNodeName()) && "time".equals(attributeName)) {
                    //去除ms
                    if (StringUtils.endsWith(attributeValue, "ms")) {
                        attributeValue = attributeValue.replace("ms", "");
                    }
                    if (Objects.isNull(tempResult.getBreakTime())) {
                        tempResult.setBreakTime(Long.valueOf(attributeValue));
                    } else {
                        tempResult.setBreakTime(tempResult.getBreakTime() + Long.valueOf(attributeValue));
                    }
                }
                if ("phoneme".equals(node.getNodeName()) && "ph".equals(attributeName)) {
                    tempResult.setPhoneme(attributeValue);
                }
                if ("say-as".equals(node.getNodeName()) && "interpret-as".equals(attributeName)) {
                    tempResult.setSayAs(attributeValue);
                }

            }

            NodeList childNodes = node.getChildNodes();
            for (int i = 0; i < childNodes.getLength(); i++) {
                Node childNode = childNodes.item(i);
                parseNode(childNode, tempResult);
            }
        } else if (node.getNodeType() == Node.TEXT_NODE) {
            String content = node.getTextContent().trim();
            if (!content.isEmpty()) {
                tempResult.addContent(content);
                LOGGER.info("Content: " + content);
            }
        }
    }

    class IvhTransfromTempResult {

        /**
         * 转换结果
         */
        private StringBuffer result = new StringBuffer();

        /**
         * 停顿时长 ms
         */
        private Long breakTime;

        /**
         * 读音
         */
        private String phoneme;

        /**
         * 数字读法
         */
        private String sayAs;

        /**
         * 添加文本
         *
         * @param content
         */
        public void addContent(String content) {
            if (StringUtils.isBlank(content)) {
                return;
            }
            //处理停顿
            if (Objects.nonNull(breakTime)) {
                result.append(BREAK_TIME_START).append(breakTime).append(DOUBLE_QUOTATION)
                        .append(SymbolE.LEFT_SLASH.getValue()).append(RIGHT);

                //清空breakTime
                breakTime = null;
            }
            //处理多音字
            if (StringUtils.isNotBlank(phoneme)) {
                result.append(PINYIN_START).append(phoneme).append(DOUBLE_QUOTATION).append(RIGHT).append(content)
                        .append(PINYIN_END);
                //清空phoneme
                phoneme = null;
                return;
            }

            //处理数字读法
            if (StringUtils.isNotBlank(sayAs)) {

                result.append(SAY_AS_START).append(sayAs).append(DOUBLE_QUOTATION).append(RIGHT).append(content)
                        .append(SAY_AS_END);

                //清空sayAs
                sayAs = null;
                return;
            }

            result.append(content);
        }

        public String getResult() {
            //末尾停顿处理
            if (Objects.nonNull(breakTime)) {
                result.append(BREAK_TIME_START).append(breakTime).append(MS).append(DOUBLE_QUOTATION)
                        .append(SymbolE.LEFT_SLASH.getValue()).append(RIGHT);
                //清空breakTime
                breakTime = null;
            }

            return result.toString();
        }

        public Long getBreakTime() {
            return breakTime;
        }

        public void setBreakTime(Long breakTime) {
            this.breakTime = breakTime;
        }

        public String getPhoneme() {
            return phoneme;
        }

        public void setPhoneme(String phoneme) {
            this.phoneme = phoneme;
        }

        public String getSayAs() {
            return sayAs;
        }

        public void setSayAs(String sayAs) {
            this.sayAs = sayAs;
        }
    }

    public static void main(String[] args) {
        String input = "随着科技的发展，金融行业正在经历一场深刻的2变革哦。<break time=\"200ms\"/>";

        IvhGrammarTransformer transformer = new IvhGrammarTransformer();
        String result = transformer.grammarTransform(input);
        System.out.println("result:" + result);

        //String input2 = "<speak>大家好<break time=\"100\"/>，我是<phoneme alphabet=\"py\" ph=\"shan4\">单</phoneme>雄。<break time=\"1000ms\"/><break time=\"500ms\"/><break time=\"100\"/>下面我来向您介绍一款产品，<break time=\"1000ms\"/>这支产品非常<phoneme alphabet=\"py\" ph=\"xing2\">行</phoneme>。今天它<say-as interpret-as=\"digits\">50</say-as>领涨,科创<say-as interpret-as=\"cardinal\">30</say-as>领跌。</speak>";
        String input2 =
                "<break time=\"1000ms\"/><say-as interpret-as=\"digits\">2024</say-as>年使用此设备<break time=\"1000ms\"/><phoneme alphabet=\"py\" ph=\"di4\">的</phoneme>\n"
                        + "其他<say-as interpret-as=\"cardinal\">2024</say-as>用户";
        String result2 = transformer.grammarTransform(input2);
        System.out.println("result2:" + result2);
    }
}
