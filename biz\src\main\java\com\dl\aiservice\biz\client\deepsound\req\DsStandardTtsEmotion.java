package com.dl.aiservice.biz.client.deepsound.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DsStandardTtsEmotion {


    /**
     * 待合成情感文本，长度限制8192字符，支持情感标签发音方式，参考（5）
     */
    @JsonProperty("text")
    private String text;

    /**
     * 支持的情感类型标签：
     * plain: 平静
     * amazing: 惊讶
     * fear: 恐惧
     * sad: 悲伤
     * happy: 喜悦
     * disgust: 厌恶
     * anger: 愤怒
     */
    @JsonProperty("emotion_type")
    private String emotionType;


}
