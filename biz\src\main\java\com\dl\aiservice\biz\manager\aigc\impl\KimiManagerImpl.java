package com.dl.aiservice.biz.manager.aigc.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.kimi.KimiClient;
import com.dl.aiservice.biz.client.kimi.config.KimiConfig;
import com.dl.aiservice.biz.client.kimi.consts.KimiConst;
import com.dl.aiservice.biz.client.kimi.req.ChatCompletionRequest;
import com.dl.aiservice.biz.client.kimi.resp.ChatCompletionChoice;
import com.dl.aiservice.biz.client.kimi.resp.ChatCompletionMessage;
import com.dl.aiservice.biz.client.kimi.resp.ChatCompletionResponse;
import com.dl.aiservice.biz.client.kimi.resp.EstimateTokenCountResponse;
import com.dl.aiservice.biz.client.kimi.resp.FileContentResponse;
import com.dl.aiservice.biz.client.kimi.resp.FileDeleteResponse;
import com.dl.aiservice.biz.client.kimi.resp.FileUploadResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.DownloadUtil;
import com.dl.aiservice.biz.manager.aigc.KimiManager;
import com.dl.aiservice.biz.manager.aigc.convert.KimiConvert;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.net.URLDecoder;
import java.util.List;
import java.util.Objects;

import static com.dl.aiservice.share.aichat.consts.AiChatKimiConst.MOONSHOT_128K;
import static com.dl.aiservice.share.aichat.consts.AiChatKimiConst.MOONSHOT_128K_TOKEN_LIMIT;
import static com.dl.aiservice.share.aichat.consts.AiChatKimiConst.MOONSHOT_32K;
import static com.dl.aiservice.share.aichat.consts.AiChatKimiConst.MOONSHOT_32K_TOKEN_LIMIT;
import static com.dl.aiservice.share.aichat.consts.AiChatKimiConst.MOONSHOT_8K;
import static com.dl.aiservice.share.aichat.consts.AiChatKimiConst.MOONSHOT_8K_TOKEN_LIMIT;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 11:22
 */
@Component
public class KimiManagerImpl implements KimiManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(KimiManagerImpl.class);

    @Autowired
    private KimiClient kimiClient;

    @Resource
    private KimiConfig kimiConfig;

    @Value("${dl.fileTempPath}")
    public String localPathPrefix;

    /*private static final String MOONSHOT_8K = "moonshot-v1-8k";

    private static final String MOONSHOT_32K = "moonshot-v1-32k";

    private static final String MOONSHOT_128K = "moonshot-v1-128k";

    private static final Long MOONSHOT_8K_TOKEN_LIMIT = 8 * 1024L;

    private static final Long MOONSHOT_32K_TOKEN_LIMIT = 32 * 1024L;

    private static final Long MOONSHOT_128K_TOKEN_LIMIT = 128 * 1024L;*/

    private static final String FILE_UPLOAD_PURPOSE = "file-extract";

    @Override
    public ChatCompletionMessage chatCompletion(String model, Integer respMaxToken,
            List<ChatCompletionMessage> messages) {
        Assert.isTrue(CollectionUtils.isNotEmpty(messages), "对话列表不能为空");
        if (StringUtils.isBlank(model)) {
            model = kimiConfig.getModel();
        }
        if (Objects.isNull(respMaxToken)) {
            respMaxToken = kimiConfig.getRespMaxToken();
        }
        ChatCompletionRequest request = KimiConvert.buildChatCompletionRequest(messages, model, respMaxToken);

        ChatCompletionResponse response = null;
        try {
            response = kimiClient.chatCompletion(request);
        } catch (Exception e) {
            LOGGER.error("调用kimi chat接口发生异常！ request:{},,,,response:{},,,e:{}", JSONUtil.toJsonStr(request),
                    JSONUtil.toJsonStr(response), e);
            throw e;
        }

        List<ChatCompletionChoice> choices = response.getChoices();
        if (CollectionUtils.isEmpty(choices)) {
            throw BusinessServiceException.getInstance("kimi返回为空");
        }
        ChatCompletionChoice choice = choices.get(0);
        if (Objects.isNull(choice.getMessage())) {
            throw BusinessServiceException.getInstance("kimi返回为空");
        }
        return choice.getMessage();
    }

    @Override
    public InputStream chatCompletionStream(Long userId, String model, Integer respMaxToken,
            List<ChatCompletionMessage> messages) {
        Assert.isTrue(CollectionUtils.isNotEmpty(messages), "对话列表不能为空");
        if (StringUtils.isBlank(model)) {
            model = kimiConfig.getModel();
        }
        if (Objects.isNull(respMaxToken)) {
            respMaxToken = kimiConfig.getRespMaxToken();
        }
        ChatCompletionRequest request = KimiConvert.buildChatCompletionRequest(messages, model, respMaxToken);
        request.setStream(Boolean.TRUE);
        InputStream inputStream = kimiClient.chatCompletionStream(request);
        return inputStream;
    }

    @Override
    public String fileUpload(String fileUrl) {
        Assert.isTrue(StringUtils.isNotBlank(fileUrl), "文件url不能为空");
        File file = null;
        try {
            String decodeUrl = URLDecoder.decode(fileUrl, "UTF-8");
            String obejetKey = DownloadUtil.getCosKeyFromUrlString(decodeUrl);
            //要对obejctKey做下encode，不然一些字符无法识别。比如（）
            String encodeUrl = DownloadUtil.encodeCosKeyFromUrlString(decodeUrl);
            //文件下载
            file = DownloadUtil.downloadFile(encodeUrl, localPathPrefix + obejetKey);
        } catch (Exception e) {
            LOGGER.error("处理文件下载出现异常! fileUrl:{}, e:{}", fileUrl, e);
            FileUtils.deleteQuietly(file);
            throw BusinessServiceException.getInstance(KimiConst.FILE_DOWNLOAD_ERROR_MSG);
        }

        FileUploadResponse response = null;
        try {
            response = kimiClient.uploadAiFiles(file, FILE_UPLOAD_PURPOSE);
        } catch (BusinessServiceException e) {
            LOGGER.error("调用kimi文件上传接口发生异常!fileUrl:{},,,response:{},,,e:{}", fileUrl, JSONUtil.toJsonStr(response), e);
            //业务异常继续往上抛
            throw e;
        } catch (Exception e) {
            LOGGER.error("调用kimi文件上传接口发生异常!fileUrl:{},,,response:{},,,e:{}", fileUrl, JSONUtil.toJsonStr(response), e);
            throw BusinessServiceException.getInstance(KimiConst.FILE_UPLOAD_ERROR_MSG);
        } finally {
            FileUtils.deleteQuietly(file);
        }

        if (!Const.RESP_CODE_OK.equals(response.getStatus())) {
            throw BusinessServiceException.getInstance(KimiConst.FILE_UPLOAD_ERROR_MSG);
        }

        return response.getId();
    }

    @Override
    public String fileUploadFile(MultipartFile multipartFile) {
        Assert.notNull(multipartFile, "文件不能为空");

        // 创建一个 File 对象，指定文件的保存路径
        File file = null;
        try {
            file = new File(localPathPrefix + System.currentTimeMillis() + "-" + multipartFile.getOriginalFilename());
            multipartFile.transferTo(file);
        } catch (Exception e) {
            LOGGER.error("文件转换发生异常！fileName:{}", multipartFile.getOriginalFilename());
            FileUtils.deleteQuietly(file);
            throw BusinessServiceException.getInstance(KimiConst.FILE_UPLOAD_ERROR_MSG);
        }

        FileUploadResponse response = null;
        try {
            response = kimiClient.uploadAiFiles(file, FILE_UPLOAD_PURPOSE);
        } catch (BusinessServiceException e) {
            LOGGER.error("调用kimi文件上传接口发生异常!fileName:{},,,response:{},,,e:{}", file.getName(),
                    JSONUtil.toJsonStr(response), e);
            //业务异常继续往上抛
            throw e;
        } catch (Exception e) {
            LOGGER.error("调用kimi文件上传接口发生异常!fileName:{},,,response:{},,,e:{}", file.getName(),
                    JSONUtil.toJsonStr(response), e);
            throw BusinessServiceException.getInstance(KimiConst.FILE_UPLOAD_ERROR_MSG);
        } finally {
            FileUtils.deleteQuietly(file);
        }

        if (!Const.RESP_CODE_OK.equals(response.getStatus())) {
            throw BusinessServiceException.getInstance(KimiConst.FILE_UPLOAD_ERROR_MSG);
        }

        return response.getId();
    }

    @Override
    public String fileContent(String fileId) {
        Assert.isTrue(StringUtils.isNotBlank(fileId), "文件id不能为空");

        FileContentResponse fileContentResponse = kimiClient.fileContent(fileId);
        if (Objects.isNull(fileContentResponse)) {
            throw BusinessServiceException.getInstance(KimiConst.FILE_CONTENT_EXTRACT_ERROR_MSG);
        }

        return fileContentResponse.getContent();
    }

    @Override
    public boolean fileDelete(String fileId) {
        Assert.isTrue(StringUtils.isNotBlank(fileId), "文件id不能为空");

        FileDeleteResponse fileDeleteResponse = kimiClient.fileDelete(fileId);
        if (Objects.isNull(fileDeleteResponse) || !fileDeleteResponse.isDeleted()) {
            LOGGER.error("kimi文件删除失败！,fileId:{},,,fileDeleteResponse:{}", fileId,
                    JSONUtil.toJsonStr(fileDeleteResponse));
            return false;
        }

        return true;
    }

    @Override
    public Long estimateTokenCount(String model, Integer respMaxToken, List<ChatCompletionMessage> messages) {
        Assert.isTrue(CollectionUtils.isNotEmpty(messages), "对话列表不能为空");
        ChatCompletionRequest request = KimiConvert.buildChatCompletionRequest(messages, model, respMaxToken);
        EstimateTokenCountResponse response = kimiClient.estimateTokenCount(request);
        if (Objects.isNull(response) || Objects.nonNull(response.getError())) {
            throw BusinessServiceException.getInstance(KimiConst.ESTIMATE_TOKEN_COUNT_ERROR_MSG);
        }

        return response.getData().getTotalTokens();
    }

    @Override
    public boolean estimateTokenCountExceedLimit(String model, Integer respMaxToken,
            List<ChatCompletionMessage> messages) {
        if (StringUtils.isBlank(model)) {
            model = kimiConfig.getModel();
        }
        if (Objects.isNull(respMaxToken)) {
            respMaxToken = kimiConfig.getRespMaxToken();
        }
        Long tokenCount = this.estimateTokenCount(model, respMaxToken, messages);

        Long limit = null;
        switch (model) {
        case MOONSHOT_8K:
            limit = MOONSHOT_8K_TOKEN_LIMIT;
            break;
        case MOONSHOT_32K:
            limit = MOONSHOT_32K_TOKEN_LIMIT;
            break;
        case MOONSHOT_128K:
            limit = MOONSHOT_128K_TOKEN_LIMIT;
            break;
        default:
            throw BusinessServiceException.getInstance("kimi model配置有误!");
        }
        boolean isExceedLimit = tokenCount + respMaxToken > limit;
        if (isExceedLimit) {
            LOGGER.info("预估耗费token数量已超过限制,预估耗费token:{},model:{},,,respMaxToken:{},,,limit:{}", tokenCount, model,
                    respMaxToken, limit);
        }

        return isExceedLimit;
    }

}
