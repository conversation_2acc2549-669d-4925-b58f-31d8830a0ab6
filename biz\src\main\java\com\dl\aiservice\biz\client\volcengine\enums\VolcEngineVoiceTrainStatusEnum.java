package com.dl.aiservice.biz.client.volcengine.enums;

import java.util.Objects;

/**
 * 火山引擎声音训练的状态枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-29 11:26
 */
public enum VolcEngineVoiceTrainStatusEnum {

    //NotFound = 0 Training = 1 Success = 2 Failed = 3 Active = 4 状态为4(Active)时可调用tts合成音频
    NOT_FOUND(0, "未找到"),
    TRAINING(1, "训练中"),
    SUCCESS(2, "训练成功"),
    FAILED(3, "训练失败"),
    ACTIVE(4, "已激活");

    private Integer status;

    private String desc;

    VolcEngineVoiceTrainStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static VolcEngineVoiceTrainStatusEnum parse(Integer status) {
        if (Objects.isNull(status)) {
            return null;
        }
        for (VolcEngineVoiceTrainStatusEnum statusEnum : VolcEngineVoiceTrainStatusEnum.values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }
}
