package com.dl.aiservice.share.videoproduce.dto.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: SubtitleTrackParam
 * @author: zhousx
 * @date: 2023/2/10 11:43
 */
@Data
public class SubtitleTrackDTO {
    @JsonProperty("SubtitleTrackClips")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<TrackClipDTO> subtitleTrackClips;
}
