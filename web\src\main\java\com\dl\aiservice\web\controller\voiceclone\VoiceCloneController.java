package com.dl.aiservice.web.controller.voiceclone;

import com.dl.aiservice.biz.common.annotation.NotAuth;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.RedisUtil;
import com.dl.aiservice.biz.manager.voiceclone.enums.VoiceCloneEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.AudioCheckParamDTO;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.EnvCheckParamDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainJobDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainJobPageQueryDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultPageQueryDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultUpdateNameParamDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.jsonwebtoken.lang.Assert;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/voice/clone")
public class VoiceCloneController {

    @Resource
    private VoiceCloneProcessor voiceCloneProcessor;
    @Resource
    private TrainCallbackProcessor trainCallbackProcessor;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ChannelUtil channelUtil;

    private static final String VOICE_CLONE_LOCK = "voice_clone_lock:";

    private static final Long VOICE_CLONE_LOCK_TIMEOUT = 60L;

    @ApiOperation("环境音检测接口")
    @PostMapping("/env/check")
    ResultModel envCheck(@RequestBody @Validated EnvCheckParamDTO param) {
        return voiceCloneProcessor.envCheck(param.getUrl());
    }

    @ApiOperation("音质检测接口")
    @PostMapping("/audio/check")
    ResultModel<AudioCheckResponseDTO> audioCheck(@RequestBody @Validated AudioCheckParamDTO param) {
        return voiceCloneProcessor.audioCheck(param.getUrl(), param.getText(), param.getLanguage());
    }

    @ApiOperation("声纹模型训练")
    @PostMapping("/audio/train")
    ResultModel<AudioTrainResponseDTO> audioTrain(@RequestBody @Validated AudioTrainParamDTO param) {
        //上锁：  tenantCode+channel+speaker+extModelCode
        String key = VOICE_CLONE_LOCK + channelUtil.getTenantCode() + "-" + channelUtil.getChannel() + "-" + param
                .getSpeaker() + "-" + param.getExtModelCode();
        if (!redisUtil.tryLockAndSetTimeout(key, VOICE_CLONE_LOCK_TIMEOUT)) {
            return ResultModel.error("-1", "当前声音训练已在处理中，请勿频繁提交");
        }
        try {
            return voiceCloneProcessor.audioTrain(param);
        } finally {
            redisUtil.del(key);
        }
    }

    /**
     * 模型查询
     * 查询已提交训练模型 的状态。
     *
     * @param trainJobId
     * @return
     */
    @ApiOperation("声纹模型查询")
    @PostMapping("/train/detail")
    ResultModel<AudioTrainDetailResponseDTO> trainDetail(@RequestParam String trainJobId) {
        return voiceCloneProcessor.queryAudioTrain(trainJobId);
    }

    /**
     * 语音合成
     * 将待合成的文本上传到服务端，服务端返回文本的语音合成结果，开发者需要保证在语音合成结果返回之前连接不中断
     *
     * @return
     */
    @ApiOperation("语音合成")
    @PostMapping("/tts")
    ResultModel<TTSResponseDTO> tts(@RequestBody @Validated TTSProduceParamDTO param) {
        return voiceCloneProcessor.ttsProduce(param);
    }

    @ApiOperation("训练结果回调接口")
    @PostMapping("/callback/{trainChannel}")
    @NotAuth
    public ResultModel<Boolean> trainCallback(HttpServletRequest request, @PathVariable Integer trainChannel) {
        VoiceCloneEnum e = VoiceCloneEnum.getByCode(trainChannel);
        Assert.notNull(e, "请求非法!");
        return trainCallbackProcessor.trainCallback(request, ServiceChannelEnum.getByCode(e.getCode()));
    }

    @ApiOperation("分页查询声音训练结果")
    @PostMapping("/trainresult/page")
    public ResultPageModel<VoiceTrainResultDTO> pageTrainResult(@RequestBody VoiceTrainResultPageQueryDTO queryDTO) {
        return voiceCloneProcessor.pageTrainResult(queryDTO);
    }

    @ApiOperation("修改声音训练结果的训练名")
    @PostMapping("/trainresult/updatename")
    public ResultModel<Void> updateTrainResultName(
            @RequestBody @Validated VoiceTrainResultUpdateNameParamDTO paramDTO) {
        return voiceCloneProcessor.updateTrainResultName(paramDTO);
    }

    @ApiOperation("分页查询声音训练结果下的训练任务")
    @PostMapping("/trainresult/job/page")
    public ResultPageModel<VoiceTrainJobDTO> pageTrainResultJob(
            @RequestBody @Validated VoiceTrainJobPageQueryDTO queryDTO) {
        return voiceCloneProcessor.pageTrainResultJob(queryDTO);
    }

}