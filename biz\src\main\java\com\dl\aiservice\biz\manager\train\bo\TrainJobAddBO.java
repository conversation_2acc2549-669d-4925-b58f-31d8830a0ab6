package com.dl.aiservice.biz.manager.train.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-29 09:20
 */
@Data
public class TrainJobAddBO {

    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    private Integer channel;

    /**
     * 训练类型：0 数字人训练；1 声音训练
     *
     * @See:com.dl.aiservice.biz.manager.train.enums.TrainTypeEnum
     */
    private Integer jobType;

    /**
     * 性别：1 男 ；2 女
     */
    private Integer gender;

    /**
     * 第三方训练id
     */
    private String extJobId;

    /**
     * 第三方训练人模型编号
     */
    private String extModelCode;

    /**
     * 训练状态：1 训练中；0 训练完成；-1 训练失败
     */
    private Integer status;

    /**
     * 请求三方报文
     */
    private String jobContent;

    /**
     * 回调业务url
     */
    private String callbackUrl;

    /**
     * 训练名称 深声：声讯编码
     */
    private String trainName;

    /**
     * 训练来源，1-A端，2-D端
     */
    private Integer source;

}
