package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class IvhVirtualmanImage {

    /**
     * 定义播报的⻆⾊、服装、姿态、分辨率等信息
     */
    @JsonProperty(value = "VirtualmanKey")
    private String virtualmanKey;
    /**
     * ⻆⾊名称
     */
    @JsonProperty(value = "Name")
    private String name;
    /**
     * ⻆⾊code
     */
    @JsonProperty(value = "Code")
    private String code;
    /**
     * 数智⼈服装
     */
    @JsonProperty(value = "ClothesName")
    private String clothesName;
    /**
     * 数智⼈姿态
     */
    @JsonProperty(value = "PoseName")
    private String poseName;
    /**
     * 数智⼈分辨率
     */
    @JsonProperty(value = "Resolution")
    private String resolution;
    /**
     * 数智⼈头像图⽚url
     */
    @JsonProperty(value = "HeaderImage")
    private String headerImage;
    /**
     * 数智⼈姿态图⽚url
     */
    @JsonProperty(value = "PoseImage")
    private String poseImage;
    /**
     * 数智⼈服装图⽚url
     */
    @JsonProperty(value = "ClothesImage")
    private String clothesImage;
    /**
     * 数智⼈⽀持的驱动类型1. Text: ⽂本驱动2. OriginalVoice: 原声⾳频驱动3. ModulatedVoice: 变声⾳频驱动
     */
    @JsonProperty(value = "SupportDriverTypes")
    private List<String> supportDriverTypes;


}