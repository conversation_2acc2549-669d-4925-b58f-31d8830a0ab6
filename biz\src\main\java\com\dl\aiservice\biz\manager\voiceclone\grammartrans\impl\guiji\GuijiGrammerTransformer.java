package com.dl.aiservice.biz.manager.voiceclone.grammartrans.impl.guiji;

import com.dl.aiservice.biz.common.enums.SymbolE;
import com.dl.aiservice.biz.manager.voiceclone.grammartrans.BaseGrammarTransformer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 硅基语法转换
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-07 09:38
 */
@Component
public class GuijiGrammerTransformer extends BaseGrammarTransformer {
    private static final Logger LOGGER = LoggerFactory.getLogger(GuijiGrammerTransformer.class);

    private static final String DELAY = "delay";
    private static final String VALUE = "value";
    private static final String GRAMMAR_START = "<grammar";
    private static final String GRAMMAR_END = "</grammar>";
    private static final String TYPE_PINYIN = "type=\"pinyin\"";
    private static final String TYPE_CUSTOM = "type=\"custom\"";
    public static String LEFT = "<";
    public static String RIGHT = ">";
    public static String DOUBLE_QUOTATION = "\"";

    //读数字
    public static String DIGITS = "digits";
    //读数值
    public static String CARDINAL = "cardinal";

    @Override
    protected String doTransform(Element root) {
        GuijiDmTransfromTempResult tempResult = new GuijiDmTransfromTempResult();
        //递归处理Dom节点
        this.parseNode(root, tempResult);
        return tempResult.getResult().toString();
    }

    private void parseNode(Node node, GuijiDmTransfromTempResult tempResult) {
        if (node.getNodeType() == Node.ELEMENT_NODE) {
            LOGGER.info("Tag: " + node.getNodeName());

            NamedNodeMap attributes = node.getAttributes();
            for (int i = 0; i < attributes.getLength(); i++) {
                Node attribute = attributes.item(i);
                String attributeName = attribute.getNodeName();
                String attributeValue = attribute.getNodeValue();
                LOGGER.info("Attribute: " + attributeName + ", Value: " + attributeValue);

                if ("break".equals(node.getNodeName()) && "time".equals(attributeName)) {
                    //去除ms
                    if (StringUtils.endsWith(attributeValue, "ms")) {
                        attributeValue = attributeValue.replace("ms", "");
                    }
                    if (Objects.isNull(tempResult.getBreakTime())) {
                        tempResult.setBreakTime(Long.valueOf(attributeValue));
                    } else {
                        tempResult.setBreakTime(tempResult.getBreakTime() + Long.valueOf(attributeValue));
                    }
                }
                if ("phoneme".equals(node.getNodeName()) && "ph".equals(attributeName)) {
                    tempResult.setPhoneme(attributeValue);
                }
                if ("say-as".equals(node.getNodeName()) && "interpret-as".equals(attributeName)) {
                    tempResult.setSayAs(attributeValue);
                }

            }

            NodeList childNodes = node.getChildNodes();
            for (int i = 0; i < childNodes.getLength(); i++) {
                Node childNode = childNodes.item(i);
                parseNode(childNode, tempResult);
            }
        } else if (node.getNodeType() == Node.TEXT_NODE) {
            String content = node.getTextContent().trim();
            if (!content.isEmpty()) {
                tempResult.addContent(content);
                LOGGER.info("Content: " + content);
            }
        }
    }

    class GuijiDmTransfromTempResult {

        /**
         * 转换结果
         */
        private StringBuffer result = new StringBuffer();

        /**
         * 停顿时长 ms
         */
        private Long breakTime;

        /**
         * 读音
         */
        private String phoneme;

        /**
         * 数字读法
         */
        private String sayAs;

        /**
         * 添加文本
         *
         * @param content
         */
        public void addContent(String content) {
            if (StringUtils.isBlank(content)) {
                return;
            }
            //处理停顿
            if (Objects.nonNull(breakTime)) {
                //将ms转换成s
                result.append(LEFT).append(DELAY).append(" ").append(VALUE).append(SymbolE.EQUAL.getValue())
                        .append(DOUBLE_QUOTATION)
                        .append(new BigDecimal(breakTime).divide(new BigDecimal(1000), 1, RoundingMode.HALF_UP))
                        .append(DOUBLE_QUOTATION).append(SymbolE.LEFT_SLASH.getValue()).append(RIGHT);

                //清空breakTime
                breakTime = null;
            }
            //处理多音字
            if (StringUtils.isNotBlank(phoneme)) {
                result.append(GRAMMAR_START).append(" ").append(TYPE_PINYIN).append(" ").append(VALUE)
                        .append(SymbolE.EQUAL.getValue()).append(DOUBLE_QUOTATION).append(phoneme)
                        .append(DOUBLE_QUOTATION).append(RIGHT).append(content).append(GRAMMAR_END);
                //清空phoneme
                phoneme = null;
                return;
            }

            //处理数字读法
            if (StringUtils.isNotBlank(sayAs)) {
                //类型转换 数值读法
                if (CARDINAL.equals(sayAs)) {
                    //硅基好像默认是数值读法的
                    result.append(content);
                    //数字读法
                } else if (DIGITS.equals(sayAs)) {
                    String numberPronunciation = NumberToNumberConvertor.convertNumberToWord(Double.valueOf(content));
                    LOGGER.info("guiji numberPronunciation:{}", numberPronunciation);
                    result.append(GRAMMAR_START).append(" ").append(TYPE_CUSTOM).append(" ").append(VALUE)
                            .append(SymbolE.EQUAL.getValue()).append(DOUBLE_QUOTATION).append(numberPronunciation)
                            .append(DOUBLE_QUOTATION).append(RIGHT).append(content).append(GRAMMAR_END);
                }
                //清空sayAs
                sayAs = null;
                return;
            }

            result.append(content);
        }

        public StringBuffer getResult() {
            //末尾停顿处理
            if (Objects.nonNull(breakTime)) {
                //将ms转换成s
                result.append(LEFT).append(DELAY).append(" ").append(VALUE).append(SymbolE.EQUAL.getValue())
                        .append(DOUBLE_QUOTATION)
                        .append(new BigDecimal(breakTime).divide(new BigDecimal(1000), 1, RoundingMode.HALF_UP))
                        .append(DOUBLE_QUOTATION).append(SymbolE.LEFT_SLASH.getValue()).append(RIGHT);

                //清空breakTime
                breakTime = null;
            }
            return result;
        }

        public Long getBreakTime() {
            return breakTime;
        }

        public void setBreakTime(Long breakTime) {
            this.breakTime = breakTime;
        }

        public String getPhoneme() {
            return phoneme;
        }

        public void setPhoneme(String phoneme) {
            this.phoneme = phoneme;
        }

        public String getSayAs() {
            return sayAs;
        }

        public void setSayAs(String sayAs) {
            this.sayAs = sayAs;
        }
    }

    public static void main(String[] args) {
        /*String input =
                "<speak>大家好，我是<phoneme alphabet=\"py\" ph=\"shan4\">单</phoneme>雄。<break time=\"1000ms\"/><break time=\"500ms\"/><break time=\"100\"/>下面我来向您介绍一款产品，<break time=\"1000ms\"/>这支产品非常<phoneme alphabet=\"py\" ph=\"xing2\">行</phoneme>。今天它<say-as interpret-as=\"digits\">50</say-as>领涨,科创<say-as interpret-as=\"cardinal\">30</say-as>领跌。"
                        + "<say-as interpret-as=\"cardinal\">30.12</say-as><say-as interpret-as=\"digits\">91232320.23</say-as><break time=\"300ms\"/></speak>";*/
        String input =
                "<break time=\"1000ms\"/><say-as interpret-as=\"digits\">2024</say-as>年使用此设备<break time=\"1000ms\"/><phoneme alphabet=\"py\" ph=\"di4\">的</phoneme>\n"
                        + "其他<say-as interpret-as=\"cardinal\">2024</say-as>用户";
        GuijiGrammerTransformer transformer = new GuijiGrammerTransformer();
        String result = transformer.grammarTransform(input);
        System.out.println("result:" + result);
    }

}
