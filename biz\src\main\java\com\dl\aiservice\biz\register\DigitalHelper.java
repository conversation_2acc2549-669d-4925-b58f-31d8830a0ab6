package com.dl.aiservice.biz.register;

import com.dl.aiservice.biz.service.digital.BaseDigitalService;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@AllArgsConstructor
public class DigitalHelper {

    private final DigitalRegister diagnosisRegister;

    @PostConstruct
    public void init() {
        diagnosisRegister.init();
    }

    public BaseDigitalService get(ServiceChannelEnum typeEnum) {
        return diagnosisRegister.get(typeEnum);
    }
}
