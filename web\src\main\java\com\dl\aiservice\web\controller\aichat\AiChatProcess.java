package com.dl.aiservice.web.controller.aichat;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.kimi.consts.KimiConst;
import com.dl.aiservice.biz.client.kimi.enums.ChatMessageRoleEnum;
import com.dl.aiservice.biz.client.kimi.resp.ChatCompletionMessage;
import com.dl.aiservice.biz.manager.aigc.KimiManager;
import com.dl.aiservice.share.aichat.AiFileContentAndTitleRespDTO;
import com.dl.aiservice.share.aichat.AiMultiChatMessageDTO;
import com.dl.aiservice.share.aichat.AiMultiChatRequestDTO;
import com.dl.aiservice.share.aichat.AiSingleChatRequestDTO;
import com.dl.aiservice.share.aichat.AiSingleChatResponseDTO;
import com.dl.aiservice.share.aichat.errorcode.AiChatErrorCodeEnum;
import com.dl.aiservice.share.aichat.errorcode.AiMultiChatResponseDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 17:01
 */
@Component
public class AiChatProcess {
    private static final Logger LOGGER = LoggerFactory.getLogger(AiChatProcess.class);

    @Resource
    private KimiManager kimiManager;

    private static final String FILE_CONTENT_PREFIX = "参考资料:";

    /**
     * 提取文件标题的预设文案
     */
    private static final String EXTRACT_FILE_TITLE_PRESUPPOSE_TEXT = "请提取出下面内容的标题，不超过10个字";

    public ResultModel<AiSingleChatResponseDTO> singleChat(AiSingleChatRequestDTO requestDTO) {
        //1.构建用户文本消息
        List<ChatCompletionMessage> questionMsgs = new ArrayList<>();
        ChatCompletionMessage textMessage = new ChatCompletionMessage();
        textMessage.setRole(ChatMessageRoleEnum.USER.getCode());
        textMessage.setContent(requestDTO.getUserMessage());
        questionMsgs.add(textMessage);

        //2.文件上传、获取文件内容、构建用户文件消息
        String fileId = null;
        if (StringUtils.isNotBlank(requestDTO.getFileUrl())) {
            fileId = kimiManager.fileUpload(requestDTO.getFileUrl());
            String fileContent = kimiManager.fileContent(fileId);
            ChatCompletionMessage fileMessage = new ChatCompletionMessage();
            fileMessage.setRole(ChatMessageRoleEnum.USER.getCode());
            fileMessage.setContent(FILE_CONTENT_PREFIX + fileContent);
            questionMsgs.add(fileMessage);
        }

        try {
            //3.预估耗费token数量是否超过限制
            if (kimiManager
                    .estimateTokenCountExceedLimit(requestDTO.getModel(), requestDTO.getRespMaxToken(), questionMsgs)) {
                throw BusinessServiceException.getInstance(AiChatErrorCodeEnum.CONTENT_LENGTH_TOO_LONG.getCode(),
                        AiChatErrorCodeEnum.CONTENT_LENGTH_TOO_LONG.getMessage());
            }

            //4.调用chat接口
            ChatCompletionMessage answerMsg = kimiManager
                    .chatCompletion(requestDTO.getModel(), requestDTO.getRespMaxToken(), questionMsgs);

            AiSingleChatResponseDTO aiSingleChatResponseDTO = new AiSingleChatResponseDTO();
            aiSingleChatResponseDTO.setContent(answerMsg.getContent());
            return ResultModel.success(aiSingleChatResponseDTO);
        } finally {
            //5.删除文件
            if (StringUtils.isNotBlank(fileId)) {
                kimiManager.fileDelete(fileId);
            }
        }

    }

    public ResultModel<AiMultiChatResponseDTO> multiChat(AiMultiChatRequestDTO requestDTO) {
        LOGGER.info("多条对话，只返回最后的响应。入参:requestDTO:{}", JSONUtil.toJsonStr(requestDTO));

        //消息列表
        List<ChatCompletionMessage> historyMsgs = new ArrayList<>();

        String finalAnswer = "";
        //1.遍历请求中的用户消息列表
        for (int i = 0; i < requestDTO.getMessages().size(); i++) {
            AiMultiChatMessageDTO chatMessageDTO = requestDTO.getMessages().get(i);

            //构建用户请求
            ChatCompletionMessage questionMessage = new ChatCompletionMessage();
            questionMessage.setRole(ChatMessageRoleEnum.USER.getCode());
            questionMessage.setContent(chatMessageDTO.getText());
            historyMsgs.add(questionMessage);

            //2.预估耗费token数量是否超过限制
            if (kimiManager
                    .estimateTokenCountExceedLimit(requestDTO.getModel(), requestDTO.getRespMaxToken(), historyMsgs)) {
                throw BusinessServiceException.getInstance(AiChatErrorCodeEnum.CONTENT_LENGTH_TOO_LONG.getCode(),
                        AiChatErrorCodeEnum.CONTENT_LENGTH_TOO_LONG.getMessage());
            }

            //3.调用kimi接口得到响应
            ChatCompletionMessage answerMessage = null;
            answerMessage = kimiManager
                    .chatCompletion(requestDTO.getModel(), requestDTO.getRespMaxToken(), historyMsgs);

            //非最后一次，将响应添加到历史消息列表中
            if (i < requestDTO.getMessages().size() - 1) {
                historyMsgs.add(answerMessage);
            } else {
                //最后一次，赋值给finalAnswer
                finalAnswer = answerMessage.getContent();
            }

        }
        return ResultModel.success(new AiMultiChatResponseDTO(finalAnswer));
    }

    public void singleChatStream(HttpServletResponse resp, AiSingleChatRequestDTO requestDTO) throws IOException {
        LOGGER.info("流式地单条对话入参:requestDTO:{}", JSONUtil.toJsonStr(requestDTO));

        OutputStream out = resp.getOutputStream();
        // 设置响应类型为流式
        resp.setContentType("text/event-stream");
        resp.setCharacterEncoding("UTF-8");
        resp.setHeader("Transfer-Encoding", "chunked");
        resp.setHeader("X-Accel-Buffering", "no");

        //1.构建用户文本消息
        List<ChatCompletionMessage> questionMsgs = new ArrayList<>();
        ChatCompletionMessage textMessage = new ChatCompletionMessage();
        textMessage.setRole(ChatMessageRoleEnum.USER.getCode());
        textMessage.setContent(requestDTO.getUserMessage());
        questionMsgs.add(textMessage);

        //2.文件上传、获取文件内容、构建用户文件消息
        String fileId = null;
        if (StringUtils.isNotBlank(requestDTO.getFileUrl())) {
            try {
                fileId = kimiManager.fileUpload(requestDTO.getFileUrl());
                String fileContent = kimiManager.fileContent(fileId);
                ChatCompletionMessage fileMessage = new ChatCompletionMessage();
                fileMessage.setRole(ChatMessageRoleEnum.USER.getCode());
                fileMessage.setContent(FILE_CONTENT_PREFIX + fileContent.replaceAll("\r", "").replaceAll("\n", ""));
                questionMsgs.add(fileMessage);
            } catch (BusinessServiceException e) {
                this.wrapperErrorWriteSseEvent(out, e.getErrMsg());
                this.wrapperDoneWriteSseEvent(out);
                return;
            } catch (Exception e) {
                LOGGER.error("处理文件上传和获取文件内容发生异常!fileUrl:{},,,e:", requestDTO.getFileUrl(), e);
                this.wrapperErrorWriteSseEvent(out, KimiConst.FILE_CONTENT_EXTRACT_ERROR_MSG);
                this.wrapperDoneWriteSseEvent(out);
                return;
            }
        }

        //3.预估耗费token数量是否超过限制
        if (kimiManager
                .estimateTokenCountExceedLimit(requestDTO.getModel(), requestDTO.getRespMaxToken(), questionMsgs)) {
            this.wrapperErrorWriteSseEvent(out, KimiConst.CONTENT_LENGTH_TOO_LONG_ERROR_MSG);
            this.wrapperDoneWriteSseEvent(out);
            return;
        }

        //4.调用chat接口(流式)
        this.invokeChatCompletionStream(requestDTO.getUserId(), out, requestDTO.getModel(),
                requestDTO.getRespMaxToken(), questionMsgs, fileId);

    }

    public void multiChatFinalStream(HttpServletResponse resp, AiMultiChatRequestDTO requestDTO) throws IOException {
        LOGGER.info("多条对话，最后的响应以流式返回。入参:requestDTO:{}", JSONUtil.toJsonStr(requestDTO));

        OutputStream out = resp.getOutputStream();
        // 设置响应类型为流式
        resp.setContentType("text/event-stream");
        resp.setCharacterEncoding("UTF-8");
        resp.setHeader("Transfer-Encoding", "chunked");
        resp.setHeader("X-Accel-Buffering", "no");

        //消息列表
        List<ChatCompletionMessage> historyMsgs = new ArrayList<>();

        //1.遍历请求中的用户消息列表 不遍历最后一条用户消息(最后一条消息下面会单独处理)
        for (int i = 0; i < requestDTO.getMessages().size() - 1; i++) {
            AiMultiChatMessageDTO chatMessageDTO = requestDTO.getMessages().get(i);

            //构建用户请求
            ChatCompletionMessage questionMessage = new ChatCompletionMessage();
            questionMessage.setRole(ChatMessageRoleEnum.USER.getCode());
            questionMessage.setContent(chatMessageDTO.getText());
            historyMsgs.add(questionMessage);

            //2.预估耗费token数量是否超过限制
            if (kimiManager
                    .estimateTokenCountExceedLimit(requestDTO.getModel(), requestDTO.getRespMaxToken(), historyMsgs)) {
                this.wrapperErrorWriteSseEvent(out, KimiConst.CONTENT_LENGTH_TOO_LONG_ERROR_MSG);
                this.wrapperDoneWriteSseEvent(out);
                return;
            }

            //3.调用kimi接口得到响应，将响应添加到历史消息列表中
            ChatCompletionMessage answerMessage = null;
            try {
                answerMessage = kimiManager
                        .chatCompletion(requestDTO.getModel(), requestDTO.getRespMaxToken(), historyMsgs);
            } catch (BusinessServiceException e) {
                LOGGER.error("流式对话发生BusinessServiceException!", e);
                this.wrapperErrorWriteSseEvent(out, e.getErrMsg());
                //发送"[DONE]"事件，即结束信号
                this.wrapperDoneWriteSseEvent(out);
                return;
            } catch (Exception e) {
                LOGGER.error("流式对话发生异常!", e);
                this.wrapperErrorWriteSseEvent(out, KimiConst.COMMON_ERROR_MSG);
                //发送"[DONE]"事件，即结束信号
                this.wrapperDoneWriteSseEvent(out);
                return;
            }
            historyMsgs.add(answerMessage);
        }

        //4.处理最后一条消息。
        ChatCompletionMessage questionMessage = new ChatCompletionMessage();
        questionMessage.setRole(ChatMessageRoleEnum.USER.getCode());
        questionMessage.setContent(requestDTO.getMessages().get(requestDTO.getMessages().size() - 1).getText());
        historyMsgs.add(questionMessage);

        //5.预估耗费token数量是否超过限制
        if (kimiManager
                .estimateTokenCountExceedLimit(requestDTO.getModel(), requestDTO.getRespMaxToken(), historyMsgs)) {
            this.wrapperErrorWriteSseEvent(out, KimiConst.CONTENT_LENGTH_TOO_LONG_ERROR_MSG);
            this.wrapperDoneWriteSseEvent(out);
            return;
        }

        //6.最后的响应以流式返回
        this.invokeChatCompletionStream(requestDTO.getUserId(), out, requestDTO.getModel(),
                requestDTO.getRespMaxToken(), historyMsgs, null);

    }

    /**
     * 调用chat接口(流式)
     *
     * @param userId
     * @param out
     * @param historyMsgs
     * @param fileId
     */
    private void invokeChatCompletionStream(Long userId, OutputStream out, String model, Integer respMaxToken,
            List<ChatCompletionMessage> historyMsgs, String fileId) {
        try (InputStream inputStream = kimiManager.chatCompletionStream(userId, model, respMaxToken, historyMsgs);
                BufferedReader bufferedReader = new BufferedReader(
                        new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            while (true) {
                String line = bufferedReader.readLine();
                if (line == null) {
                    break;
                }

                // 忽略空白行
                if (StrUtil.isBlank(line)) {
                    continue;
                }

                // 处理JSON错误
                if (JSONUtil.isTypeJSON(line)) {
                    JSONObject errorObject = JSONUtil.parseObj(line).getJSONObject("error");
                    if (errorObject != null) {
                        String errorMessage = errorObject.getStr("message");
                        LOGGER.error("调用kimi流式对话服务发生异常，errorMessage: {}", errorMessage);

                        this.wrapperErrorWriteSseEvent(out, KimiConst.COMMON_ERROR_MSG);
                        break;
                    }
                }

                // 处理非JSON内容和"[DONE]"标记
                line = StrUtil.replace(line, "data: ", StrUtil.EMPTY);
                if (StrUtil.equals("[DONE]", line)) {
                    LOGGER.info("kimi回答完毕！[DONE]");
                    break;
                }

                // 处理有效的JSON回复
                if (JSONUtil.isTypeJSON(line) && this.parseAndWriteAnswer(out, line)) {
                    continue;
                }

                LOGGER.error("kimi回答内容非json结构!", line);
                this.wrapperErrorWriteSseEvent(out, KimiConst.COMMON_ERROR_MSG);
                break;
            }
        } catch (BusinessServiceException e) {
            LOGGER.error("流式对话发生BusinessServiceException!", e);
            this.wrapperErrorWriteSseEvent(out, e.getErrMsg());
        } catch (IOException e) {
            LOGGER.error("读取或写入流时发生异常", e);
            this.wrapperErrorWriteSseEvent(out, KimiConst.COMMON_ERROR_MSG);
        } catch (Exception e) {
            LOGGER.error("流式对话发生异常!", e);
            this.wrapperErrorWriteSseEvent(out, KimiConst.COMMON_ERROR_MSG);
        } finally {
            // 在finally块中统一发送"[DONE]"事件，确保总是发送结束信号
            this.wrapperDoneWriteSseEvent(out);

            // 删除文件（如果文件ID存在）
            if (StringUtils.isNotBlank(fileId)) {
                kimiManager.fileDelete(fileId);
            }
        }
    }

    public ResultModel<AiFileContentAndTitleRespDTO> extractFileContentAndTitle(MultipartFile multipartFile,
            String presupposeText, Long userId, String model, Integer respMaxToken) {
        //1.构建用户文本消息
        List<ChatCompletionMessage> questionMsgs = new ArrayList<>();
        ChatCompletionMessage textMessage = new ChatCompletionMessage();
        textMessage.setRole(ChatMessageRoleEnum.USER.getCode());
        textMessage.setContent(
                StringUtils.isNotBlank(presupposeText) ? presupposeText : EXTRACT_FILE_TITLE_PRESUPPOSE_TEXT);
        questionMsgs.add(textMessage);

        //2.文件上传、获取文件内容、构建用户文件消息
        String fileId = kimiManager.fileUploadFile(multipartFile);
        String fileContent = kimiManager.fileContent(fileId);
        ChatCompletionMessage fileMessage = new ChatCompletionMessage();
        fileMessage.setRole(ChatMessageRoleEnum.USER.getCode());
        fileMessage.setContent(FILE_CONTENT_PREFIX + fileContent);
        questionMsgs.add(fileMessage);

        try {
            //3.预估耗费token数量是否超过限制
            if (kimiManager.estimateTokenCountExceedLimit(model, respMaxToken, questionMsgs)) {
                throw BusinessServiceException.getInstance(AiChatErrorCodeEnum.CONTENT_LENGTH_TOO_LONG.getCode(),
                        AiChatErrorCodeEnum.CONTENT_LENGTH_TOO_LONG.getMessage());
            }

            //4.调用chat接口
            ChatCompletionMessage answerMsg = kimiManager.chatCompletion(model, respMaxToken, questionMsgs);

            AiFileContentAndTitleRespDTO aiSingleChatResponseDTO = new AiFileContentAndTitleRespDTO();
            aiSingleChatResponseDTO.setTitle(answerMsg.getContent());
            aiSingleChatResponseDTO.setContent(fileContent);

            return ResultModel.success(aiSingleChatResponseDTO);
        } finally {
            //5.删除文件
            if (StringUtils.isNotBlank(fileId)) {
                kimiManager.fileDelete(fileId);
            }
        }
    }

    private boolean parseAndWriteAnswer(OutputStream out, String line) {
        JSONArray choicesArray = JSONUtil.parseObj(line).getJSONArray("choices");
        if (choicesArray != null && choicesArray.size() > 0) {
            JSONObject firstChoice = choicesArray.getJSONObject(0);
            JSONObject delta = firstChoice.getJSONObject("delta");
            if (delta != null) {
                String content = delta.getStr("content");
                if (content != null) {
                    //LOGGER.info("kimi回答内容 rowData: {}", content);
                    this.wrapperContentWriteSseEvent(out, content);
                    return true;
                }
            }
            return true;
        }

        return false;
    }

    /**
     * 包装内容并写入SSE事件
     *
     * @param out
     * @param content
     */
    private void wrapperContentWriteSseEvent(OutputStream out, String content) {
        JSONObject object = new JSONObject();
        object.put("event", "cmpl");
        object.put("text", content);
        writeSseEvent(out, object.toString());
    }

    /**
     * 包装错误并写入SSE事件
     *
     * @param out
     * @param errMsg
     */
    private void wrapperErrorWriteSseEvent(OutputStream out, String errMsg) {
        LOGGER.warn("写入错误事件,errMsg:{}", errMsg);
        JSONObject object = new JSONObject();
        object.put("event", "error");
        object.put("text", errMsg);
        writeSseEvent(out, object.toString());
    }

    /**
     * 包装错误并写入SSE事件
     *
     * @param out
     */
    private void wrapperDoneWriteSseEvent(OutputStream out) {
        JSONObject object = new JSONObject();
        object.put("event", "all_done");
        writeSseEvent(out, object.toString());
    }

    /**
     * 写入SSE事件
     * <p>
     * 内容结构体：
     * {
     * "text": "",
     * "event": ""
     * }
     * <p>
     * event值：
     * cmpl -表示ai回答   text为回答的内容
     * error -表示错误   text为错误信息
     * all_done - 表示全部ai回答完成  无text
     *
     * @param out
     * @param data
     */
    private void writeSseEvent(OutputStream out, String data) {
        try {
            out.write("data: ".getBytes(StandardCharsets.UTF_8));
            out.write(data.getBytes(StandardCharsets.UTF_8));
            out.write("\n\n".getBytes(StandardCharsets.UTF_8));
            out.flush();
        } catch (IOException e) {
            LOGGER.error("写入sse事件流，发生异常！，data:{},,,e:", data, e);
        }
    }

}
