package com.dl.aiservice.share.digitalman;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-14 10:21
 */
@Data
public class DigitalManBaseInfoDTO implements Serializable {
    private static final long serialVersionUID = -1721019484823493158L;

    @ApiModelProperty(value = "模特ID")
    private Long digitalManId;

    @ApiModelProperty(value = "数字人形象代码")
    private String dgAvatar;

    @ApiModelProperty(value = "数字人名称")
    private String dgName;

    @ApiModelProperty(value = "仿真人声音代码")
    private String dgPer;

    @ApiModelProperty(value = "仿真人声音内部唯一代码")
    private Long dgPerBizId;

    @ApiModelProperty(value = "仿真人有效期开始")
    private Date validityStartDt;

    @ApiModelProperty(value = "仿真人有效期结束")
    private Date validityEndDt;

    @ApiModelProperty(value = "性别：1 男; 2 女")
    private Integer gender;

    @ApiModelProperty(value = "渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云")
    private Integer channel;

    @ApiModelProperty(value = "数字人头像地址url")
    private String headImg;

    @ApiModelProperty(value = "数智⼈类型：1 2d真⼈;2 3d真⼈")
    private Integer vmType;

    @ApiModelProperty(value = "是否支持语速调节，0 否；1 是")
    private Integer isEnableSpeed;

    @ApiModelProperty(value = "默认语速 1.0；范围 0.5 ~ 1.5")
    private Float defaultSpeed;

}
