package com.dl.aiservice.biz.service.digital.dto.req.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AliyunTTSEditingRequestDTO {
    /**
     * 声音类型
     */
    @JsonProperty("voice")
    private String voice ;
    /**
     * 人声克隆的 VoiceId
     */
    @JsonProperty("customizedVoice")
    private String customizedVoice;
    /**
     * 输出文件格式，支持：PCM/WAV/MP3
     */
    @JsonProperty("format")
    private String format;

    /**
     * 音量，取值0~100，默认值50
     */
    @JsonProperty("volume")
    private String volume;

    /**
     * 语速，取值范围：-500～500，默认值：0
     */
    @JsonProperty("speech_rate")
    private String speechRate;

    /**
     * 语调，取值范围：-500～500，默认值：0
     */
    @JsonProperty("pitchRate")
    private String pitch_rate;

}