package com.dl.aiservice.biz.client.ivh;

import com.dl.aiservice.biz.client.ivh.intercepter.IvhDigitalInterceptor;
import com.dl.aiservice.biz.client.ivh.req.IvhBaseRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetServiceAssetRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetSmallSampleAnchorRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetTimbreRequest;
import com.dl.aiservice.biz.client.ivh.resp.IvhBaseResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhGetSmallSampleAnchorResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhServiceAssetResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhTimbreResponse;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

@BaseRequest(baseURL = "https://gw.tvs.qq.com/v2/ivh/crmserver/customerassetservice", interceptor =
        IvhDigitalInterceptor.class)
public interface IvhDigitalSmallSampleClient {

    /**
     * 通过分⻚参数查询⼩样本形象列表
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/describesmallsampleimage")
    IvhBaseResponse<IvhGetSmallSampleAnchorResponse> listAnchors(
            @JSONBody IvhBaseRequest<IvhGetSmallSampleAnchorRequest> request);

    /**
     * 根据形象的VirtualmanKey查询⽀持的⾳⾊
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/getimagetimbre")
    IvhBaseResponse<IvhTimbreResponse> getTimbre(@JSONBody IvhBaseRequest<IvhGetTimbreRequest> request);

    /**
     * 查询客户服务资产信息
     *
     * @param request
     * @return
     */
    @Post(url = "/getserviceasset")
    IvhBaseResponse<IvhServiceAssetResponse> getServiceAsset(@JSONBody IvhBaseRequest<IvhGetServiceAssetRequest> request);
}
