package com.dl.aiservice.share.digitalasset;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 数字资产-仿真人信息表
 *
 * @TableName da_virtual_man
 */
@Data
public class DaVirtualManDTO implements Serializable {

    private static final long serialVersionUID = -4542681998006271374L;

    private Long id;

    @ApiModelProperty(value = "模特ID")
    private Long bizId;

    @NotBlank(message = "数字人形象代码必填")
    @ApiModelProperty(value = "数字人形象代码")
    private String vmCode;

    @ApiModelProperty(value = "数字人名称")
    private String vmName;

    /**
     * 仿真人自有声音代码
     */
    @ApiModelProperty(value = "仿真人声音代码")
    private String vmVoiceKey;

    @ApiModelProperty(value = "关联合成音/克隆音的声音代码")
    private Long voiceBizId;

    @ApiModelProperty(value = "仿真人有效期开始")
    private Date effectDt;

    @ApiModelProperty(value = "仿真人有效期结束")
    private Date expiryDt;

    @NotNull(message = "数字人性别必填")
    @ApiModelProperty(value = "性别：1 男; 2 女")
    private Integer gender;

    @NotNull(message = "数字人渠道必填")
    @ApiModelProperty(value = "渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云")
    private Integer channel;

    private String tenantCode;

    @ApiModelProperty(value = "数字人头像地址url")
    private String headImg;

    @ApiModelProperty(value = "数智⼈类型：1 2d真⼈;2 3d真⼈")
    private Integer vmType;

    @ApiModelProperty(value = "是否启用 0：否，1：是")
    private Integer isEnabled;

    @ApiModelProperty(value = "是否启用语速调整 0：否，1：是")
    private Integer enableSpeed;

    @ApiModelProperty("数字人默认语速值")
    private Float defaultSpeed;

    private String authTenantCode;

    private String sceneNameList;
}