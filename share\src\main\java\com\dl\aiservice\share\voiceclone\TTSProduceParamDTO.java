package com.dl.aiservice.share.voiceclone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class TTSProduceParamDTO implements Serializable {

    private static final long serialVersionUID = -6442816271939461816L;

    @ApiModelProperty(value = "发言人训练成功的音色名", required = true)
    @NotBlank(message = "voiceName必填")
    private String voiceName;

    /**
     * 不再使用，腾讯云数字人的声音使用virtualManKey
     */
    @Deprecated
    private String timbreKey;

    @ApiModelProperty(value = "需要合成的文本串待合成的文本，需要为UTF-8编码，再使用url_safe base64编码", required = true)
    @NotBlank(message = "text必填")
    private String text;

    @ApiModelProperty(value = "上层业务作品唯一标识")
    private Long worksBizId;

    @ApiModelProperty(value = "快视频合成任务内部唯一标识")
    private Long videoTaskJobId;

    /**
     * 数字的读法
     * 可选以下值，默认 ordinal_first；
     * ordinal_first # 优先数值读法；
     * digits_first # 优先数字串读法；
     * all_ordinal # 完全数值发音；
     * all_digits # 完全数字串读；
     * military # 军事读法。
     */
    @ApiModelProperty(value = "数字的读法,默认 ordinal_first")
    private String numberRead;

    /**
     * 音频格式 默认为 audio/L16;rate=44100；
     * 可设置为 audio/L16;rate=8000、audio/L16;rate=16000、audio/L16;rate=24000、audio/L16;rate=44100。
     * 当 audio-encode 设置为 audio/mp3 时，该参数须设置为 audio/L16;rate=44100
     */
    @ApiModelProperty(value = "音频格式 默认为 audio/L16;rate=44100")
    private String audioFormat;

    /**
     * 音频编码
     * 可选audio/mp3 audio/wav audio/opus
     * 其中：
     * audio/raw 表示音频为 wav 编码；
     * audio/opus 为不带容器的 Opus 格 式；每一帧头为 2 字节大端格式的整 数，表示帧数据长度。
     */
    @ApiModelProperty(value = "音频编码")
    private String audioEncode;

    /**
     * 返回数据
     * 格式 可选 raw、json、stream。
     * 选 raw 或 stream 时返回音频的二进制 流；
     * 选 json 时返回音频的 URL；其中 raw 和 json 支持时间戳，stream 不支持时间戳。
     */
    @ApiModelProperty(value = "返回数据格式,可选 raw、json、stream")
    private String outputFormat;

    /**
     * 音调 默认值：normal；
     * 可选： lower/low/normal/high/higher
     */
    @ApiModelProperty(value = "音调:默认值：normal")
    private String pitch;

    /**
     * 语速
     */
    @ApiModelProperty(value = "语速 0.5~1.5")
    private String speed;

    @ApiModelProperty(value = "音量;取值范围 0.0-1.0，1.0 表示最大音量")
    private String volume;

    /**
     * 音质 训练的音色默认为 high
     */
    @ApiModelProperty(value = "音质训练的音色默认为:high")
    private String quality;

    /**
     * 情感
     */
    @ApiModelProperty(value = "neutral(中性)、sad(悲伤)、happy(高兴)、angry(生气)、fear(恐惧)、news(新闻)、story(故事)、radio(广播)、poetry(诗歌)、call(客服)")
    private String emotionCategory;

    /**
     * 控制合成音频情感程度，取值范围为[50,200],默认为100；只有EmotionCategory不为空时生效
     */
    @ApiModelProperty(value = "控制合成音频情感程度，取值范围为[50,200],默认为100；只有EmotionCategory不为空时生效")
    private Long emotionIntensity;

    @ApiModelProperty(value = "是否需要字幕，0-否，1-是")
    private Integer needSubtitle;

    @ApiModelProperty(value = "单行字幕最大字数 取值范围:【6-40】，为null则不开启该功能。当needSubtitle为1时有效。")
    private Integer maxLength;

    @ApiModelProperty(value = "是否需要字幕中关键词高亮，0-否，1-是。当needSubtitle为1时有效。")
    private Integer subtitleKeyWordsHighlight;

    @ApiModelProperty(value = "是否使用自定义地址存储音频 0-否 1-是")
    private Integer customStoreUrl;

    @ApiModelProperty(value = "声音类型， 1 克隆音；2 合成音。非必填，为了D端新增智能语音合成tts时，" + "同一厂商不同声音类型请求外部接口入参不同而加的字段。")
    private Integer voiceType;
    
}
