package com.dl.aiservice.web.controller.subtitle;

import com.dl.aiservice.biz.manager.subtitle.SubtitleManager;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleAndReviseRequestDTO;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleDTO;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleRequestDTO;
import com.dl.aiservice.share.subtitle.dto.ReviseAsrRequestDTO;
import com.dl.aiservice.share.subtitle.dto.RevisedAsrResponseDTO;
import com.dl.framework.common.model.ResultModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * @describe: SubtitleController
 * @author: zhousx
 * @date: 2023/3/24 16:16
 */
@Slf4j
@RestController
@RequestMapping("/subtitle")
public class SubtitleController {

    @Resource
    private SubtitleManager subtitleManager;

    /**
     * 字幕校验
     *
     * @param requestDTO
     * @return
     */
    @PostMapping("/asrrevise")
    public ResultModel<RevisedAsrResponseDTO> asrRevise(@RequestBody @Validated ReviseAsrRequestDTO requestDTO) {
        return ResultModel.success(subtitleManager.asrRevise(requestDTO));
    }

    /**
     * 音频识别——音频链接
     *
     * @param requestDTO
     * @return
     */
    @PostMapping("/asr")
    public ResultModel<List<AsrSubtitleDTO>> asr(@RequestBody AsrSubtitleRequestDTO requestDTO) {
        Assert.isTrue(StringUtils.isNotBlank(requestDTO.getAudioUrl()), "音频url不能为空");
        return ResultModel.success(subtitleManager.asr(requestDTO));
    }

    /**
     * 音频识别——音频文件
     *
     * @param file
     * @param sentenceMaxLength 每句最多展示字数 非必填
     * @return
     */
    @PostMapping("/asrfile")
    public ResultModel<List<AsrSubtitleDTO>> asrFile(MultipartFile file,
            @RequestParam(required = false) Integer sentenceMaxLength, @RequestParam(required = true) Integer channel) {
        Assert.notNull(file, "音频文件不能为空");
        return ResultModel.success(subtitleManager.asrFile(file, sentenceMaxLength, channel));
    }

    /**
     * 音频识别音频地址并字幕校验
     *
     * @param requestDTO
     * @return
     */
    @PostMapping("/asrandrevise")
    public ResultModel<RevisedAsrResponseDTO> asrAndRevise(
            @RequestBody @Validated AsrSubtitleAndReviseRequestDTO requestDTO) {
        return ResultModel.success(subtitleManager.asrAndRevise(requestDTO));
    }

    /**
     * 音频识别音频文件并字幕校验
     *
     * @param file
     * @param sentenceMaxLength 每句最多展示字数 非必填
     * @return
     */
    @PostMapping("/asrfileandrevise")
    public ResultModel<RevisedAsrResponseDTO> asrFileAndRevise(MultipartFile file,
            @RequestParam(required = false) Integer sentenceMaxLength, @RequestParam(required = true) Integer channel,
            @RequestParam String originalScript) {
        Assert.notNull(file, "音频文件不能为空");
        return ResultModel.success(subtitleManager.asrFileAndRevise(file, sentenceMaxLength, channel, originalScript));
    }

    /**
     * 获取分词后 词组集合
     *
     * @param sequence
     * @return
     */
    //@NotAuth
    @PostMapping("/segmentor")
    public ResultModel<List<String>> segmentor(@RequestParam String sequence) {
        return ResultModel.success(subtitleManager.segmentor(sequence));
    }
}
