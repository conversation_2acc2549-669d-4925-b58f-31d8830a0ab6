package com.dl.aiservice.biz.client.Ifly.enums;

import java.util.Objects;

public enum IflyErrCodeEnum {

    ERROR_CODE_0("000000", "正常"),

    ERROR_CODE_000001("000001", "HTTP请求读取错误"),
    ERROR_CODE_000002("000002", "JSON格式不合法"),
    ERROR_CODE_000003("000003", "请求参数错误(缺少必要字段、数据大小超过限制等)"),
    ERROR_CODE_000004("000004", "请求没有授权"),
    ERROR_CODE_000005("000005", "合成权限异常"),
    ERROR_CODE_000006("000006", "动画主播权限异常"),
    ERROR_CODE_000007("000007", "真人主播权限异常"),
    ERROR_CODE_000008("000008", "vcn权限异常"),
    ERROR_CODE_000009("000009", "Ip 不在白名单内"),
    ERROR_CODE_000010("000010", "请求超过配置路数"),
    ERROR_CODE_000011("000011", "sign校验失败"),
    ERROR_CODE_000012("000012", "anchorId权限异常"),
    ERROR_CODE_000013("000013", "背景图片下载失败"),
    ERROR_CODE_000020("000020", "多语种缺少语种权限"),
    ERROR_CODE_000021("000021", "多语种文本缺少语种标签"),
    ERROR_CODE_000022("000022", "应用路数为0"),
    ERROR_CODE_000023("000023", "任务不存在"),
    UNKNOWN("9999999", "未知异常");

    private final String errorCode;
    private final String errorDesc;

    IflyErrCodeEnum(String errorCode, String errorDesc) {
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }

    public static String getErrorDesc(String errorCode) {
        for (IflyErrCodeEnum wxErrorCodeEnum : values()) {
            if (Objects.equals(wxErrorCodeEnum.errorCode, errorCode)) {
                return wxErrorCodeEnum.errorDesc;
            }
        }
        return null;
    }

    public static IflyErrCodeEnum errorCode(String code) {
        for (IflyErrCodeEnum value : values()) {
            if (Objects.nonNull(code)) {
                if (value.getErrorCode().equals(code)) {
                    return value;
                }
            }
        }
        return UNKNOWN;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorDesc() {
        return errorDesc;
    }
}
