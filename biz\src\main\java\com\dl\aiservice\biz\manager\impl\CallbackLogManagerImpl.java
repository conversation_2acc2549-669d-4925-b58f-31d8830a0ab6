package com.dl.aiservice.biz.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.aiservice.biz.dal.mapper.CallbackLogMapper;
import com.dl.aiservice.biz.dal.po.CallbackLogPO;
import com.dl.aiservice.biz.manager.CallbackLogManager;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【callback_log(回调记录表)】的数据库操作Service实现
 * @createDate 2023-03-10 17:01:41
 */
@Service
public class CallbackLogManagerImpl extends ServiceImpl<CallbackLogMapper, CallbackLogPO>
        implements CallbackLogManager {

}




