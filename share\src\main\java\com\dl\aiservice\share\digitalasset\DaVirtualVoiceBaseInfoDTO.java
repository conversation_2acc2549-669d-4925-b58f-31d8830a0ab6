package com.dl.aiservice.share.digitalasset;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-21 09:33
 */
@Data
public class DaVirtualVoiceBaseInfoDTO implements Serializable {
    private static final long serialVersionUID = 3435015445940055700L;

    @ApiModelProperty(value = "数字声音唯一标识")
    private Long bizId;

    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    private Integer channel;

    /**
     * 外部厂商声音唯一标识
     */
    private String voiceKey;

    /**
     * 声音名称
     */
    private String voiceName;

    /**
     * 性别：1 男 ；2 女
     */
    private Integer gender;

    /**
     * 描述
     */
    private String voiceDesc;

    /**
     * 1 克隆音；2 合成音
     */
    private Integer voiceType;

    /**
     * 默认：通用
     */
    private String voiceCategory;

    /**
     * 默认的试听链接
     */
    private String sampleLink;

    /**
     * 生效日期
     */
    private Date effectDt;

    /**
     * 失效日期
     */
    private Date expiryDt;

    /**
     * 最大声音试听链接
     */
    private String maxVoiceLink;

    /**
     * 建议音量
     */
    private String volume;

    /**
     * 建议语速
     */
    private String speed;

    /**
     * 语音头像
     */
    private String headImg;

    /**
     * 是否启用 0：否，1：是
     */
    private Integer isEnabled;

    /**
     * 音频时长(单位：毫秒)
     */
    private Long duration;

    /**
     * 多试听链接
     */
    private List<DaVirtualVoiceLinkDTO> voiceLinks;
}
