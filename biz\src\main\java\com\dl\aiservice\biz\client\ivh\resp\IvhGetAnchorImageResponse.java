package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class IvhGetAnchorImageResponse implements Serializable {

    private static final long serialVersionUID = 1961425384297568194L;

    @JsonProperty(value = "VirtualmanResources")
    private List<IvhVirtualmanImageResources> virtualmanResources;


}
