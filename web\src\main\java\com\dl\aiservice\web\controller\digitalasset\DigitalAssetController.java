package com.dl.aiservice.web.controller.digitalasset;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.transaction.TransactionProxyManager;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.DaTenantAuthPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManSceneQueryPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManScenesPO;
import com.dl.aiservice.biz.dal.po.DaVirtualVoicePO;
import com.dl.aiservice.biz.manager.digitalasset.DaTenantAuthManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualManManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualManScenesManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualVoiceManager;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManScenesBO;
import com.dl.aiservice.biz.manager.digitalasset.enums.DaTenantAuthBizTypeEnum;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManAuthDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManPageRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManSceneVoiceRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesQueryDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManVoiceRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceRequestDTO;
import com.dl.aiservice.share.digitalman.DigitalManAggregationInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManAggregationRequestDTO;
import com.dl.aiservice.share.digitalman.DigitalManBaseInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManInfoDTO;
import com.dl.aiservice.web.controller.digitalasset.convert.DigitalAssetConvert;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.common.utils.JsonUtils;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/digital/asset")
public class DigitalAssetController {

    @Resource
    private DaVirtualManScenesManager daVirtualManScenesManager;
    @Resource
    private DaVirtualManManager daVirtualManManager;
    @Resource
    private DaVirtualVoiceManager daVirtualVoiceManager;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private DaTenantAuthManager daTenantAuthManager;
    @Resource
    private TransactionProxyManager transactionProxyManager;

    @PostMapping("/vm/one")
    @ApiOperation("查询单个数字人信息（带场景）")
    public ResultModel<DigitalManInfoDTO> one(Long bizId) {
        Assert.notNull(bizId, "入参不能为空");
        List<DaVirtualManScenesBO> digitalManList = daVirtualManScenesManager
                .listVirtualManScene(channelUtil.getTenantCode(), bizId, null);
        return ResultModel.success(DigitalAssetConvert.cnvDaVirtualManScenesBO2DTO(digitalManList));
    }

    @PostMapping("/vm/baseinfo")
    @ApiOperation("查询单个数字人基本信息")
    public ResultModel<DigitalManBaseInfoDTO> baseInfo(@RequestParam Long bizId) {
        Assert.notNull(bizId, "入参不能为空");
        DaVirtualManPO daVirtualManPO = daVirtualManManager.info(bizId);
        return ResultModel.success(DigitalAssetConvert.cnvDaVirtualManPO2BaseInfoDTO(daVirtualManPO));
    }

    @PostMapping("/vm/aggregationinfo")
    @ApiOperation("查询单个数字人组合信息（数字人信息、数字人场景、数字人声音信息）")
    public ResultModel<DigitalManAggregationInfoDTO> aggregationinfo(
            @RequestBody DigitalManAggregationRequestDTO requestDTO) {
        Assert.notNull(requestDTO.getVmBizId(), "数字人id不能为空");
        DaVirtualManPO daVirtualManPO = daVirtualManManager.info(requestDTO.getVmBizId());
        if (Objects.isNull(daVirtualManPO)) {
            return ResultModel.success(null);
        }

        List<DaVirtualManScenesBO> sceneList = daVirtualManScenesManager
                .listVirtualManSceneWithoutAuthCheck(daVirtualManPO.getBizId(), null);

        DaVirtualVoicePO voicePO = daVirtualVoiceManager.info(daVirtualManPO.getVoiceBizId());

        return ResultModel
                .success(DigitalAssetConvert.buildDigitalManAggregationInfoDTO(daVirtualManPO, sceneList, voicePO));
    }

    @ApiOperation("查询数字人列表")
    @PostMapping("/vm/list")
    public ResultModel<List<DaVirtualManDTO>> vmList(@RequestBody DaVirtualManRequestDTO param) {
        List<DaTenantAuthPO> authVmList = daTenantAuthManager.list(Wrappers.lambdaQuery(DaTenantAuthPO.class)
                .eq(DaTenantAuthPO::getTenantCode, channelUtil.getTenantCode())
                .eq(DaTenantAuthPO::getBizType, DaTenantAuthBizTypeEnum.DIGITAL_MAN.getType())
                .eq(Objects.nonNull(param.getBizId()), DaTenantAuthPO::getBizId, param.getBizId())
                .eq(DaTenantAuthPO::getIsDeleted, Const.ZERO));
        if (CollectionUtils.isEmpty(authVmList)) {
            return ResultModel.success(Collections.emptyList());
        }
        List<Long> vmBizIdList = authVmList.stream().map(DaTenantAuthPO::getBizId).collect(Collectors.toList());

        List<DaVirtualManPO> vmList = daVirtualManManager.lambdaQuery().in(DaVirtualManPO::getBizId, vmBizIdList)
                .in(CollectionUtils.isNotEmpty(param.getChannels()), DaVirtualManPO::getChannel, param.getChannels())
                .eq(DaVirtualManPO::getIsEnabled, Const.ONE).eq(DaVirtualManPO::getIsDeleted, Const.ZERO).list();
        if (CollectionUtils.isEmpty(vmList)) {
            return ResultModel.success(Lists.newArrayList());
        }
        return ResultModel.success(vmList.stream().map(x -> convert(x)).collect(Collectors.toList()));
    }

    @PostMapping("/vm/page")
    public ResultPageModel<DaVirtualManDTO> genericPage(@RequestBody DaVirtualManPageRequestDTO param) {
        IPage<DaVirtualManDTO> pageResult = daVirtualManManager.pageVm(param);
        ResultPageModel<DaVirtualManDTO> resultPageModel = new ResultPageModel<>();
        resultPageModel.setPageIndex(pageResult.getCurrent());
        resultPageModel.setPageSize(pageResult.getSize());
        resultPageModel.setTotal(pageResult.getTotal());
        resultPageModel.setTotalPage(pageResult.getPages());
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            resultPageModel.setDataResult(Lists.newArrayList());
            return resultPageModel;
        }
        resultPageModel.setDataResult(pageResult.getRecords());
        return resultPageModel;
    }

    private DaVirtualManDTO convert(DaVirtualManPO source) {
        DaVirtualManDTO daVirtualManDTO = new DaVirtualManDTO();
        daVirtualManDTO.setBizId(source.getBizId());
        daVirtualManDTO.setTenantCode(channelUtil.getTenantCode());
        daVirtualManDTO.setVmCode(source.getVmCode());
        daVirtualManDTO.setVmName(source.getVmName());
        daVirtualManDTO.setVmVoiceKey(source.getVmVoiceKey());
        daVirtualManDTO.setVoiceBizId(source.getVoiceBizId());
        daVirtualManDTO.setGender(source.getGender());
        daVirtualManDTO.setHeadImg(source.getHeadImg());
        daVirtualManDTO.setVmType(source.getVmType());
        daVirtualManDTO.setEffectDt(source.getEffectDt());
        daVirtualManDTO.setExpiryDt(source.getExpiryDt());
        daVirtualManDTO.setChannel(source.getChannel());
        return daVirtualManDTO;
    }

    @PostMapping("/vm/scene/page")
    public ResultPageModel<DaVirtualManScenesDTO> genericPage(@RequestBody PageRequestDTO pageRequestDTO) {
        DaVirtualManSceneQueryPO query = new DaVirtualManSceneQueryPO();
        query.setPageIndex(pageRequestDTO.getPageIndex());
        query.setPageSize(pageRequestDTO.getPageSize());
        query.setTenantCode(channelUtil.getTenantCode());
        IPage<DaVirtualManScenesBO> pageResult = daVirtualManScenesManager.pageVirtualMan(query);
        ResultPageModel<DaVirtualManScenesDTO> resultPageModel = new ResultPageModel<>();
        resultPageModel.setPageIndex(pageResult.getCurrent());
        resultPageModel.setPageSize(pageResult.getSize());
        resultPageModel.setTotal(pageResult.getTotal());
        resultPageModel.setTotalPage(pageResult.getPages());
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            resultPageModel.setDataResult(Lists.newArrayList());
            return resultPageModel;
        }
        resultPageModel.setDataResult(pageResult.getRecords().stream()
                .map(x -> DigitalAssetConvert.cnvDaVirtualManScenesBO2DTO(x, channelUtil.getTenantCode()))
                .collect(Collectors.toList()));
        return resultPageModel;
    }

    @PostMapping("/vm/scene/list")
    public ResultModel<List<DaVirtualManScenesDTO>> vmSceneList(@RequestBody DaVirtualManRequestDTO param) {
        List<DaVirtualManScenesBO> sceneList = daVirtualManScenesManager
                .listVirtualManScene(channelUtil.getTenantCode(), param.getBizId(), param.getEnableFilter());
        if (CollectionUtils.isEmpty(sceneList)) {
            return ResultModel.success(Lists.newArrayList());
        }
        return ResultModel.success(sceneList.stream()
                .map(x -> DigitalAssetConvert.cnvDaVirtualManScenesBO2DTO(x, channelUtil.getTenantCode()))
                .collect(Collectors.toList()));
    }

    @PostMapping("/vm/scene/listbybizids")
    public ResultModel<List<DaVirtualManScenesDTO>> vmSceneListByBizIds(@RequestBody DaVirtualManRequestDTO param) {
        //bizId不为空，只查询bizId对应的数据
        Assert.isTrue(Objects.nonNull(param.getBizId()) || CollectionUtils.isNotEmpty(param.getBizIds()), "bizId和bizIds不能同时为空");
        List<Long> bizIdList = param.getBizIds();
        if (Objects.nonNull(param.getBizId())) {
            bizIdList = Collections.singletonList(param.getBizId());
        }
        List<DaVirtualManScenesBO> sceneList = daVirtualManScenesManager
                .listVirtualManSceneByBizIds(channelUtil.getTenantCode(), bizIdList, param.getEnableFilter());
        if (CollectionUtils.isEmpty(sceneList)) {
            return ResultModel.success(Lists.newArrayList());
        }
        return ResultModel.success(sceneList.stream()
                .map(x -> DigitalAssetConvert.cnvDaVirtualManScenesBO2DTO(x, channelUtil.getTenantCode()))
                .collect(Collectors.toList()));
    }

    @PostMapping("/vm/scene/listbysceneids")
    public ResultModel<List<DaVirtualManScenesDTO>> vmSceneListBySceneIds(
            @RequestBody @Validated DaVirtualManScenesQueryDTO param) {
        List<DaVirtualManScenesBO> sceneList = daVirtualManScenesManager
                .listVirtualManSceneBySceneIdsWithoutAuthCheck(param.getSceneIds(), param.getChannels(),
                        param.getEnableFilter());
        if (CollectionUtils.isEmpty(sceneList)) {
            return ResultModel.success(Lists.newArrayList());
        }
        return ResultModel.success(sceneList.stream()
                .map(x -> DigitalAssetConvert.cnvDaVirtualManScenesBO2DTO(x, channelUtil.getTenantCode()))
                .collect(Collectors.toList()));
    }

    @PostMapping("/voice/list")
    public ResultModel<List<DaVirtualVoiceDTO>> voiceList(@RequestBody DaVirtualVoiceRequestDTO param) {
        List<DaVirtualVoicePO> voiceList = daVirtualVoiceManager
                .listVoice(channelUtil.getTenantCode(), param.getVoiceType(), param.getChannels());
        if (CollectionUtils.isEmpty(voiceList)) {
            return ResultModel.success(Lists.newArrayList());
        }
        return ResultModel.success(
                voiceList.stream().map(x -> DigitalAssetConvert.cnvDaVirtualVoicePO2DTO(x, channelUtil.getTenantCode()))
                        .collect(Collectors.toList()));
    }

    @PostMapping("/voice/list/magic")
    public ResultModel<List<DaVirtualVoiceDTO>> voiceListMagic(@RequestBody DaVirtualVoiceRequestDTO param) {
        List<DaVirtualVoicePO> voiceList = daVirtualVoiceManager
                .listVoice(channelUtil.getTenantCode(), param.getVoiceType(), param.getChannels());

        return ResultModel.success(
                voiceList.stream().map(x -> DigitalAssetConvert.cnvDaVirtualVoicePO2DTO(x, channelUtil.getTenantCode()))
                        .collect(Collectors.toList()));
    }

    @PostMapping("/voice/one")
    public ResultModel<DaVirtualVoiceDTO> voiceOne(@RequestBody DaVirtualVoiceRequestDTO param) {
        DaVirtualVoicePO voicePO = daVirtualVoiceManager.lambdaQuery().eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO)
                .eq(DaVirtualVoicePO::getIsEnabled, Const.ONE)
                .in(CollectionUtils.isNotEmpty(param.getChannels()), DaVirtualVoicePO::getChannel, param.getChannels())
                .eq(Objects.nonNull(param.getVoiceType()), DaVirtualVoicePO::getVoiceType, param.getVoiceType())
                .eq(Objects.nonNull(param.getVoiceBizId()), DaVirtualVoicePO::getBizId, param.getVoiceBizId())
                .eq(StringUtils.isNotBlank(param.getVoiceKey()), DaVirtualVoicePO::getVoiceKey, param.getVoiceKey())
                .one();
        if (Objects.isNull(voicePO)) {
            return ResultModel.success(null);
        }
        return ResultModel.success(DigitalAssetConvert.cnvDaVirtualVoicePO2DTO(voicePO, channelUtil.getTenantCode()));
    }

    @PostMapping("/vm/saveOrUpdate")
    @Transactional(rollbackFor = Exception.class)
    public ResultModel<String> vmSaveOrUpdate(@RequestBody DaVirtualManDTO param) {
        DaVirtualManPO daVirtualMan = convert(param);
        //修改
        if (Objects.nonNull(param.getBizId())) {
            DaVirtualManPO vmExist = daVirtualManManager.lambdaQuery()
                    .eq(DaVirtualManPO::getBizId, param.getBizId())
                    .eq(DaVirtualManPO::getIsDeleted, Const.ZERO)
                    .one();
            Assert.notNull(vmExist, String.format("数字人[%s]不存在", param.getBizId()));
            daVirtualMan.setBizId(null);
            if (StringUtils.isNotBlank(param.getVmCode()) && !StringUtils.equals(param.getVmCode(),
                    vmExist.getVmCode())) {
                DaVirtualManPO vmExistCode = daVirtualManManager.lambdaQuery()
                        .eq(DaVirtualManPO::getVmCode, param.getVmCode())
                        .eq(DaVirtualManPO::getChannel, param.getChannel()).eq(DaVirtualManPO::getIsDeleted, Const.ZERO)
                        .one();
                if (Objects.nonNull(vmExistCode)) {
                    return ResultModel.error(Const.TIME_RULE_SPLITTER + Const.ONE, "该数字人已存在");
                }
            }
            daVirtualManManager.update(daVirtualMan, Wrappers.lambdaUpdate(DaVirtualManPO.class)
                    .eq(DaVirtualManPO::getVmCode, vmExist.getVmCode())
                    .eq(DaVirtualManPO::getChannel, vmExist.getChannel()));
            //若vmCode修改了，则scene中的vmCode也要修改
            if (StringUtils.isNotBlank(daVirtualMan.getVmCode()) && !StringUtils.equals(vmExist.getVmCode(),
                    daVirtualMan.getVmCode())) {
                daVirtualManScenesManager.lambdaUpdate().set(DaVirtualManScenesPO::getModifyDt, new Date())
                        .set(DaVirtualManScenesPO::getVmCode, daVirtualMan.getVmCode())
                        .eq(DaVirtualManScenesPO::getVmCode, vmExist.getVmCode())
                        .eq(DaVirtualManScenesPO::getChannel, vmExist.getChannel()).update();
            }
            return ResultModel.success(vmExist.getBizId().toString());
        }
        DaVirtualManPO vmExist = daVirtualManManager.lambdaQuery().eq(DaVirtualManPO::getVmCode, param.getVmCode())
                .eq(DaVirtualManPO::getChannel, param.getChannel()).eq(DaVirtualManPO::getIsDeleted, Const.ZERO).one();
        if (Objects.nonNull(vmExist)) {
            return ResultModel.error(Const.TIME_RULE_SPLITTER + Const.ONE, "该数字人已存在");
        }

        transactionProxyManager.process(() -> {
            // 新增操作
            daVirtualMan.setBizId(hostTimeIdg.generateId().longValue());
            daVirtualManManager.save(daVirtualMan);

            //新增的数字人默认授权给DL租户
            daVirtualManManager.vmAuth(param.getVmCode(), param.getChannel(), Lists.newArrayList("DL"));
        });

        return ResultModel.success(daVirtualMan.getBizId().toString());
    }

    @PostMapping("/vm/scene/saveOrUpdate")
    public ResultModel sceneSaveOrUpdate(@RequestBody DaVirtualManScenesDTO param) {
        Assert.notNull(param.getChannel(), "渠道信息必填");
        Assert.isTrue(StringUtils.isNotBlank(param.getVmCode()), "数字人code必填");
        Assert.isTrue(StringUtils.isNotBlank(param.getSceneId()), "数字人场景ID必填");
        Assert.notNull(param.getBizId(), "数字人ID必填");
        DaVirtualManScenesPO daVirtualManScenes = new DaVirtualManScenesPO();
        daVirtualManScenes.setSceneId(param.getSceneId());
        daVirtualManScenes.setChannel(param.getChannel());
        daVirtualManScenes.setSceneName(param.getSceneName());
        daVirtualManScenes.setVmCode(param.getVmCode());
        daVirtualManScenes.setCoverUrl(param.getCoverUrl());
        daVirtualManScenes.setExampleUrl(param.getExampleUrl());
        daVirtualManScenes.setPose(param.getPose());
        daVirtualManScenes.setCloth(param.getCloth());
        daVirtualManScenes.setResolution(param.getResolution());
        daVirtualManScenes.setExampleText(param.getExampleText());
        daVirtualManScenes.setExampleDuration(param.getExampleDuration());
        if (Objects.nonNull(param.getId())) {
            DaVirtualManScenesPO sceneExist = daVirtualManScenesManager.lambdaQuery()
                    .eq(DaVirtualManScenesPO::getId, param.getId())
                    .eq(DaVirtualManScenesPO::getIsDeleted, Const.ZERO)
                    .one();
            if (Objects.isNull(sceneExist)) {
                return ResultModel.error(Const.TIME_RULE_SPLITTER + Const.ONE, "场景数据不存在");
            }
            if (!StringUtils.equals(param.getSceneId(), sceneExist.getSceneId())) {
                sceneExist = daVirtualManScenesManager.lambdaQuery()
                        .eq(DaVirtualManScenesPO::getVmBizId, param.getBizId())
                        .eq(DaVirtualManScenesPO::getSceneId, param.getSceneId())
                        .eq(DaVirtualManScenesPO::getIsDeleted, Const.ZERO)
                        .one();
                if (Objects.nonNull(sceneExist)) {
                    return ResultModel.error(Const.TIME_RULE_SPLITTER + Const.ONE, "该场景已存在");
                }
            }
            daVirtualManScenesManager.update(daVirtualManScenes,
                    Wrappers.lambdaUpdate(DaVirtualManScenesPO.class).eq(DaVirtualManScenesPO::getId, param.getId()));
            return ResultModel.success(null);
        }
        DaVirtualManScenesPO sceneExist = daVirtualManScenesManager.lambdaQuery()
                .eq(DaVirtualManScenesPO::getVmBizId, param.getBizId())
                .eq(DaVirtualManScenesPO::getSceneId, param.getSceneId())
                .eq(DaVirtualManScenesPO::getIsDeleted, Const.ZERO)
                .one();
        if (Objects.nonNull(sceneExist)) {
            return ResultModel.error(Const.TIME_RULE_SPLITTER + Const.ONE, "该场景已存在");
        }
        // 新增操作
        List<DaVirtualManPO> vmExistList = daVirtualManManager.lambdaQuery()
                .eq(DaVirtualManPO::getVmCode, param.getVmCode())
                .eq(DaVirtualManPO::getIsDeleted, Const.ZERO)
                .eq(DaVirtualManPO::getChannel, param.getChannel())
                .list();
        if (CollectionUtils.isNotEmpty(vmExistList)) {
            List<DaVirtualManScenesPO> sceneList = Lists.newArrayList();
            String objJson = JsonUtils.toJSON(daVirtualManScenes);
            vmExistList.stream().forEach(x -> {
                DaVirtualManScenesPO newScene = JsonUtils.fromJSON(objJson, DaVirtualManScenesPO.class);
                newScene.setVmBizId(x.getBizId());
                sceneList.add(newScene);
            });
            daVirtualManScenesManager.saveBatch(sceneList);
        }
        return ResultModel.success(null);
    }

    private DaVirtualManPO convert(DaVirtualManDTO source) {
        DaVirtualManPO daVirtualManPO = new DaVirtualManPO();
        daVirtualManPO.setBizId(source.getBizId());
        daVirtualManPO.setVmCode(source.getVmCode());
        daVirtualManPO.setVmName(source.getVmName());
        daVirtualManPO.setVmVoiceKey(source.getVmVoiceKey());
        daVirtualManPO.setVoiceBizId(source.getVoiceBizId());
        daVirtualManPO.setEffectDt(source.getEffectDt());
        daVirtualManPO.setExpiryDt(source.getExpiryDt());
        daVirtualManPO.setGender(source.getGender());
        daVirtualManPO.setChannel(source.getChannel());
        daVirtualManPO.setHeadImg(source.getHeadImg());
        daVirtualManPO.setVmType(source.getVmType());
        daVirtualManPO.setIsEnableSpeed(source.getEnableSpeed());
        daVirtualManPO.setDefaultSpeed(source.getDefaultSpeed());
        daVirtualManPO.setIsEnabled(source.getIsEnabled());
        return daVirtualManPO;
    }

    /**
     * 数字人授权
     *
     * @param param
     * @return
     */
    @PostMapping("/vm/auth")
    public ResultModel<Void> vmAuth(@RequestBody @Validated DaVirtualManAuthDTO param) {
        daVirtualManManager.vmAuth(param.getVmCode(), param.getChannel(), param.getAuthTenantCodeList());
        return ResultModel.success(null);
    }

    @PostMapping("/vm/scene/specificvoice")
    @ApiOperation("查询单个数字人场景的指定声音信息")
    public ResultModel<DaVirtualVoiceDTO> vmSceneSpecificVoice(
            @RequestBody DaVirtualManSceneVoiceRequestDTO requestDTO) {
        Assert.isTrue(StringUtils.isNotBlank(requestDTO.getSceneId()), "数字人场景id不能为空");
        Assert.isTrue(StringUtils.isNotBlank(requestDTO.getVoiceCode()), "数字人声音编码不能为空");

        DaVirtualManScenesPO daVirtualManScenesPO = daVirtualManScenesManager
                .getOne(Wrappers.lambdaQuery(DaVirtualManScenesPO.class)
                        .eq(DaVirtualManScenesPO::getSceneId, requestDTO.getSceneId())
                        .eq(DaVirtualManScenesPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(daVirtualManScenesPO)) {
            return ResultModel.success(null);
        }
        DaVirtualManPO daVirtualManPO = daVirtualManManager.getOne(Wrappers.lambdaQuery(DaVirtualManPO.class)
                .eq(DaVirtualManPO::getBizId, daVirtualManScenesPO.getVmBizId()));
        if (Objects.isNull(daVirtualManPO)) {
            return ResultModel.success(null);
        }
        if (!Objects.equals(requestDTO.getVoiceCode(), daVirtualManPO.getVmVoiceKey())) {
            return ResultModel.success(null);
        }
        DaVirtualVoicePO virtualVoicePO = daVirtualVoiceManager.getOne(Wrappers.lambdaQuery(DaVirtualVoicePO.class)
                .eq(DaVirtualVoicePO::getBizId, daVirtualManPO.getVoiceBizId())
                .eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO));

        return ResultModel
                .success(DigitalAssetConvert.cnvDaVirtualVoicePO2DTO(virtualVoicePO, channelUtil.getTenantCode()));
    }

    @PostMapping("/vm/specificvoice")
    @ApiOperation("查询单个数字人的指定声音信息")
    public ResultModel<DaVirtualVoiceDTO> vmSpecificVoice(@RequestBody DaVirtualManVoiceRequestDTO requestDTO) {
        Assert.notNull(requestDTO.getVmBizId(), "数字人bizId不能为空");
        Assert.isTrue(StringUtils.isNotBlank(requestDTO.getVoiceCode()), "数字人声音编码不能为空");

        DaVirtualManPO daVirtualManPO = daVirtualManManager.getOne(Wrappers.lambdaQuery(DaVirtualManPO.class)
                .eq(DaVirtualManPO::getBizId, requestDTO.getVmBizId()));
        if (Objects.isNull(daVirtualManPO)) {
            return ResultModel.success(null);
        }
        if (!Objects.equals(requestDTO.getVoiceCode(), daVirtualManPO.getVmVoiceKey())) {
            return ResultModel.success(null);
        }
        DaVirtualVoicePO virtualVoicePO = daVirtualVoiceManager.getOne(Wrappers.lambdaQuery(DaVirtualVoicePO.class)
                .eq(DaVirtualVoicePO::getBizId, daVirtualManPO.getVoiceBizId()).eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO));

        return ResultModel.success(DigitalAssetConvert.cnvDaVirtualVoicePO2DTO(virtualVoicePO, channelUtil.getTenantCode()));
    }

}