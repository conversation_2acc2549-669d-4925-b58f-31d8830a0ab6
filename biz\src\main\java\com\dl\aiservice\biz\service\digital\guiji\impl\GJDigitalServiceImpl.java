package com.dl.aiservice.biz.service.digital.guiji.impl;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.guiji.GJDigitalClient;
import com.dl.aiservice.biz.client.guiji.convert.PageConvertService;
import com.dl.aiservice.biz.client.guiji.req.GJPageRequest;
import com.dl.aiservice.biz.client.guiji.req.GjCreateTrainingRequest;
import com.dl.aiservice.biz.client.guiji.req.GjVideoCreate3DRequest;
import com.dl.aiservice.biz.client.guiji.req.GjVideoCreate3DTssRequest;
import com.dl.aiservice.biz.client.guiji.req.GjVideoCreateRequest;
import com.dl.aiservice.biz.client.guiji.resp.GJAccountResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJAllSpeakersResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJBaseResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJCreateTrainingResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJPageResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJRobotResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJScene;
import com.dl.aiservice.biz.client.guiji.resp.GJTokenResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJTrainResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJUserInfoResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJUserResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJVideoCreateResponse;
import com.dl.aiservice.biz.client.guiji.resp.GJVideoResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.DownloadUtil;
import com.dl.aiservice.biz.common.util.Md5Util;
import com.dl.aiservice.biz.common.util.RedisUtil;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.manager.voiceclone.grammartrans.impl.guiji.GuijiGrammerTransformer;
import com.dl.aiservice.biz.service.digital.AbstractDigitalService;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateTrainingRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.RobotDetailRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TrainRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.VideoCreate3DRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.VideoCreate3DTssRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.AccountResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.AllSpeakersResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateTrainingResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.FreeRobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotDetailDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotDetailResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.SpeakersResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.TrainResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.UserInfoResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.UserResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.VideoCreateResponseDTO;
import com.dl.aiservice.biz.service.digital.enums.DigitalComposeEnum;
import com.dl.aiservice.biz.service.digital.enums.SynthesisStatusEnum;
import com.dl.aiservice.biz.service.digital.guiji.GJDigitalService;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: xuebin
 * @description 硅基智能数字人接口
 * @Date: 2023/2/28 17:50
 */
@Slf4j
@Service
public class GJDigitalServiceImpl extends AbstractDigitalService implements GJDigitalService, PageConvertService {

    private static final String CALL_BACK_URL = "%s/guiji/callback";
    private static final String WEBM = "webm";
    private static final String GUIJI_CACHE_KEY_PREFIX = "guiji_access_token_";
    private static final Long GUIJI_CACHE_EXPIRE_TIME = 7180L;

    @Resource
    private GJDigitalClient gjDigitalClient;
    @Resource
    private AiConfig aiConfig;
    @Resource
    private GuijiGrammerTransformer guijiGrammerTransformer;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private CosFileUploadManager cosFileUploadManager;

    @Value("${digtal.gj.appkey}")
    private String appid;
    @Value("${digtal.gj.secret}")
    private String appSecret;
    @Value("${dl.fileTempPath}")
    public String localPathPrefix;

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.GUI_JI, ServiceChannelEnum.CJHX_GUIJI);
    }

    @Override
    public String getAccessToken(Integer channel) {
        String redisKey = GUIJI_CACHE_KEY_PREFIX + channel;
        String token = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }

        String appId;
        String secret;

        appId = appid;
        secret = appSecret;

        long now = System.currentTimeMillis();
        String encrypt = Md5Util.encrypt(appId + now + secret);
        GJBaseResponse<GJTokenResponse> resp = gjDigitalClient.getToken(appId, String.valueOf(now), encrypt, "sign");
        if (!resp.isSuccess() || StringUtils.isEmpty(resp.getData().getAccessToken())) {
            throw BusinessServiceException.getInstance(resp.getCode().toString(), "硅基智能获取token失败!" + resp.getMessage());
        }

        //放入redis
        redisUtil.set(redisKey, resp.getData().getAccessToken(), GUIJI_CACHE_EXPIRE_TIME);

        return resp.getData().getAccessToken();
    }

    @Override
    public SpeakersResponseDTO allSpeaker() {
        GJBaseResponse<List<GJAllSpeakersResponse>> resp = gjDigitalClient.allSpeaker();
        SpeakersResponseDTO respDto = new SpeakersResponseDTO();
        if (resp.isSuccess()) {
            List<AllSpeakersResponseDTO> gjAllSpeakersResponse = resp.getData().stream().map(i -> {
                AllSpeakersResponseDTO allSpeakersResponseDTO = new AllSpeakersResponseDTO();
                BeanUtils.copyProperties(i, allSpeakersResponseDTO);
                return allSpeakersResponseDTO;
            }).collect(Collectors.toList());
            respDto.setGjAllSpeakersList(gjAllSpeakersResponse);
            return respDto;
        }
        return null;
    }

    @Override
    public CreateResponseDTO videoCreate(CreateRequestDTO request) {
        Assert.notNull(request.getType(), "合成类型不能为空");
        Assert.isTrue(StringUtils.isNotEmpty(request.getSceneId()), "场景id不能为空");
        if (Objects.equals(request.getType(), DigitalComposeEnum.TEXT.getCode())) {
            Assert.isTrue(StringUtils.isNotEmpty(request.getText()), "合成的文本不能为空");
            Assert.isTrue(StringUtils.isNotEmpty(request.getSpeakerId()), "发音人不能为空");
            GjVideoCreateRequest gjVideoCreateRequest = new GjVideoCreateRequest();
            BeanUtils.copyProperties(request, gjVideoCreateRequest);
            gjVideoCreateRequest.setText(guijiGrammerTransformer.grammarTransform(request.getText()));
            gjVideoCreateRequest.setSceneId(request.getSceneId());
            gjVideoCreateRequest.setSpeakerId(request.getSpeakerId());
            if (StringUtils.isNotBlank(request.getVideoFormat())) {
                gjVideoCreateRequest.setVideoFormat(request.getVideoFormat());
            } else {
                gjVideoCreateRequest.setVideoFormat(WEBM);
            }
            gjVideoCreateRequest.setCallbackUrl(String.format(CALL_BACK_URL, getCallbackPrefix()));
            GJBaseResponse<GJVideoCreateResponse> resp =
                    gjDigitalClient.videoCreateTss(gjVideoCreateRequest, request.getUpdateId());
            CreateResponseDTO respDto = new CreateResponseDTO();
            if (resp.isSuccess()) {
                respDto.setTaskId(resp.getData().getVideoId());
            }
            return respDto;
        }
        if (Objects.equals(request.getType(), DigitalComposeEnum.VIDEO.getCode())) {
            Assert.isTrue(StringUtils.isNotEmpty(request.getAudioUrl()), "上传音频不能为空");
            GjVideoCreateRequest gjVideoCreateRequest = new GjVideoCreateRequest();
            BeanUtils.copyProperties(request, gjVideoCreateRequest);
            gjVideoCreateRequest.setAudioUrl(request.getAudioUrl());
            if (StringUtils.isNotBlank(request.getVideoFormat())) {
                gjVideoCreateRequest.setVideoFormat(request.getVideoFormat());
            } else {
                gjVideoCreateRequest.setVideoFormat(WEBM);
            }
            gjVideoCreateRequest.setCallbackUrl(String.format(CALL_BACK_URL, getCallbackPrefix()));
            GJBaseResponse<GJVideoCreateResponse> resp =
                    gjDigitalClient.videoCreate(gjVideoCreateRequest, request.getUpdateId());
            CreateResponseDTO respDto = new CreateResponseDTO();
            if (resp.isSuccess()) {
                respDto.setTaskId(resp.getData().getVideoId());
            }
            return respDto;
        }
        return null;
    }

    @Override
    public ResultPageModel<RobotResponseDTO> robotPageList(PageRequestDTO pageRequestDTO) {
        GJPageRequest gjPageRequest = new GJPageRequest();
        gjPageRequest.setSize(pageRequestDTO.getPageSize());
        gjPageRequest.setPage(pageRequestDTO.getPageIndex());
        GJBaseResponse<GJPageResponse<GJRobotResponse>> resp = gjDigitalClient.robotPageList(gjPageRequest);
        ResultPageModel<RobotResponseDTO> respDto = new ResultPageModel<>();
        if (resp.isSuccess()) {
            respDto = convert(resp.getData(), i -> {
                RobotResponseDTO responseDTO = new RobotResponseDTO();
                BeanUtils.copyProperties(i, responseDTO);
                responseDTO.setId(i.getId().toString());
                return responseDTO;
            });
        }
        return respDto;

    }

    @Override
    public RobotDetailResponseDTO robotDetail(RobotDetailRequestDTO requestDTO) {
        GJPageRequest gjPageRequest = new GJPageRequest();
        gjPageRequest.setSize(1000);
        GJBaseResponse<GJPageResponse<GJRobotResponse>> resp = gjDigitalClient.robotPageList(gjPageRequest);

        RobotDetailResponseDTO robotDetailResponseDTO = new RobotDetailResponseDTO();
        if (resp.isSuccess()) {
            List<GJRobotResponse> records = resp.getData().getRecords();
            Map<Integer, GJRobotResponse> robotResponseMap =
                    records.stream().collect(Collectors.toMap(GJRobotResponse::getId, Function.identity()));
            GJRobotResponse robotResponse = robotResponseMap.get(Integer.valueOf(requestDTO.getId()));
            if (Objects.nonNull(robotResponse)) {
                List<GJScene> sceneList = robotResponse.getSceneList();
                List<RobotDetailDTO> robotDetailList = sceneList.stream().map(scene -> {
                    RobotDetailDTO robotDetailDTO = new RobotDetailDTO();
                    BeanUtils.copyProperties(robotDetailDTO, scene);
                    robotDetailDTO.setSceneId(scene.getId().toString());
                    return robotDetailDTO;
                }).collect(Collectors.toList());
                robotDetailResponseDTO.setRobotDetailList(robotDetailList);
            }
        }
        return robotDetailResponseDTO;

    }

    @Override
    public FreeRobotResponseDTO robotFreeList() {
        GJBaseResponse<List<GJRobotResponse>> response = gjDigitalClient.robotFreeList();
        List<RobotResponseDTO> robotListResponse = response.getData().stream().map(i -> {
            RobotResponseDTO robotResponseDTO = new RobotResponseDTO();
            BeanUtils.copyProperties(i, robotResponseDTO);
            return robotResponseDTO;
        }).collect(Collectors.toList());
        FreeRobotResponseDTO gjFreeRobotResponseDTO = new FreeRobotResponseDTO();
        gjFreeRobotResponseDTO.setFreeRobotResponseList(robotListResponse);
        return gjFreeRobotResponseDTO;
    }

    @Override
    public ResultPageModel<ProgressResponseDTO> videoPageList(PageRequestDTO pageRequestDTO) {
        GJPageRequest gjPageRequest = new GJPageRequest();
        gjPageRequest.setSize(pageRequestDTO.getPageSize());
        gjPageRequest.setPage(pageRequestDTO.getPageIndex());
        GJBaseResponse<GJPageResponse<GJVideoResponse>> resp = gjDigitalClient.videoPageList(gjPageRequest);
        ResultPageModel<ProgressResponseDTO> respDto = new ResultPageModel<>();
        if (resp.isSuccess()) {
            respDto = convert(resp.getData(), i -> {
                ProgressResponseDTO responseDTO = new ProgressResponseDTO();
                BeanUtils.copyProperties(i, responseDTO);
                return responseDTO;
            });
        }
        return respDto;

    }

    @Override
    public UserInfoResponseDTO getUser() {
        GJBaseResponse<GJUserInfoResponse> resp = gjDigitalClient.getUser();
        UserInfoResponseDTO respDto = new UserInfoResponseDTO();
        if (resp.isSuccess()) {
            GJUserResponse gjUserResponse = resp.getData().getUser();
            GJAccountResponse gjAccountResponse = resp.getData().getAccount();
            UserResponseDTO userResponseDTO = new UserResponseDTO();
            AccountResponseDTO accountResponseDTO = new AccountResponseDTO();
            if (Objects.nonNull(gjUserResponse)) {
                BeanUtils.copyProperties(gjUserResponse, userResponseDTO);
            }
            if (Objects.nonNull(gjAccountResponse)) {
                BeanUtils.copyProperties(gjAccountResponse, accountResponseDTO);
            }
            respDto.setUser(userResponseDTO);
            respDto.setAccount(accountResponseDTO);
        }
        return respDto;
    }

    @Override
    public ResultPageModel<UserResponseDTO> allUserList(PageRequestDTO pageRequestDTO) {
        GJPageRequest gjPageRequest = new GJPageRequest();
        gjPageRequest.setSize(pageRequestDTO.getPageSize());
        gjPageRequest.setPage(pageRequestDTO.getPageIndex());
        GJBaseResponse<GJPageResponse<GJUserResponse>> resp = gjDigitalClient.allUserList(gjPageRequest);
        ResultPageModel<UserResponseDTO> respDto = new ResultPageModel<>();
        if (resp.isSuccess()) {
            respDto = convert(resp.getData(), i -> {
                UserResponseDTO responseDTO = new UserResponseDTO();
                BeanUtils.copyProperties(i, responseDTO);
                return responseDTO;
            });
        }
        return respDto;
    }

    @Override
    public VideoCreateResponseDTO create3D(VideoCreate3DRequestDTO request) {
        GjVideoCreate3DRequest gjVideoCreateRequest = new GjVideoCreate3DRequest();
        BeanUtils.copyProperties(request, gjVideoCreateRequest);
        GJBaseResponse<GJVideoCreateResponse> resp = gjDigitalClient.create3D(gjVideoCreateRequest);
        VideoCreateResponseDTO respDto = new VideoCreateResponseDTO();
        if (resp.isSuccess()) {
            respDto.setVideoId(resp.getData().getVideoId());
        }
        return respDto;
    }

    @Override
    public VideoCreateResponseDTO create3DTss(VideoCreate3DTssRequestDTO request) {
        GjVideoCreate3DTssRequest gjVideoCreateRequest = new GjVideoCreate3DTssRequest();
        BeanUtils.copyProperties(request, gjVideoCreateRequest);
        GJBaseResponse<GJVideoCreateResponse> resp = gjDigitalClient.create3Dtss(gjVideoCreateRequest);
        VideoCreateResponseDTO respDto = new VideoCreateResponseDTO();
        if (resp.isSuccess()) {
            respDto.setVideoId(resp.getData().getVideoId());
        }

        return respDto;
    }

    @Override
    public ResultPageModel<TrainResponseDTO> trainPageList(PageRequestDTO pageRequestDTO) {
        GJPageRequest gjPageRequest = new GJPageRequest();
        gjPageRequest.setSize(pageRequestDTO.getPageSize());
        gjPageRequest.setPage(pageRequestDTO.getPageIndex());
        GJBaseResponse<GJPageResponse<GJTrainResponse>> resp = gjDigitalClient.trainPageList(gjPageRequest);
        ResultPageModel<TrainResponseDTO> respDto = new ResultPageModel<>();
        if (resp.isSuccess()) {
            respDto = convert(resp.getData(), i -> {
                TrainResponseDTO responseDTO = new TrainResponseDTO();
                BeanUtils.copyProperties(i, responseDTO);
                return responseDTO;
            });
        }

        return respDto;
    }

    @Override
    public TrainResponseDTO getTrain(TrainRequestDTO gjTrainRequestDTO) {
        GJBaseResponse<GJTrainResponse> resp = gjDigitalClient.getTrain(gjTrainRequestDTO.getTaskId());
        TrainResponseDTO respDto = new TrainResponseDTO();
        if (resp.isSuccess()) {
            BeanUtils.copyProperties(resp.getData(), respDto);
        }
        return respDto;
    }

    @Override
    public ProgressResponseDTO getProgress(TaskRequestDTO taskRequestDTO) {
        GJBaseResponse<GJVideoResponse> resp = gjDigitalClient.getVideo(taskRequestDTO.getTaskId());
        ProgressResponseDTO respDTO = new ProgressResponseDTO();
        if (!resp.isSuccess()) {
            log.error("查询硅基数字人合成进度失败。taskRequestDTO:{},,,resp:{}", JSONUtil.toJsonStr(taskRequestDTO),
                    JSONUtil.toJsonStr(resp));
            return respDTO;
        }

        BeanUtils.copyProperties(resp.getData(), respDTO);
        respDTO.setFailMessage(resp.getData().getFailReason());
        //合成成功
        if (SynthesisStatusEnum.SUCCESS.getCode().equals(resp.getData().getSynthesisStatus())) {
            //将视频转存到cos
            String cosUrl = null;
            boolean isSuccess = false;
            try {
                cosUrl = this.transfertToCos(taskRequestDTO.getMediaJobId(), resp.getData().getVideoUrl());
                //不一样则说明转存成功
                if (!resp.getData().getVideoUrl().equals(cosUrl)) {
                    isSuccess = true;
                }
            } catch (Exception e) {
                log.error("硅基视频转存到cos发生异常！mediaJobId:{},,,videoUrl:{},,,respDTO:{},,,e:{}",
                        taskRequestDTO.getMediaJobId(), resp.getData().getVideoUrl(), JSONUtil.toJsonStr(respDTO), e);
            }

            //不成功的话再重试一次
            if (!isSuccess) {
                try {
                    //将视频转存到cos
                    cosUrl = this.transfertToCos(taskRequestDTO.getMediaJobId(), resp.getData().getVideoUrl());
                    //不一样则说明转存成功
                    if (!resp.getData().getVideoUrl().equals(cosUrl)) {
                        isSuccess = true;
                        log.info("再次转存成功! mediaJobId:{}", taskRequestDTO.getMediaJobId());
                    }
                } catch (Exception e) {
                    log.error("硅基视频再次转存到cos发生异常！用原始视频url作为兜底。mediaJobId:{},,,videoUrl:{},,,respDTO:{},,,e:{}",
                            taskRequestDTO.getMediaJobId(), resp.getData().getVideoUrl(), JSONUtil.toJsonStr(respDTO),
                            e);
                }

                if (!isSuccess) {
                    log.warn("再次转存失败! mediaJobId:{}", taskRequestDTO.getMediaJobId());
                }
            }

            respDTO.setVideoUrl(cosUrl);
        }

        return respDTO;
    }

    @Override
    public CreateTrainingResponseDTO createTraining(CreateTrainingRequestDTO request) {
        GjCreateTrainingRequest gjCreateTrainingRequest = new GjCreateTrainingRequest();
        BeanUtils.copyProperties(request, gjCreateTrainingRequest);
        gjCreateTrainingRequest.setCallbackUrl(String.format(CALL_BACK_URL, getCallbackPrefix()));
        GJBaseResponse<GJCreateTrainingResponse> resp = gjDigitalClient
                .createTraining(gjCreateTrainingRequest, request.getUpdateId());
        CreateTrainingResponseDTO respDto = new CreateTrainingResponseDTO();
        if (resp.isSuccess()) {
            BeanUtils.copyProperties(resp.getData(), respDto);
        }
        return respDto;
    }

    /**
     * 转存到腾讯云cos
     *
     * @param videoUrl
     * @param mediaJobId
     * @return
     */
    @Override
    public String transfertToCos(Long mediaJobId, String videoUrl) {
        String filePath =
                localPathPrefix + mediaJobId + UUID.randomUUID().toString().replaceAll("-", "") + "." + videoUrl
                        .substring(videoUrl.lastIndexOf('.') + 1);
        File videoFile = null;
        String cosUrl;
        try {
            videoFile = DownloadUtil.downloadFile(videoUrl, filePath);
            cosUrl = cosFileUploadManager.uploadFile(videoFile, null, "/temp/visual/dm");
        } catch (Exception e) {
            log.error("硅基数字人视频转存到腾讯云cos失败,用原始视频url作为兜底。taskId:{},,,videoUrl:{},,,e:{}", mediaJobId, videoUrl, e);
            return videoUrl;
            //throw BusinessServiceException.getInstance("硅基数字人视频转存到腾讯云cos失败");
        } finally {
            FileUtils.deleteQuietly(videoFile);
        }
        log.info("硅基视频视频转存到cos taskId:{},videoUrl:{},cosUrl:{}", mediaJobId, videoUrl, cosUrl);

        if (StringUtils.isBlank(cosUrl)) {
            log.error("硅基数字人视频转存到腾讯云cos,cosUrl为空,用原始视频url作为兜底。taskId:{},,,videoUrl:{}", mediaJobId, videoUrl);
            return videoUrl;
        }

        return cosUrl;
    }

    private String getCallbackPrefix() {
        String callback = aiConfig.getCallbackPrefix();
        if (StringUtils.lastIndexOf(callback, Const.SLASH) == (callback.length() - Const.ONE)) {
            return StringUtils.substring(callback, Const.ZERO, callback.length() - Const.ONE);
        }
        return callback;
    }

}

