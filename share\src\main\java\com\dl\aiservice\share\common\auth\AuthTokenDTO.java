package com.dl.aiservice.share.common.auth;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 路由对象
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-08 15:40
 */
@Data
public class AuthTokenDTO implements Serializable {
    private static final long serialVersionUID = 6046853545093945991L;

    private String tenantCode;

    private Integer channel;

    /**
     * 生成时间
     */
    private Date tokenCreateDt;
}
