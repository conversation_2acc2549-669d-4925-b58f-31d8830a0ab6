package com.dl.aiservice.biz.manager.subtitle.aliyun.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-31 17:07
 */
public enum AliyunAsrRespStatusEnum {

    SUCCESS(20000000L, "成功"),

    ;

    private long status;

    private String desc;

    AliyunAsrRespStatusEnum(long status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public long getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
