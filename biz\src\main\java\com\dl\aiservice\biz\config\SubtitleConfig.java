package com.dl.aiservice.biz.config;

import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-22 15:39
 */
@Configuration
public class SubtitleConfig {

    /**
     * 高亮字幕中的关键词的ai提示
     */
    private String keywordsHighlightAiPrompt = "作为短视频制作助手，您的职责是从口语脚本中识别并突出关键词，以增强剧本的表现力。" + "请遵循以下步骤和规则来完成任务："
            + "1.**关键词标记规则**：\\n-使用'<i></i>'标签来包裹关键词。\\n" + "2.**关键词数量限制**：每行最多标记2个关键词。\\n"
            + "3.**优先级规则**：\\n-如果句子中包含数字，应优先将其作为关键词进行标记。\\n" + "4.**输出格式**：每行输出后需添加换行符`\\n`。\\n"
            + "5.**保持原文结构**：输出的格式和结构必须与输入保持一致，确保行数和每行内容的位置不变。\\n" + "6.**输出时无需返回你的分析**\\n" + "6.**学习案例**：\\n"
            + "-通过以下案例学习如何应用上述规则：\\n"
            + "-输入：\\n1、资本大佬陆正耀又栽了\\n2、这次呢是被执行了19个亿\\n3、跟神州系汽车公司相关\\n4、之前呢就是他已经有\\n5、三条执行记录\\n6、之前11个亿\\n7、总共30个亿\\n"
            + "-输出：\\n1、资本大佬陆正耀<i>又栽了</i>\\n2、这次呢是被执行了<i>19个亿</i>\\n3、跟<i>神州系</i>汽车公司相关\\n4、之前呢就是他已经有\\n5、<i>三条</i>执行记录\\n6、之前<i>11个亿</i>\\n7、总共<i>30个亿</i>\\n"
            + "-输入：\\n1、光伏行业迎来价格调整\\n2、2024年6月3日\\n3、硅料价格小幅下降\\n4、每公斤均价降至40.5元\\n5、国产颗粒硅价格\\n6、稳定在36.5-\\n7、37元区间\\n"
            + "-输出：\\n1、光伏行业迎来<i>价格调整</i>\\n2、2024年6月3日\\n3、硅料价格<i>小幅下降</i>\\n4、每公斤均价降至<i>40.5元</i>\\n5、国产颗粒硅价格\\n6、稳定在<i>36.5-</i>\\n7、<i>37元</i>区间\\n"
            + "-输入：\\n1、综上所述《\\n2、歌手2024》的爆火\\n3、不仅直接提升了下游\\n4、内容分发的收视率和收益\\n5、也带动了中游\\n6、内容整合的广告价值\\n7、和营销机会\\n"
            + "-输出：\\n1、综上所述《\\n2、<i>歌手2024</i>》的爆火\\n3、不仅直接提升了下游\\n4、<i>内容分发</i>的收视率和收益\\n5、也带动了中游\\n6、<i>内容整合</i>的广告价值\\n7、和营销机会\\n"
            + "-输入：\\n1、进一步观察\\n2、中游传输设备和网络规划服务\\n3、将迎来产业技术升级的机遇\\n4、传输设备\\n5、在5.5 g技术的推动下\\n6、将实现更高效的数据传输能力\\n"
            + "-输出：\\n1、进一步观察\\n2、<i>中游传输设备</i>和<i>网络规划服务</i>\\n3、将迎来产业技术升级的机遇\\n4、传输设备\\n5、在<i>5.5g技术</i>的推动下\\n6、将实现更高效的数据传输能力\\n"
            + "下面是需要你处理的句子：\\n-输入：\\n";
    /*"作为短视频制作助手，您的职责是从口语脚本中识别并突出关键词，以增强剧本的表现力。"
            + "请遵循以下步骤和规则来完成任务：1.**保持原文结构**：输出的格式和结构必须与输入保持一致，确保行数和每行内容的位置不变。\\n" + "2.**保持原意**：在标记关键词时，确保不改变原脚本的含义。\\n"
            + "3.**关键词标记规则**：\\n-使用'<i></i>'标签来包裹关键词。\\n-关键词可能包括但不限于专有名词、数字、日期和重要指标。\\n"
            + "4.**关键词数量限制**：每行最多标记2个关键词。\\n" + "5.**优先级规则**：\\n-如果句子中包含数字，应优先将其作为关键词进行标记。\\n"
            + "6.**输出格式**：每行输出后需添加换行符`\\n`。\\n" + "7.**输出时无需返回kimi的分析**\\n" + "8.**学习案例**：\\n" + "-通过以下案例学习如何应用上述规则：\\n"
            + "-输入：\\n1、资本大佬陆正耀又栽了\\n2、这次呢是被执行了19个亿\\n3、跟神州系汽车公司相关\\n4、之前呢就是他已经有\\n5、三条执行记录\\n6、之前11个亿\\n7、总共30个亿\\n"
            + "-输出：\\n1、资本大佬陆正耀<i>又栽了</i>\\n2、这次呢是被执行了<i>19个亿</i>\\n3、跟<i>神州系</i>汽车公司相关\\n4、之前呢就是他已经有\\n5、<i>三条执行记录</i>\\n6、之前<i>11个亿</i>\\n7、总共<i>30个亿</i>\\n"
            + "-输入：\\n1、光伏行业迎来价格调整\\n2、2024年6月3日\\n3、硅料价格小幅下降\\n4、每公斤均价降至40.5元\\n5、国产颗粒硅价格\\n6、稳定在36.5-\\n7、37元区间\\n"
            + "-输出：\\n1、光伏行业迎来<i>价格调整</i>\\n2、2024年6月3日\\n3、<i>硅料价格小幅下降</i>\\n4、每公斤均价降至<i>40.5元</i>\\n5、国产颗粒硅价格\\n6、稳定在<i>36.5-</i>\\n7、<i>37元</i>区间\\n"
            + "-输入：\\n1、综上所述《\\n2、歌手2024》的爆火\\n3、不仅直接提升了下游\\n4、内容分发的收视率和收益\\n5、也带动了中游\\n6、内容整合的广告价值\\n7、和营销机会\\n"
            + "-输出：\\n1、综上所述《\\n2、<i>歌手2024</i>》的爆火\\n3、不仅直接提升了下游\\n4、<i>内容分发</i>的收视率和收益\\n5、也带动了中游\\n6、<i>内容整合</i>的广告价值\\n7、和营销机会\\n"
            + "-输入：\\n1、进一步观察\\n2、中游传输设备和网络规划服务\\n3、将迎来产业技术升级的机遇\\n4、传输设备\\n5、在5.5 g技术的推动下\\n6、将实现更高效的数据传输能力\\n"
            + "-输出：\\n1、进一步观察\\n2、<i>中游传输设备</i>和<i>网络规划服务</i>\\n3、将迎来产业技术升级的机遇\\n4、传输设备\\n5、在<i>5.5g技术</i>的推动下\\n6、将实现更高效的数据传输能力\\n"
            + "下面是需要你处理的句子：\\n-输入：\\n";*/
            /*"你作为一个短视频制作助手，特别专注于标识出口语脚本中的关键词。突出句子中的关键短语来强调剧本的重要部分。应该注意不要改变脚本的原始含义，同时确保重点清楚地标记，用“<i></i>”包裹住关键词。\n"
                    + "要求：(1)不改变原文结构，不合并语句，原文案在第几行输出时就在第几行(2)不改变原脚本含义(3)用“<i></i>”包裹住关键词(4)每行关键词个数:0个-2个。（补充：数字，日期，指标也需要标识出来）(5)输出时每行后面需要加\\n(6)若句子中有数字，则优先包裹数字。"
                    + "\n你先学习下下面案列的创作方式及表达方式：\n" + "案例：\n"
                    + "输入：\n1、资本大佬陆正耀又栽了\\n\n2、这次呢是被执行了19个亿\\n\n3、跟神州系汽车公司相关\\n\n4、之前呢就是他已经有\\n\n5、三条执行记录\\n\n6、之前11个亿\\n\n7、总共30个亿\\n\n"
                    + "输出：\n1、资本大佬陆正耀<i>又栽了</i>\\n\n2、这次呢是被执行了<i>19个亿</i>\\n\n3、跟<i>神州系</i>汽车公司相关\\n\n4、之前呢就是他已经有\\n\n5、<i>三条执行记录</i>\\n\n6、之前<i>11个亿</i>\\n\n7、总共<i>30个亿</i>\\n\n"
                    + "\n"
                    + "输入：\n1、光伏行业迎来价格调整\\n\n2、2024年6月3日\\n\n3、硅料价格小幅下降\\n\n4、每公斤均价降至40.5元\\n\n5、国产颗粒硅价格\\n\n6、稳定在36.5-\\n\n7、37元区间\\n\n8、这一价格变动\\n\n9、预计将对\\n\n10、光伏产业链的各个环节\\n\n11、产生不同程度的影响\\n\n"
                    + "输出：\n1、光伏行业迎来<i>价格调整</i>\\n\n2、<i>2024年6月3日</i>\\n\n3、<i>硅料价格</i>小幅下降\\n\n4、每公斤均价降至<i>40.5元</i>\\n\n5、<i>国产颗粒硅价格</i>\\n\n6、稳定在<i>36.5-</i>\\n\n7、<i>37元</i>区间\\n\n8、这一价格变动\\n\n9、预计将对\\n\n10、光伏产业链的各个环节\\n\n11、产生不同程度的影响\\n\n"
                    + "\n" + "输入：\n1、光伏产业链\\n\n2、包括上游的原材料生产处理、\\n\n3、中游的光伏组件制造\\n\n4、以及下游的光伏系统应用\\n\n"
                    + "输出：\n1、光伏产业链\\n\n2、包括上游的<i>原材料生产处理</i>、\\n\n3、中游的<i>光伏组件制造</i>\\n\n4、以及下游的<i>光伏系统应用</i>\\n\n"
                    + "\n"
                    + "输入：\n1、接下来\\n\n2、中游医药产品研制领域\\n\n3、也将受益\\n\n4、化学制剂药、\\n\n5、中药和生物制药的销售\\n\n6、渠道增加\\n\n7、药品销售量有望提升\\n\n8、渗透率也将得到提高\\n\n9、医疗器械通常在医院销售\\n\n10、因此影响相对中性\\n\n"
                    + "输出：\n1、接下来\\n\n2、<i>中游医药产品研制领域</i>\\n\n3、也将<i>受益</i>\\n\n4、<i>化学制剂药</i>、\\n\n5、<i>中药和生物制药</i>的销售\\n\n6、渠道增加\\n\n7、<i>药品销售量</i>有望提升\\n\n8、<i>渗透率</i>也将得到提高\\n\n9、医疗器械通常在医院销售\\n\n10、因此影响相对<i>中性</i>\\n\n"
                    + "\n" + "下面是需要你处理的句子：\n输入：\n";*/
            /*"你作为一个短视频制作助手，特别专注于标识出口语脚本中的关键词。突出句子中的关键短语来强调剧本的重要部分。应该注意不要改变脚本的原始含义，同时确保重点清楚地标记，用“<i> </i>”包裹住关键词。\n"
                    + "要求：\n" + "1）不改变原文结构\n" + "2）不改变原脚本含义\n" + "3）用“<i> </i>”包裹住关键词\n"
                    + "4）每句中关键词个数:0个-2个。（补充：数字，日期，指标 也需要标识出来）\n" + "5）输出每行后面需要加\\n\n" + "6）若句子中有数字，则优先包裹数字\n\n"
                    + "你先学习下下面案列的创作方式及表达方式：\n" + "\n" + "案例：\n" + "输入：\n" + "资本大佬陆正耀又栽了\\n\n" + "这次呢是被执行了19个亿\\n\n"
                    + "跟神州系汽车公司相关\\n\n" + "之前呢就是他已经有\\n\n" + "三条执行记录\\n\n" + "之前11个亿\\n\n" + "总共30个亿\\n\n"
                    + "大家知道陆正义他是个连续创业者\\n\n" + "\n" + "输出：\n" + "资本大佬陆正耀<i>又栽了</i>\\n\n" + "这次呢是被执行了<i>19个亿</i>\\n\n"
                    + "跟<i>神州系</i>汽车公司相关\\n\n" + "之前呢就是他已经有\\n\n" + "<i>三条执行记录</i>\\n\n" + "之前<i>11个亿</i>\\n\n"
                    + "总共<i>30个亿</i>\\n\n" + "大家知道陆正义他是个连续创业者\\n\n\n" + "输入：\n" + "光伏行业迎来价格调整\\n\n" + "2024年6月3日\\n\n"
                    + "硅料价格小幅下降\\n\n" + "每公斤均价降至40.5元\\n\n" + "国产颗粒硅价格\\n\n" + "稳定在36.5-\\n\n" + "37元区间\\n\n"
                    + "这一价格变动\\n\n" + "预计将对\\n\n" + "光伏产业链的各个环节\\n\n" + "产生不同程度的影响\\n\n" + " \n" + "输出：\n"
                    + "光伏行业迎来<i>价格调整</i>\\n\n" + "<i>2024年6月3日</i>\\n\n" + "<i>硅料价格</i>小幅下降\\n\n"
                    + "每公斤均价降至<i>40.5元</i>\\n\n" + "<i>国产颗粒硅价格</i>\\n\n" + "稳定在<i>36.5-</i>\\n\n" + "<i>37元</i>区间\\n\n"
                    + "这一价格变动\\n\n" + "预计将对\\n\n" + "光伏产业链的各个环节\\n\n" + "产生不同程度的影响\\n" + "输入：\n" + "光伏产业链\\n\n"
                    + "包括上游的原材料生产处理、\\n\n" + "中游的光伏组件制造\\n\n" + "以及下游的光伏系统应用\\n\n" + " \n" + "输出：\n" + "光伏产业链\\n\n"
                    + "<i>包括</i>上游的原材料生产处理、\\n\n" + "<i>中游的光伏组件制造</i>\\n\n" + "以及下游的光伏系统应用\\n" + "下面是需要你处理的句子：\n"
                    + "输入：\n";*/

    public String getKeywordsHighlightAiPrompt() {
        return keywordsHighlightAiPrompt;
    }

    public static void main(String[] args) {
        // 输入字符串
        //String input = "1、煤炭产业链从上游的煤矿开采\n2、经过中游的洗选加工\\\\n\n3、最终到达下游的终端消费\\\\n4、哈哈哈哈\\n5、啦啦啦\\n\n";
        String input = "14月18日\\\\n\\n2、华为终端官方微博宣布\\\\n\\n3、\u003ci\u003ePura70系列\u003c/i\u003e“先锋计划”\\\\n\\n4、\u003ci\u003ePura70Ultra\u003c/i\u003e和\u003ci\u003ePura70Pro\u003c/i\u003e\\\\n\\n5、在发布后不到一分钟便售罄\\\\n\\n6、作为华为的影像旗舰\\\\n\\n7、\u003ci\u003eP系列新机\u003c/i\u003e在光学、外观、\\\\n\\n8、卫星通话等方面\\\\n\\n9、进行了多项升级\\\\n\\n10、销量的火爆\\\\n\\n11、证明了技术路径的可行性\\\\n\\n12、预计将对\u003ci\u003e消费电子产业链\u003c/i\u003e\\\\n\\n13、产生深远影响\\\\n";
        // 使用正则表达式来匹配序号，并根据序号分割字符串
        //String[] parts = input.split("(?<=\\d+、)");

        String[] parts = input.split("[\n,\\n,\\\\n]");
        List<String> results = new ArrayList<>();
        for (String part : parts) {
            if (StringUtils.isNotBlank(part)) {
                results.add(part);
            }
        }

        for (int i = 0; i < results.size(); i++) {
            String result = results.get(i).substring(results.get(i).indexOf("、") + 1);
            //String[] aiSubtitle = aiSubtitleList.get(i).split("、");
            //aiSubtileList.add(aiSubtitle[1]);
            System.out.println(result);
        }

        // 打印输出结果
        System.out.println(JSONUtil.toJsonStr(results));
    }
}
