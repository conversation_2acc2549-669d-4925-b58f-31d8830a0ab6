package com.dl.aiservice.biz.client.guiji.intercepter;

import com.dl.aiservice.biz.client.guiji.GJDigitalClient;
import com.dl.aiservice.biz.client.guiji.enums.GjErrCodeEnum;
import com.dl.aiservice.biz.client.guiji.resp.GJBaseResponse;
import com.dl.aiservice.biz.client.guiji.support.GJContextHolder;
import com.dl.aiservice.biz.client.guiji.support.GJDigitalManager;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.Assert;

@Slf4j
public class GJDigitalInterceptor implements Interceptor {


    @SneakyThrows
    @Override
    public boolean beforeExecute(ForestRequest request) {

        String requestUrl = request.getMethod().getMetaRequest().getUrl();
        if (!requestUrl.startsWith(GJDigitalClient.GET_TOKEN)) {
            ChannelUtil channelUtil = ApplicationContextUtils.getContext().getBean(ChannelUtil.class);
            Integer channel = channelUtil.getChannel();

            GJDigitalManager gjDigitalManager = GJContextHolder.getGJManager();
            String accessToken = gjDigitalManager.gjDigitalService().getAccessToken(channel);
            Assert.isTrue(StringUtils.isNotEmpty(accessToken), "硅基智能获取access_token失败");
            request.addQuery(GJDigitalClient.ACCESS_TOKEN, accessToken);
        }
        log.info("before execute:\nrequest: {}", request.getBody().nameValuesMapWithObject());
        return Boolean.TRUE;
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("after execute:\nrequest: {}\nresponse: {}", request.getBody().nameValuesMapWithObject(), response.getContent());
    }


    /**
     * 成功判断方式
     *
     * @param data
     * @param request
     * @param response
     */
    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        if (isIgnore(request)) {
            return;
        }
        fillErrMsg(data);
    }

    private void fillErrMsg(Object data) {
        if (!(data instanceof GJBaseResponse)) {
            return;
        }
        GJBaseResponse resp = (GJBaseResponse) data;
        GjErrCodeEnum errCodeEnum = GjErrCodeEnum.errorCode(resp.getCode());
        if (errCodeEnum != GjErrCodeEnum.ERROR_CODE_0) {
            throw BusinessServiceException.getInstance(errCodeEnum.getErrorCode().toString(), errCodeEnum.getErrorDesc());
        }
    }

    private boolean isIgnore(ForestRequest request) {
        String url = request.getMethod().getMetaRequest().getUrl();
        if (url.contains(GJDigitalClient.VIDEO_PATH)) {
            return Boolean.TRUE;
        }
        if (url.contains(GJDigitalClient.TRAIN_PATH)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
