package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class IvhTrainingCreateResponse implements Serializable {


    private static final long serialVersionUID = -1301231693693196145L;

    /**
     * 制作状态：
     * MAKING：进⾏中
     * SUCCESS：制作成功
     * FAIL：制作失败
     */
    @JsonProperty(value = "Status")
    private String status;
    /**
     * 当 Status：MAKING时，任务进⾏中所处的阶段：
     * EXAMINE：视频审核中
     * TRAIN：模型训练中
     * 当 Status：SUCCESS时，任务进⾏中所处的阶段：
     * END：完成
     */
    @JsonProperty(value = "Stage")
    private String stage;
    /**
     * 制作失败返回的失败原因，便于排查问题
     */
    @JsonProperty(value = "FailMessage")
    private String failMessage;

    /**
     * 处于某个Stage的额外信息，⽬前仅在Stage：END时，返回授权信息（待定）
     */
    @JsonProperty(value = "StageInfo")
    private String stageInfo;

    /**
     * 定制管线id，⽤于排查问题，业务⽆须关注
     */
    @JsonProperty(value = "StageId")
    private String stageId;


}
