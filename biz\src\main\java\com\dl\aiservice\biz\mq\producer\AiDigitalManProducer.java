package com.dl.aiservice.biz.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.mq.AiChannels;
import com.dl.aiservice.biz.mq.delay.DelayedMessageService;
import com.dl.aiservice.biz.mq.enums.DelayLevelEnum;
import com.dl.aiservice.biz.service.digital.dto.req.ProgressRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

/**
 * @describe: 数字人合成请求消息发送
 * @author: zhousx
 * @date: 2023/5/11 13:37
 */
@Slf4j
@Service
public class AiDigitalManProducer {
    @Autowired
    private AiChannels aiChannels;

    @Autowired
    private DelayedMessageService delayedMessageService;

    public void getProgressAndCallBackDelayed(ProgressRequestDTO progressRequestDTO) {
        // 使用延迟消息服务来实现延迟功能
        delayedMessageService.sendDelayedDigitalProgressMessage(progressRequestDTO, DelayLevelEnum.FIVE_SECEND);
        log.info("提交延迟发送数字人查询进度请求的消息,progressRequestDTO:{}", JSONUtil.toJsonStr(progressRequestDTO));
    }
}
