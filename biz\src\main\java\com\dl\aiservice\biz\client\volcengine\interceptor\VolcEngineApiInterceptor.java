package com.dl.aiservice.biz.client.volcengine.interceptor;

import com.dl.aiservice.biz.client.volcengine.config.VolcEngineApiConfig;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.SortedMap;
import java.util.TimeZone;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-26 16:52
 */
@Slf4j
public class VolcEngineApiInterceptor implements Interceptor {

    @SneakyThrows
    @Override
    public boolean beforeExecute(ForestRequest request) {
        VolcEngineApiConfig config = ApplicationContextUtils.getContext().getBean(VolcEngineApiConfig.class);

        String accessKeyID = config.getAccessKeyId();
        String secretAccessKey = config.getSecretAccessKey();

        String host = "open.volcengineapi.com";
        String path = "/";
        String service = "speech_saas_prod";
        String region = "cn-north-1";
        String schema = "https";
        Sign sign = new Sign(region, service, schema, host, path, accessKeyID, secretAccessKey);

        String version = "2023-11-07";
        Date date = new Date();

        String action = request.getArgument(0).toString();
        Object reqBody = request.getArgument(2);
        ObjectMapper objectMapper = new ObjectMapper();
        // 将实例序列化为JSON字符串
        String bodyJson = objectMapper.writeValueAsString(reqBody);
        log.info("VolcEngineApiInterceptor bodyJson:{}", bodyJson);

        byte[] body = bodyJson.getBytes();

        try {
            String xContentSha256 = hashSHA256(body);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
            sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
            String xDate = sdf.format(date);
            String shortXDate = xDate.substring(0, 8);
            String contentType = "application/json; charset=utf-8";
            String signHeader = "content-type;host;x-content-sha256;x-date";
            SortedMap<String, String> realQueryList = new TreeMap<>();
            realQueryList.put("Action", action);
            realQueryList.put("Version", version);
            StringBuilder querySB = new StringBuilder();
            for (String key : realQueryList.keySet()) {
                querySB.append(signStringEncoder(key)).append("=").append(signStringEncoder(realQueryList.get(key)))
                        .append("&");
            }
            querySB.deleteCharAt(querySB.length() - 1);

            String canonicalStringBuilder =
                    "POST" + "\n" + path + "\n" + querySB + "\n" + "content-type:" + contentType + "\n" + "host:" + sign
                            .getHost() + "\n" + "x-content-sha256:" + xContentSha256 + "\n" + "x-date:" + xDate + "\n"
                            + "\n" + signHeader + "\n" + xContentSha256;

            System.out.println(canonicalStringBuilder);

            String hashcanonicalString = hashSHA256(canonicalStringBuilder.getBytes());
            String credentialScope = shortXDate + "/" + region + "/" + service + "/request";
            String signString = "HMAC-SHA256" + "\n" + xDate + "\n" + credentialScope + "\n" + hashcanonicalString;

            byte[] signKey = genSigningSecretKeyV4(sign.getSk(), shortXDate, region, service);
            String signature = bytesToHex(hmacSHA256(signKey, signString));
            System.out.println(signature);

            request.addHeader("Host", host);
            request.addHeader("X-Date", xDate);
            request.addHeader("X-Content-Sha256", xContentSha256);
            request.addHeader("Content-Type", contentType);
            request.addHeader("Authorization",
                    "HMAC-SHA256" + " Credential=" + sign.getAk() + "/" + credentialScope + ", SignedHeaders="
                            + signHeader + ", Signature=" + signature);

        } catch (Exception e) {
            log.error("火山云api拦截器 生成签名失败!e:", e);
            throw BusinessServiceException.getInstance("生成火山云前面失败！");
        }

        log.info("VolcEngineApiInterceptor before execute:\nrequest: {}", request.getBody().nameValuesMapWithObject());
        return Boolean.TRUE;
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("VolcEngineApiInterceptor after execute:\nrequest: {},\nhttpStatus:{},\nresponse: {}",
                request.getBody().nameValuesMapWithObject(), response.getStatusCode(), response.getContent());
    }

    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {

    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {

    }

    private String signStringEncoder(String source) {
        if (source == null) {
            return null;
        }
        StringBuilder buf = new StringBuilder(source.length());
        ByteBuffer bb = Sign.UTF_8.encode(source);
        while (bb.hasRemaining()) {
            int b = bb.get() & 255;
            if (Sign.URLENCODER.get(b)) {
                buf.append((char) b);
            } else if (b == 32) {
                buf.append("%20");
            } else {
                buf.append("%");
                char hex1 = Sign.CONST_ENCODE.charAt(b >> 4);
                char hex2 = Sign.CONST_ENCODE.charAt(b & 15);
                buf.append(hex1);
                buf.append(hex2);
            }
        }

        return buf.toString();
    }

    public static String hashSHA256(byte[] content) throws Exception {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");

            return bytesToHex(md.digest(content));
        } catch (Exception e) {
            throw new Exception("Unable to compute hash while signing request: " + e.getMessage(), e);
        }
    }

    public static byte[] hmacSHA256(byte[] key, String content) throws Exception {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(key, "HmacSHA256"));
            return mac.doFinal(content.getBytes());
        } catch (Exception e) {
            throw new Exception("Unable to calculate a request signature: " + e.getMessage(), e);
        }
    }

    private byte[] genSigningSecretKeyV4(String secretKey, String date, String region, String service)
            throws Exception {
        byte[] kDate = hmacSHA256((secretKey).getBytes(), date);
        byte[] kRegion = hmacSHA256(kDate, region);
        byte[] kService = hmacSHA256(kRegion, service);
        return hmacSHA256(kService, "request");
    }

    public static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = Sign.CONST_ENCODE.toCharArray()[v >>> 4];
            hexChars[j * 2 + 1] = Sign.CONST_ENCODE.toCharArray()[v & 0x0F];
        }
        return new String(hexChars);
    }
}
