package com.dl.aiservice.share.digitalman;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName SceneInfoDTO
 * @Description
 * <AUTHOR>
 * @Date 2023/3/27 15:30
 * @Version 1.0
 **/
@Data
public class SceneInfoDTO implements Serializable {

    private static final long serialVersionUID = 6007098128646896532L;

    @ApiModelProperty(value = "场景id(重要，视频合成必填ID)")
    private String sceneId;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "场景封面地址")
    private String coverUrl;

    @ApiModelProperty(value = "场景样例视频地址")
    private String exampleUrl;

}
