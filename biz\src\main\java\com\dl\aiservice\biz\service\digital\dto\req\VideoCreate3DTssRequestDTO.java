package com.dl.aiservice.biz.service.digital.dto.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@NoArgsConstructor
@Data
public class VideoCreate3DTssRequestDTO implements Serializable {
    private static final long serialVersionUID = -6727712564242004694L;
    /**
     * 合成的文本
     */
    private String text;
    /**
     * 场景ID，从模特列表接口获取
     */
    private String sceneId;
    /**
     * 发音人id
     */
    private String speakerId;
    /**
     * 合成作品名
     */
    private String videoName;
    /**
     * 横向分辨率（默认’1080’）
     */
    private String width;
    /**
     * 纵向分辨率（默认’1920’）
     */
    private String height;
    /**
     * 合成结果回调地址
     */
    private String callbackUrl;
    /**
     * 语速，取值区间：[0-1.0]
     */
    private String speechRate;
    /**
     * 是否生成字幕文件，0-不生成 1-生成
     */
    private String srtFlag;

}