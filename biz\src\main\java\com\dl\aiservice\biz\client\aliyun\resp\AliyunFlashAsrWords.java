package com.dl.aiservice.biz.client.aliyun.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-31 15:15
 */
@Data
public class AliyunFlashAsrWords {

    /**
     * 当前句子包含的词信息。
     */
    private String text;

    /**
     * 当前词开始时间，单位：毫秒。
     */
    @JsonProperty("begin_time")
    private Integer beginTime;

    /**
     * 当前词结束时间，单位：毫秒。
     */
    @JsonProperty("end_time")
    private Integer endTime;

    /**
     * 当前词尾的标点信息，没有标点则为空。
     */
    private String punc;
}
