package com.dl.aiservice.biz.manager.digitalasset;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.aiservice.biz.dal.po.DaVirtualVoicePO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoiceListQueryPairBO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoicePageBO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceChannelDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【da_virtual_voice(数字资产-数字声音信息表)】的数据库操作Service
 * @createDate 2023-06-02 13:53:14
 */
public interface DaVirtualVoiceManager extends IService<DaVirtualVoicePO> {

    /**
     * 查询数字声音信息
     * 优先判断该bizId是否有对应的新bizId，若有则查询新bizId的数据。
     * 强烈建议所有查询单个数字声音信息的地方都走这个接口！！！
     *
     * @param bizId
     * @return
     */
    DaVirtualVoicePO info(Long bizId);

    /**
     * 根据厂商+外部厂商声音唯一标识查询
     *
     * @param channel
     * @param voiceKey
     * @return
     */
    DaVirtualVoicePO infoByChannelVoiceKey(Integer channel, String voiceKey);

    /**
     * 查询租户的声音列表
     *
     * @param tenantCode
     * @param voiceType
     * @param channels
     * @return
     */
    List<DaVirtualVoicePO> listVoice(String tenantCode, Integer voiceType, List<Integer> channels);

    /**
     * 根据条件分页查询
     *
     * @param queryBO 查询条件
     */
    IPage<DaVirtualVoicePO> pageVoice(DaVirtualVoicePageBO queryBO);

    List<DaVirtualVoiceChannelDTO> getVoiceChannel();

    /**
     * 授权租户
     */
    void voiceAuth(Long bizId, List<String> authTenantCodeList);

    /**
     * 根据厂商+声音编码列表查询声音列表
     *
     * @return
     */
    List<DaVirtualVoicePO> listByChannelAndVoiceKeyList(List<DaVirtualVoiceListQueryPairBO> pairList);

}
