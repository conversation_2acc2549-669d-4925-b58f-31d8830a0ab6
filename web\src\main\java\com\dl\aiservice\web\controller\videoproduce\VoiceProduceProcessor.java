package com.dl.aiservice.web.controller.videoproduce;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.callback.AliyunVideoProduceCallBackClient;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.CallbackLogPO;
import com.dl.aiservice.biz.manager.CallbackLogManager;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.videoproduce.VideoProduceHandleManager;
import com.dl.aiservice.biz.register.VideoProduceHelper;
import com.dl.aiservice.share.enums.MediaProduceChannelEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.videoproduce.VideoProduceResponseDTO;
import com.dl.aiservice.share.videoproduce.dto.aliyun.ProduceMediaCompleteParamDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.security.MessageDigest;

/**
 * @ClassName VoiceCloneServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/3/13 9:54
 * @Version 1.0
 **/
@Component
@Slf4j
public class VoiceProduceProcessor {

    @Resource
    private VideoProduceHelper videoProduceHelper;

    @Resource
    private ChannelUtil channelUtil;

    @Resource
    private CallbackLogManager callbackLogManager;

    @Resource
    private AliyunVideoProduceCallBackClient aliyunVideoProduceCallBackClient;

    @Resource
    private MediaProduceJobManager mediaProduceJobManager;

    @Value("${videoProduce.notifyUrl.aliyun}")
    private String aliyunVideoNofifyUrl;

    private VideoProduceHandleManager getHandler() {
        MediaProduceChannelEnum e = MediaProduceChannelEnum.getByCode(channelUtil.getChannel());
        Assert.notNull(e, "该渠道暂不支持视频生产");
        return videoProduceHelper.get(ServiceChannelEnum.getByCode(e.getCode()));
    }

    public static String md5(String needSignatureStr) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(needSignatureStr.getBytes("UTF-8"));
            byte[] summery = md5.digest();
            StringBuilder md5StrBuilder = new StringBuilder();

            for (byte aSummery : summery) {
                if (Integer.toHexString(255 & aSummery).length() == 1) {
                    md5StrBuilder.append("0").append(Integer.toHexString(255 & aSummery));
                } else {
                    md5StrBuilder.append(Integer.toHexString(255 & aSummery));
                }
            }
            return md5StrBuilder.toString();
        } catch (Exception e) {
            System.err.println("获取签名串失败" + e);
            return "";
        }
    }

    private CallbackLogPO initCallbackLog(ServiceChannelEnum e, String extCallbackRespBody, String recordId) {
        CallbackLogPO callbackLog = new CallbackLogPO();
        callbackLog.setExtJobId(recordId);
        // 1.视频合成记录
        callbackLog.setCallbackType(Const.ONE);
        callbackLog.setChannel(e.getCode());
        callbackLog.setExtCallbackRespBody(extCallbackRespBody);
        return callbackLog;
    }

    @Deprecated
    public ResultModel<Boolean> handleAliyunCallback(ProduceMediaCompleteParamDTO param) {
        log.info("阿里云视频合成回调，param={}", JsonUtils.toJSON(param));

        CallbackLogPO callbackLog = initCallbackLog(ServiceChannelEnum.ALIYUN, JSONUtil.toJsonStr(param),
                param.getMessageBody().getJobId());
        ResultModel callbackResult = aliyunVideoProduceCallBackClient.callback(aliyunVideoNofifyUrl, param);
        if (callbackResult.isSuccess()) {
            callbackLog.setStatus(Const.ONE);
        } else {
            callbackLog.setStatus(Const.TWO);
        }
        callbackLog.setCallbackRespBody(JSONUtil.toJsonStr(callbackResult));
        callbackLogManager.save(callbackLog);
        return ResultModel.success(true);
    }
}
