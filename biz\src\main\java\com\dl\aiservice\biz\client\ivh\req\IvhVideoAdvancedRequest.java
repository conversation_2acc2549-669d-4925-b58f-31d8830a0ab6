package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IvhVideoAdvancedRequest {

    /**
     * 视频输出格式，默认值：Mp4TransparentWebm:透明背景webm格式视频，⽀持部分微剪辑能⼒（主播参数⽀持）GreenScreenMp4:绿幕mp4格式视频，不⽀持微剪辑能⼒f:⽀持微剪辑能⼒的mp4格式视频
     */
    @JsonProperty(value = "Format")
    private String format;
    /**
     * 视频背景图⽚下载路径，⽀持jpg、png格式，图⽚分辨率需要与视频分辨率相同，不传默认为绿幕视频。
     */
    @JsonProperty(value = "BackgroundFileUrl")
    private String backgroundFileUrl;
    /**
     * ⽚头视频，⽀持mp4格式，分辨率需要与视频分辨率相同，⽂件⼤⼩限制200M
     */
    @JsonProperty(value = "VideoHeadFileUrl")
    private String videoHeadFileUrl;
    /**
     * ⽚尾视频，⽀持mp4格式，分辨率需要与视频分辨率相同，⽂件⼤⼩限制200M
     */
    @JsonProperty(value = "VideoTailFileUrl")
    private String videoTailFileUrl;
    /**
     * 是否在视频内显示字幕，默认不展示
     */
    @JsonProperty(value = "ShowSubtitles")
    private Boolean showSubtitles;
    /**
     * 定义视频中logo相关参数
     */
    @JsonProperty(value = "LogoParams")
    private List<IvhLogoParams> logoParams;
    /**
     * 定义视频中主播相关参数
     */
    @JsonProperty(value = "AnchorParam")
    private IvhAnchor anchorParam;


}