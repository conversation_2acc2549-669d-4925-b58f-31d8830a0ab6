package com.dl.aiservice.biz.client.Ifly.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class IflyResponseHeader {

    /**
     * 返回码，0表示成功，其它表示异常
     */
    private Integer code;

    /**
     * 返回信息详细描述
     */
    private String message;

    /**
     * 会话的session
     */
    private String session;

    /**
     * 本次会话的id
     */
    private String sid;

    /**
     * 本次会话的id
     */
    private String uid;

    /**
     * 拉流地址，可用VLC等播放器打开
     */
    @JsonProperty(value = "stream_url")
    private String streamUrl;

    /**
     * 状态码
     */
    private Integer status;

    @JsonProperty(value = "task_status")
    private Integer taskStatus;

    /**
     * 本次创建的任务id，⽤于唯⼀标识本次任务
     */
    @JsonProperty(value = "task_id")
    private String taskId;

}
