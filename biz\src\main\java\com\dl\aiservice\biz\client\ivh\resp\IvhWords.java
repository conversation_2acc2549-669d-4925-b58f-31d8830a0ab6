package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class IvhWords {
    /**
     * 句⼦中的⼀个字
     */
    @JsonProperty(value = "Word")
    private String word;
    /**
     * 该字的时间起点，该数值/10000为ms，相当于单位为0.1us
     */
    @JsonProperty(value = "StartTimestamp")
    private Long startTimestamp;
    /**
     * 该字的时间尾点，该数值/10000为ms，相当于单位为0.1usa
     */
    @JsonProperty(value = "EndTimestamp")
    private Long endTimestamp;

}