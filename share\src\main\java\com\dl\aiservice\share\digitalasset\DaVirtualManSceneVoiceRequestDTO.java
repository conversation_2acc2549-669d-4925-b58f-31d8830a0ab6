package com.dl.aiservice.share.digitalasset;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-09 17:30
 */
@Getter
@Setter
public class DaVirtualManSceneVoiceRequestDTO implements Serializable {
    private static final long serialVersionUID = 4796973636038877568L;

    @ApiModelProperty("数字人场景id")
    private String sceneId;

    @ApiModelProperty("数字人声音编码")
    private String voiceCode;
}
