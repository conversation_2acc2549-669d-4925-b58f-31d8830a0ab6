package com.dl.aiservice.biz.manager.voiceclone.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * 克隆音/合成音渠道枚举
 */
public enum VoiceCloneEnum {

    /**
     * 新华智云
     */
    XHZY(0, "XHZY", "新华智云"),
    /**
     * 硅基智能
     */
    GUI_JI(1, "GUI_JI", "硅基"),

    /**
     * 腾讯云克隆音
     */
    IVH(2, "IVH", "腾讯云-克隆音"),
    /**
     * 深声科技
     */
    DEEP_SOUND(3, "DS", "深声科技"),
    /**
     * 阿里云
     */
    ALIYUN(4, "ALIYUN", "阿里云-TTS"),
    /**
     * 腾讯云-tts
     */
    TENCENT_CLOUD(5, "TENCENT_CLOUD_tts", "腾讯云-TTS"),
    /**
     * 火山引擎
     */
    VOLC_ENGINE(6, "VOLC_ENGINE", "火山云"),
    /**
     * 深声科技标准版
     */
    DEEP_SOUND_STANDARD(7, "DS_STANDARD", "深声科技-标准版"),

    /**
     * 科大讯飞
     */
    IFLY_TEK(8, "IFLY_TEK", "科大讯飞"),

    /**
     * 阿里云克隆音
     */
    ALIYUN_DIGIITAL(9, "ALIYUN_DIGIITAL", "阿里云-克隆音"),

    /**
     * HeyGen数字人
     */
    HEYGEN(10, "HeyGen", "诗云"),

    /**
     * 华为云
     */
    HUA_WEI(12, "HUA_WEI", "华为云"),

    /**
     * 孚嘉科技的腾讯云数智人克隆音
     * 孚嘉是一个客户，他们有自己的腾讯云数智人账号
     */
    FUJIA_IVH(14, "FUJIA_IVH", "孚嘉科技的腾讯云数智人克隆音"),

    /**
     * 定力数影的腾讯云数智人
     */
    DLSY_IVH(16, "DLSY_IVH", "定力数影的腾讯云数智人克隆音"),


    /**
     * 微软TTS
     */
    MICROSOFT(17, "MICROSOFT", "微软TTS"),
    ;

    private Integer code;
    private String desc;
    private String cnDesc;

    VoiceCloneEnum(Integer code, String desc, String cnDesc) {
        this.code = code;
        this.desc = desc;
        this.cnDesc = cnDesc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getCnDesc() {
        return cnDesc;
    }

    public static VoiceCloneEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> Objects.equals(code, e.getCode())).findAny().orElse(null);
    }
}
