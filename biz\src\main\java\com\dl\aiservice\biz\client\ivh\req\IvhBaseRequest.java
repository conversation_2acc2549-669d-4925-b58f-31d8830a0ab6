package com.dl.aiservice.biz.client.ivh.req;

import com.dl.aiservice.biz.client.ivh.resp.IvhHeaderResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IvhBaseRequest<T> implements Serializable {


    private static final long serialVersionUID = -6023833530442878791L;
    @JsonProperty(value = "Header")
    private IvhHeaderResponse header;
    @JsonProperty(value = "Payload")
    private T payload;

}
