package com.dl.aiservice.share.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * @describe: ResourceTypeEnum
 * @author: zhousx
 * @date: 2023/3/17 13:33
 */
public enum ResourceTypeEnum {

    MEDIA_PRODUCE(1, "视频合成"),

    AVATAR(2, "数字人"),

    VOICE_CLONE(3, "克隆音"),

    SYNTHETIC_VOICE(4, "合成音");

    private Integer code;
    private String desc;

    ResourceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ResourceTypeEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> Objects.equals(code, e.getCode())).findAny().orElse(null);
    }
}
