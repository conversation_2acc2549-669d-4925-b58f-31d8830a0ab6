package com.dl.aiservice.biz.client.guiji.resp;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class GJTrainResponse implements Serializable {


    private static final long serialVersionUID = -7510384129689758788L;

    /**
     * ID
     */
    private String id;
    /**
     * 标题
     */
    private String title;
    /**
     * 提交的待训练视频地址
     */
    private String videoUrl;
    /**
     * 训练状态 0或空-准备中 1训练中 2训练成功 3训练失败 4审核不通过
     */
    private Integer status;
    /**
     * 回调状态 0或空-初始态 1-回调成功 2-回调失败
     */
    private Integer callbackStatus;
    /**
     * 备注
     */
    private String comment;
    /**
     * 训练完成模特ID
     */
    private Integer robotId;
    /**
     * 训练完成场景ID
     */
    private Integer sceneId;
    /**
     * 模特封面地址
     */
    private String coverUrl;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 训练完成后的模特详情
     */
    private GJRobotResponse gjRobotResponse;
}

