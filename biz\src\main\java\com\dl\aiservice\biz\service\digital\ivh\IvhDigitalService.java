package com.dl.aiservice.biz.service.digital.ivh;

import com.dl.aiservice.biz.service.digital.BaseDigitalService;
import com.dl.aiservice.biz.service.digital.dto.req.GetTimbreRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.ivh.IvhVideoMakeRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ivh.IvhTimbreActionResponseDTO;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhListenTtsRequestDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhListenTtsResponseDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhSmallSampleAnchorResponseDTO;
import com.dl.framework.common.model.ResultModel;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: xuebin
 * @description 数字人针对腾讯云特有的接口
 * @Date: 2023/2/28 17:50
 */
public interface IvhDigitalService extends BaseDigitalService {

    /**
     * 查询VirtualmanKey支持的⾳⾊及动作
     */
    IvhTimbreActionResponseDTO getTimbreAction(GetTimbreRequestDTO request);

    /**
     * tts试听接⼝
     * 通过Appkey和Accesstoken查询客户权限下拥有的前三个****数智⼈对应的形象、服装、姿态、分辨率以及动作。
     *
     * @param request 请求参数
     * @return 返回参数
     */
    IvhListenTtsResponseDTO listenTts(IvhListenTtsRequestDTO request);

    /**
     * 视频制作接⼝-基础版
     * 通使⽤ssml⽂本和数智⼈进⾏视频制作，通过4.4<⾳视频制作进度查询接⼝>接⼝最终返回成品视频和字幕⽂件。不⽀持
     * 定义主播位置等未剪辑⾼级参数，如需使⽤需要切换到4.3 <进阶版>。
     *
     * @param request 请求参数
     * @return 返回参数
     */
    CreateResponseDTO videoMake(@RequestBody IvhVideoMakeRequestDTO request);

    /**
     * 定制模特列表查询
     *
     * @param pageRequest 分页条件
     * @return 返回结果
     */
    ResultModel<IvhSmallSampleAnchorResponseDTO> smallSampleAnchorList(PageRequestDTO pageRequest);

    /**
     * 查询腾讯云资产信息
     *
     * @return
     */
    ResultModel<Object> getServiceAssetInfo();
}

