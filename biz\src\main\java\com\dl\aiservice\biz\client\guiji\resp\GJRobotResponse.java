package com.dl.aiservice.biz.client.guiji.resp;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class GJRobotResponse implements Serializable {

    private static final long serialVersionUID = 8134630941944115645L;
    /**
     * 模特ID
     */
    private Integer id;
    /**
     * 模特名称
     */
    private String robotName;
    /**
     * 模特描述
     */
    private String robotDesc;
    /**
     * 模特封面地址
     */
    private String coverUrl;
    /**
     * 性别1男 2女
     */
    private Integer gender;
    /**
     * 类型0:2D 1:3D
     */
    private Integer version;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 模特类型 1-私人定制 2-会员订阅 3或空-其他
     */
    private Integer type;
    /**
     * 星座
     * 1:'白羊座', 2:'金牛座', 3:'双子座', 4:'巨蟹座', 5:'狮子座', 6:'处女座',
     * 7:'天秤座', 8:'天蝎座', 9:'射手座', 10:'摩羯座', 11:'水瓶座', 12:'双鱼座'
     */
    private Integer starSigns;

    /**
     * 数字人场景列表
     */
    public List<GJScene> sceneList;

}

