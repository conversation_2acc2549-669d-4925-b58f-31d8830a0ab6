package com.dl.aiservice.biz.common.util;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-02 14:44
 */
@Component
public class CosUtil {


    private static final String BIAS = "//";
    private static final String COS = ".cos.";
    private static final String MYQCLOUD = ".myqcloud.com";


    /**
     * 根据CosUrl获取cos相关参数
     */
    public static CosBean buildCosByPathPrefix(String pathPrefix) {

        int bias = pathPrefix.indexOf(BIAS);
        int cos = pathPrefix.indexOf(COS);
        int myqloud = pathPrefix.indexOf(MYQCLOUD);
        return CosBean.builder()
                .region(pathPrefix.substring(cos + 5, myqloud))
                .key(pathPrefix.substring(myqloud + 14))
                .bucketName(pathPrefix.substring(bias + 2, cos)).build();
    }
}
