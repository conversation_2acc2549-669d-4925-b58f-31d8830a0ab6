package com.dl.aiservice.biz.client.guiji.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class GJPageRequest implements Serializable {

    private static final long serialVersionUID = 6070190914627402300L;
    /**
     * 请求页数
     */
    private Integer page = 1;
    /**
     * 每页
     */
    private Integer size = 10;


}

