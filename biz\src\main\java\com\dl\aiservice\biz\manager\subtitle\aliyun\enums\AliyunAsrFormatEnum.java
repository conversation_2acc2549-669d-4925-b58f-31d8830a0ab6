package com.dl.aiservice.biz.manager.subtitle.aliyun.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-31 16:38
 */
public enum AliyunAsrFormatEnum {

    WAV("WAV", "wav"),
    OPUS("OPUS", "opus"),
    AAC("AAC", "aac"),
    MP3("MP3", "mp3");

    /**
     * 音频编码格式。支持格式：WAV/OPUS/AAC/MP3。
     */
    private String format;

    /**
     * 文件后缀
     */
    private String suffix;

    AliyunAsrFormatEnum(String format, String suffix) {
        this.format = format;
        this.suffix = suffix;
    }

    public String getFormat() {
        return format;
    }

    public String getSuffix() {
        return suffix;
    }

    public static AliyunAsrFormatEnum parse(String suffix) {
        if (StringUtils.isBlank(suffix)) {
            return null;
        }
        for (AliyunAsrFormatEnum formatEnum : AliyunAsrFormatEnum.values()) {
            if (suffix.equals(formatEnum.getSuffix())) {
                return formatEnum;
            }
        }
        return null;
    }
}
