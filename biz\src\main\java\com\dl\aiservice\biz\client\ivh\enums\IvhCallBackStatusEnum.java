package com.dl.aiservice.biz.client.ivh.enums;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;

public enum IvhCallBackStatusEnum {

    /**
     * 视频合成任务回调
     */
    SUCCESS(0, "SUCCESS"),

    /**
     * 视频训练任务回调
     */
    FAIL(-1, "FAIL");


    private final Integer code;
    private final String desc;

    IvhCallBackStatusEnum(Integer errorCode, String errorDesc) {
        this.code = errorCode;
        this.desc = errorDesc;
    }

    public static IvhCallBackStatusEnum getEnum(String desc) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getDesc(), desc)).findFirst().orElseThrow(() -> BusinessServiceException.getInstance("枚举异常"));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
