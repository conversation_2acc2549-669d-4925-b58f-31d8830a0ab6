package com.dl.aiservice.biz.client.deepsound.intercepter;

import com.dl.aiservice.biz.client.deepsound.DeepSoundStandardClient;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import static cn.hutool.crypto.SecureUtil.md5;


@Slf4j
public class DeepSoundStandardInterceptor implements Interceptor {

    @SneakyThrows
    @Override
    public boolean beforeExecute(ForestRequest request) {
        //秒级时间戳
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        String sign = md5(DeepSoundStandardClient.APP_ID + timeStamp + DeepSoundStandardClient.APP_SECRET).toUpperCase();
        request.addHeader(DeepSoundStandardClient.HEADER_SIGN, DeepSoundStandardClient.SIGN_KEY + sign);
        request.addHeader(DeepSoundStandardClient.HEADER_APP_ID, DeepSoundStandardClient.APP_ID);
        request.addHeader(DeepSoundStandardClient.HEADER_TIMESTAMP, timeStamp);
        log.info("before execute:\nrequest: {}", request.getBody().nameValuesMapWithObject());
        return Boolean.TRUE;
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("after execute:\nrequest: {}\nresponse: {}", request.getBody().nameValuesMapWithObject(), response.getContent());
    }
}
