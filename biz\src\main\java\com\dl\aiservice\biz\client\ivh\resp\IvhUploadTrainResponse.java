package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IvhUploadTrainResponse implements Serializable {


    private static final long serialVersionUID = -1301231693693196145L;

    /**
     * 临时密钥信息
     */
    @JsonProperty(value = "Credentials")
    private IvhUploadResponse credentials;
    /**
     * 临时证书有效的时间，返回 Unix 时间戳，精确到秒
     */
    @JsonProperty(value = "ExpiredTime")
    private String expiredTime;
    /**
     * 上传到cos上的路径前缀
     */
    @JsonProperty(value = "PathPrefix")
    private String pathPrefix;


}
