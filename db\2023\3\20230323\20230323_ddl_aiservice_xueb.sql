

--训练记录表 
CREATE TABLE `train_job` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `tenant_code` varchar(100) NOT NULL DEFAULT '' COMMENT '租户代码',
  `channel` tinyint(1) NOT NULL DEFAULT '1' COMMENT '渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云',
  `job_type` tinyint NOT NULL COMMENT '训练类型：0 数字人训练；1 声音训练 ',
  `train_job_id`  bigint NOT NULL COMMENT '训练id 雪花算法',
  `ext_job_id` varchar(255) DEFAULT '' COMMENT '第三方训练id',
  `ext_model_code` varchar(255) DEFAULT '' COMMENT '第三方训练人模型编号',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '训练状态：1 训练中；0 训练完成；-1 训练失败',
  `job_content` text COMMENT '请求三方报文', 
  `callback_url` varchar(255) DEFAULT '' COMMENT '回调业务url',
  `train_name` varchar(255) DEFAULT '' COMMENT '训练名称 深声：声讯编码',
  `fail_code` varchar(64) NOT NULL DEFAULT '' COMMENT '三方错误码',
  `fail_reason` varchar(255) NOT NULL DEFAULT '' COMMENT '失败原因',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_code` (`tenant_code`),
  KEY `idx_train_job_id` (`train_job_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='训练任务表';


--媒体合成记录表
CREATE TABLE `media_produce_job` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `media_job_id` bigint NOT NULL COMMENT '媒体id 雪花算法',
  `ext_job_id` varchar(255) DEFAULT '' COMMENT '第三方媒体id',
  `tenant_code` varchar(100) NOT NULL DEFAULT '' COMMENT '租户代码',
  `channel` tinyint(1) NOT NULL DEFAULT '1' COMMENT '渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云',
  `job_type` tinyint NOT NULL DEFAULT '0' COMMENT '合成类型：0-视频合成 1-数字人 2-TTS音频',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '任务状态：1 合成中；0 合成完成；-1 合成失败',
  `callback_url` varchar(255) DEFAULT '' COMMENT '回调业务url',
  `job_content` text COMMENT '请求三方报文', --text
  `media_url` varchar(255) DEFAULT '' COMMENT '合成后url',
  `cover_url` varchar(255) DEFAULT '' COMMENT '封面图url',
  `duration` float(255,2) DEFAULT 0.00 COMMENT '时长(单位：秒，如11.32)',
  `media_name` varchar(100) NOT NULL DEFAULT '' COMMENT '作品名称',
  `fail_code` varchar(64) NOT NULL DEFAULT '' COMMENT '三方错误码',
  `fail_reason` varchar(255) NOT NULL DEFAULT '' COMMENT '失败原因',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '修改时间',
  `works_biz_id` bigint NOT NULL COMMENT '上层业务作品唯一标识',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_code` (`tenant_code`),
  KEY `idx_media_job_id` (`media_job_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='媒体合成记录表';




--回调记录表
CREATE TABLE `callback_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `ext_job_id` varchar(100) NOT NULL DEFAULT '' COMMENT '三方事件唯一id',
  `callback_type` tinyint NOT NULL DEFAULT '1' COMMENT '回调类型：1 视频合成回调 2数字人训练 3声纹训练',
  `channel` tinyint(1) NOT NULL DEFAULT '1' COMMENT '渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云',
  `ext_callback_resp_body` text COMMENT '渠道回调响应原文，json串',
  `callback_resp_body` text COMMENT '回调定力业务响应原文，json串',
  `retry_count` int NOT NULL DEFAULT '0' COMMENT '回调定力业务重试次数',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '回调定力业务状态 0回调中 1 成功 2失败 ',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`ext_job_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='回调记录表';
