package com.dl.aiservice.biz.manager.aigc;

import com.dl.aiservice.biz.client.kimi.resp.ChatCompletionMessage;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 11:22
 */
public interface KimiManager {

    /**
     * 与kimi对话
     *
     * @param messages 包含迄今为止对话的消息列表
     * @return
     */
    ChatCompletionMessage chatCompletion(String model, Integer respMaxToken, List<ChatCompletionMessage> messages);

    /**
     * 流式地与kimi对话
     *
     * @param userId
     * @param messages
     * @return
     */
    InputStream chatCompletionStream(Long userId, String model, Integer respMaxToken,
            List<ChatCompletionMessage> messages);

    /**
     * 文件上传
     *
     * @param fileUrl
     * @return fileId
     */
    String fileUpload(String fileUrl);

    /**
     * 文件上传
     *
     * @param multipartFile
     * @return fileId
     */
    String fileUploadFile(MultipartFile multipartFile);

    /**
     * 根据文件id查看文件内容
     *
     * @param fileId
     * @return
     */
    String fileContent(String fileId);

    /**
     * 文件删除
     *
     * @param fileId
     * @return
     */
    boolean fileDelete(String fileId);

    /**
     * 预估耗费token数量
     *
     * @param messages
     * @return
     */
    Long estimateTokenCount(String model, Integer respMaxToken, List<ChatCompletionMessage> messages);

    /**
     * 预估耗费token数量是否超过限制
     *
     * @param messages
     * @return 超过返回true；未超过返回false
     */
    boolean estimateTokenCountExceedLimit(String model, Integer respMaxToken, List<ChatCompletionMessage> messages);

}
