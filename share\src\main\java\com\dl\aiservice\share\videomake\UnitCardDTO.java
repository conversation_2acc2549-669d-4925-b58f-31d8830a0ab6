package com.dl.aiservice.share.videomake;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * @ClassName UnitCardMessageDTO
 * @Description 视频模板 单元卡片 报文对象
 * <AUTHOR>
 * @Date 2023/4/14 9:50
 * @Version 1.0
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UnitCardDTO {

    @ApiModelProperty("卡片唯一标识")
    String unitKey;

    @ApiModelProperty("卡片包含的变量列表")
    List<UnitCardVarDTO> variables;
}
