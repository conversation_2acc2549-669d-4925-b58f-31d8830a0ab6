package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class IvhTrainingCreateRequest implements Serializable {


    private static final long serialVersionUID = -1301231693693196145L;

    /**
     * 主播名称
     */
    @JsonProperty(value = "AnchorName")
    private String anchorName;
    /**
     * 定制类别，⽬前只⽀持IMAGE：形象定制
     */
    @JsonProperty(value = "MakeType")
    private String makeType;
    /**
     * 请提供合规性视频url地址，要求：
     * url地址为通过4.1上传到指定路径的资源url地址后增加idcard路径，例
     * 如：域名/customer-pipline/{数字}/idcard/a.mp4/
     * ⼈物为⼩样本训练⼈物
     * ⼿持本⼈身份证
     * 说出要求话术："我是上传视频⾳频的本⼈，所提供的视频和⾳频经过本
     * ⼈授权，本⼈知悉并同意所提供材料⽤于数智⼈定制。"
     */
    @JsonProperty(value = "IdentityCosUrl")
    private String identityCosUrl;

    /**
     * 语速（1.0为正常语速，范围[0.5-1.5]，值为0.5时播报语速最慢，值为1.5时播报语速最快）
     */
    @JsonProperty(value = "MaterialCosUrl")
    private String materialCosUrl;

    /**
     * 语速（1.0为正常语速，范围[0.5-1.5]，值为0.5时播报语速最慢，值为1.5时播报语速最快）
     */
    @JsonProperty(value = "IsHaveBackground")
    private Boolean isHaveBackground;


}
