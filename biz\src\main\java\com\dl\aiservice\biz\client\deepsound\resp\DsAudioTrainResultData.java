package com.dl.aiservice.biz.client.deepsound.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName DsDiffInfo
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 15:55
 * @Version 1.0
 **/
@Data
public class DsAudioTrainResultData implements Serializable {

    private static final long serialVersionUID = 3593481160681924569L;

    /**
     * 服务内部对当前提交音色的唯 一编号
     */
    @JsonProperty("business_id")
    private String businessId;

    /**
     * 业务方对当前提交音色的唯一 编号
     */
    @JsonProperty("record_id")
    private String recordId;
}
