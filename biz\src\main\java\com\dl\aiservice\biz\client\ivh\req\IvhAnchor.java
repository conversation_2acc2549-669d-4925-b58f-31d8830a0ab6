package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class IvhAnchor {


    /**
     * 定义主播横向位置（0为中间位
     * 置，范围[-0.5, 0.5]，-0.5为最
     * 左，0.5为最右) 其中，不同主播的
     * Tag会效果不同，Tag值⻅接⼝
     * 4.6：Basic：可以左右移动，即⽀
     * 持[-0.5, 0.5]Standard：可以左右
     * 移动，即⽀持[-0.5,
     * 0.5]Advanced：不⽀持左右移
     * 动，不管值为何，都会当作0值处
     * 理
     */
    @JsonProperty(value = "HorizontalPosition")
    public Double horizontalPosition;

    /**
     * 定义主播横向位置（0为中间位置，范围[-0.5, 0.5]，-0.5为最左，0.5为最右)其中，不同主播的Tag会效果不同，
     * Tag值⻅接⼝4.6：Basic：可以左右移动，即⽀持[-0.5, 0.5]Standard：可以左右移动，即⽀持[-0.5,0.5]Advanced：不⽀持左右移动，不管值为何，都会当作0值处理
     */
    @JsonProperty(value = "VerticalPosition")
    public Double verticalPosition;

    /**
     * 主播⼤⼩（1是默认⼤⼩，范围(0,1]）
     */
    @JsonProperty(value = "Scale")
    public Double scale;

    /**
     * 主播⻆度（默认0度，范围
     * [0,360]）其中，不同主播的Tag会
     * 效果不同，Tag值⻅接⼝4.6：
     * Basic：仅3D主播⽀持Standard：
     * 不⽀持Advanced：不⽀持
     */
    @JsonProperty(value = "Angle")
    public Integer angle;
}