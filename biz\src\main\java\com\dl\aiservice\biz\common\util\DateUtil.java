package com.dl.aiservice.biz.common.util;


import com.dl.aiservice.biz.common.constant.DateConsts;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

public class DateUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);
    public static final String Y_M_D = DateConsts.Y_M_D;
    public static final String Y_M_DH_M = DateConsts.Y_M_DH_M;
    public static final String Y_M_D_H_M_S = DateConsts.Y_M_D_H_M_S;
    public static final String Y_M_D_H_M_S_S = DateConsts.Y_M_D_H_M_S_S;
    public static final String Y_M_D_H_M_S_S_C = DateConsts.Y_M_D_H_M_S_S_C;
    public static final String YMD = DateConsts.YMD;
    public static final String YMDHMS = DateConsts.YMDHMS;
    public static final String YMDHMSS = DateConsts.YMDHMSS;
    public static final String Y_M_D_H_M_S_TZ = DateConsts.Y_M_D_H_M_S_TZ;
    public static final String M_D_H_M = DateConsts.M_D_H_M;
    public static final String Y_M_DH_M_CN = DateConsts.Y_M_DH_M_CN;
    public static final String H = DateConsts.H;
    public static final String M_D = DateConsts.M_D;

    public static final long SEC_MILLIS = DateConsts.SEC_MILLIS;
    public static final long MINUTE_MILLIS = DateConsts.MINUTE_MILLIS;
    public static final long HOUR_MILLIS = DateConsts.HOUR_MILLIS;
    public static final long DAY_MILLIS = DateConsts.DAY_MILLIS;
    public static final long WEEK_MILLIS = DateConsts.WEEK_MILLIS;

    public static final Integer MAX_HOUR = DateConsts.MAX_HOUR;
    public static final Integer MAX_MINUTE = DateConsts.MAX_MINUTE;
    public static final Integer MAX_SECOND = DateConsts.MAX_SECOND;

    public static final Integer MIN_HOUR = DateConsts.MIN_HOUR;
    public static final Integer MIN_MINUTE = DateConsts.MIN_MINUTE;
    public static final Integer MIN_SECOND = DateConsts.MIN_SECOND;
    public static final Integer MIN_MILLISECOND = DateConsts.MIN_MILLISECOND;

    /**
     * 格式化
     *
     * @param strDateTime
     * @param format
     * @return Date
     */
    public static Date parse(String strDateTime, String... format) {
        try {
            Date date = DateUtils.parseDate(strDateTime, format);
            return date;
        } catch (Exception e) {
            LOGGER.error("时间格式化失败 format{}！", format, e);
            return null;
        }
    }

    public static String format(Date date, String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        try {
            String dateStr = formatter.format(date);
            return dateStr;
        } catch (Exception e) {
            LOGGER.error("时间格式化失败 format{}！", format, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 时间描述 1天是 24*3600*1000毫秒
     *
     * @param date
     * @return
     */
    public static String formatDesc(Date date) {
        if (null == date) {
            return null;
        }
        Calendar now = Calendar.getInstance();
        Calendar time = Calendar.getInstance();
        time.setTime(date);
        // 毫秒偏移量
        long t = (now.getTimeInMillis() - time.getTimeInMillis()) / 1000;
        // 昨天以前的，直接显示日期，如2012-01-08，如果有多余空间，显示日期及时间2012-01-08 09:20
        if ((t < 0) || (t > ((now.get(Calendar.HOUR_OF_DAY) + 24) * 3600 + now.get(Calendar.MINUTE) + 60 + now
                .get(Calendar.SECOND))) || (t < 0)) {
            SimpleDateFormat df = new SimpleDateFormat("yyyy/MM/dd");
            return df.format(date);
        }

        // 1个小时以上，但还在今天内，显示为"今天 09:30"
        if (t > 3600) {
            SimpleDateFormat df = new SimpleDateFormat("HH:mm");
            String str = df.format(date);
            int day_time = time.get(Calendar.DAY_OF_YEAR);// 评论的天
            int now_time = now.get(Calendar.DAY_OF_YEAR);// 当前天

            // 1个小时以上，但在昨天内，显示为"昨天11:59"
            if (day_time < now_time) {
                str = "昨天 ".concat(str);
            } else {
                str = "今天 ".concat(str);
            }
            return str;
        }
        // 1分钟到1个小时内，显示为"XX分钟前"
        if (t >= 60) {
            return t / 60 + "分钟前";
        }
        // 小于1分钟的为刚刚
        return "刚刚";
    }

    /**
     * 在当前年份基础上，增加年份
     *
     * @param amount
     * @return Date
     * <AUTHOR>
     */
    public static Date addYear(int amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, amount);
        return calendar.getTime();
    }

    /**
     * 在当前月份基础上增加月份
     *
     * @param amount
     * @return Date
     * <AUTHOR>
     */
    public static Date addMonth(int amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, amount);
        return calendar.getTime();
    }

    /**
     * 在给定月份基础上增加月份
     *
     * @param amount
     * @return Date
     * <AUTHOR>
     */
    public static Date addMonth(int amount, Date source) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(source);
        calendar.add(Calendar.MONTH, amount);
        return calendar.getTime();
    }

    /**
     * 在给定日期基础上增加周
     *
     * @param amount
     * @return Date
     * <AUTHOR>
     */
    public static Date addWeek(int amount, Date source) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(source);
        calendar.add(Calendar.WEEK_OF_MONTH, amount);
        return calendar.getTime();
    }

    /**
     * 在当前日基础增加天数
     *
     * @param amount
     * @return Date
     * <AUTHOR>
     */
    public static Date addDay(int amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, amount);
        return calendar.getTime();
    }

    /**
     * 在给定日期基础增加天数
     *
     * @param amount
     * @return Date
     * <AUTHOR>
     */
    public static Date addDay(int amount, Date source) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(source);
        calendar.add(Calendar.DAY_OF_MONTH, amount);
        return calendar.getTime();
    }

    /**
     * 在给定日期基础增加秒
     *
     * @param amount
     * @return Date
     * <AUTHOR>
     */
    public static Date addSecond(int amount, Date source) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(source);
        calendar.add(Calendar.SECOND, amount);
        return calendar.getTime();
    }

    /**
     * 在给定日期基础增加小时
     *
     * @param amount
     * @return Date
     * <AUTHOR>
     */
    public static Date addHour(int amount, Date source) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(source);
        calendar.add(Calendar.HOUR_OF_DAY, amount);
        return calendar.getTime();
    }

    /**
     * 在当前小时基础增加小时
     *
     * @param amount
     * @return Date
     * <AUTHOR>
     */
    public static Date addHour(int amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, amount);
        return calendar.getTime();
    }

    public static Date addMinute(int amount, Date source) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(source);
        calendar.add(Calendar.MINUTE, amount);
        return calendar.getTime();
    }

    /**
     * 获取时间之间的差值
     *
     * @param earlyDate
     * @param lateDate
     * @param returnTimeFormat
     * @return
     */
    public static long between(Date earlyDate, Date lateDate, int returnTimeFormat) {
        Calendar cnow = Calendar.getInstance();
        cnow.setTime(earlyDate);
        Calendar clast = Calendar.getInstance();
        clast.setTime(lateDate);
        return between(cnow, clast, returnTimeFormat);
    }

    /**
     * 获取时间之间的差值
     *
     * @param earlyDate
     * @param lateDate
     * @param returnTimeFormat
     * @return
     */
    public static long between(Calendar earlyDate, Calendar lateDate, int returnTimeFormat) {
        if (returnTimeFormat == Calendar.MILLISECOND) {
            long between = lateDate.getTimeInMillis() - earlyDate.getTimeInMillis();
            return between;
        } else if (returnTimeFormat == Calendar.SECOND) {
            long between = lateDate.getTimeInMillis() - earlyDate.getTimeInMillis();
            return (between + SEC_MILLIS - 1) / SEC_MILLIS;
        } else if (returnTimeFormat == Calendar.MINUTE) {
            long between = lateDate.getTimeInMillis() - earlyDate.getTimeInMillis();
            return (between + MINUTE_MILLIS - 1) / MINUTE_MILLIS;
        } else if (returnTimeFormat == Calendar.HOUR) {
            long between = lateDate.getTimeInMillis() - earlyDate.getTimeInMillis();
            return (between + HOUR_MILLIS - 1) / HOUR_MILLIS;
        } else if (returnTimeFormat == Calendar.DATE || returnTimeFormat == Calendar.DAY_OF_YEAR) {//天
            long between = lateDate.getTimeInMillis() - earlyDate.getTimeInMillis();
            return (between + DAY_MILLIS - 1) / DAY_MILLIS;
        } else if (returnTimeFormat == Calendar.WEEK_OF_MONTH || returnTimeFormat == Calendar.WEEK_OF_YEAR) {
            long between = lateDate.getTimeInMillis() - earlyDate.getTimeInMillis();
            return (between + WEEK_MILLIS - 1) / WEEK_MILLIS;
        } else if (returnTimeFormat == Calendar.MONTH) {
            int year = lateDate.get(Calendar.YEAR) - earlyDate.get(Calendar.YEAR);
            int month = lateDate.get(Calendar.MONTH) - earlyDate.get(Calendar.MONTH);
            int between = year * 12 + month;
            Calendar tmpEnd = (Calendar) earlyDate.clone();
            tmpEnd.add(Calendar.MONTH, between);
            if (tmpEnd.before(lateDate)) {
                between += 1;
            }
            return between;
        } else if (returnTimeFormat == Calendar.YEAR) {
            int between = lateDate.get(Calendar.YEAR) - earlyDate.get(Calendar.YEAR);
            Calendar tmpEnd = (Calendar) earlyDate.clone();
            tmpEnd.add(Calendar.YEAR, between);
            if (tmpEnd.before(lateDate)) {
                between += 1;
            }
            return between;
        } else {
            throw new IllegalArgumentException("不支持的日期格式");
        }
    }

    /**
     * @param date
     * @param field
     * @return
     */
    public static Date truncate(Date date, int field) {
        return DateUtils.truncate(date, field);
    }

    /**
     * @param calendar
     * @param field
     * @return
     */
    public static Calendar truncate(Calendar calendar, int field) {
        return DateUtils.truncate(calendar, field);
    }

    /**
     * 计算年龄
     *
     * @param now
     * @param birthDay
     * @return
     */
    public static String calAge(Date now, Date birthDay) {
        if (now == null || birthDay == null || birthDay.after(now)) {
            LOGGER.warn("计算详细年龄失败：出生日期为空");
            return "年龄未知";
        }

        Calendar createDate = Calendar.getInstance();
        createDate.setTime(now);
        Calendar birthDate = Calendar.getInstance();
        birthDate.setTime(birthDay);

        int day = createDate.get(Calendar.DAY_OF_MONTH) - birthDate.get(Calendar.DAY_OF_MONTH);
        int month = createDate.get(Calendar.MONTH) - birthDate.get(Calendar.MONTH);
        int year = createDate.get(Calendar.YEAR) - birthDate.get(Calendar.YEAR);

        if (day < 0) {
            month--;
            createDate.add(Calendar.MONTH, -1);// 得到上一个月，用来得到上个月的天数。
        }
        if (month < 0) {
            month = (month + 12) % 11;
            year--;
        }

        String result = "";
        if (year > 0) {
            result += year + "岁";
        }
        if (month > 0 && year < 11) {
            result += month + "个月";
        }
        if (StringUtils.isBlank(result)) {
            result = "未满月";
        }

        return result;
    }

    /**
     * 获取指定日期的最大时间
     *
     * @param date 如2019-01-04 11:11:11
     * @return 2019-01-04 23:59:59
     */
    public static Date getMaxDate(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, MAX_HOUR);
        calendar.set(Calendar.MINUTE, MAX_MINUTE);
        calendar.set(Calendar.SECOND, MAX_SECOND);
        return calendar.getTime();
    }

    /**
     * 获取指定日期的最小时间
     *
     * @param date 如2019-01-04 11:11:11
     * @return 2019-01-04 00:00:00.000
     */
    public static Date getMinDate(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, MIN_HOUR);
        calendar.set(Calendar.MINUTE, MIN_MINUTE);
        calendar.set(Calendar.SECOND, MIN_SECOND);
        calendar.set(Calendar.MILLISECOND, MIN_MILLISECOND);
        return calendar.getTime();
    }

    /**
     * 获取整点时间
     *
     * @param date
     * @return
     */
    public static Date getIntegralHour(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MINUTE, MIN_MINUTE);
        calendar.set(Calendar.SECOND, MIN_SECOND);
        calendar.set(Calendar.MILLISECOND, MIN_MILLISECOND);
        return calendar.getTime();
    }

    /**
     * date2比date1多的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int differentDays(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        //不同年
        if (year1 != year2) {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                //闰年
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0) {
                    timeDistance += 366;
                } else {
                    //不是闰年
                    timeDistance += 365;
                }
            }
            return timeDistance + (day2 - day1);
        } else {
            //同年
            //LOGGER.debug("判断day2 - day1 : " + (day2 - day1));
            return day2 - day1;
        }
    }

    /**
     * 获取每天的开始时间 00:00:00:00
     */
    public static Date getStartTime(Date date) {
        Calendar dateStart = Calendar.getInstance();
        dateStart.setTime(date);
        dateStart.set(Calendar.HOUR_OF_DAY, 0);
        dateStart.set(Calendar.MINUTE, 0);
        dateStart.set(Calendar.SECOND, 0);
        return dateStart.getTime();
    }

    /**
     * 获取每天的开始时间 23:59:59:999
     */
    public static Date getEndTime(Date date) {
        Calendar dateEnd = Calendar.getInstance();
        dateEnd.setTime(date);
        dateEnd.set(Calendar.HOUR_OF_DAY, 23);
        dateEnd.set(Calendar.MINUTE, 59);
        dateEnd.set(Calendar.SECOND, 59);
        return dateEnd.getTime();
    }

}
