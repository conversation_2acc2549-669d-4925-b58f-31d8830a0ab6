package com.dl.aiservice.share.voiceclone;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-06 17:06
 */
@Data
public class VoiceTrainResultUpdateNameParamDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 训练名
     */
    @NotBlank(message = "训练名不能为空")
    private String name;

    /**
     * 厂商，3-深声科技（线上训练），6-火山引擎
     */
    @NotNull(message = "厂商不能为空")
    private Integer channel;

    /**
     * 厂商训练模型编号
     */
    @NotBlank(message = "厂商训练模型编号不能为空")
    private String extModelCode;

    /**
     * 训练类型：0 数字人训练；1 声音训练
     *
     * @See:com.dl.aiservice.biz.manager.train.enums.TrainTypeEnum
     */
    @NotNull(message = "训练类型不能为空")
    private Integer trainType;

}
