package com.dl.aiservice.web.controller.voiceclone.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeepSoundTrainCallbackParam implements AbstractTrainCallbackParam {

    @ApiModelProperty(value = "训练失败时的错误码；0=成功")
    @JsonProperty(value = "error_code")
    Integer errorCode = 0;

    @ApiModelProperty("错误的详细信息")
    String msg;

    @ApiModelProperty(value = "音色的音质")
    String quality;

    @ApiModelProperty("声纹训练结果 - 音色名")
    @JsonProperty(value = "voice_name")
    String voiceName;

    @ApiModelProperty("当前提交音色的唯一编号")
    @JsonProperty(value = "record_id")
    String recordId;

    @ApiModelProperty("服务内部对当前提交音色的唯一编号")
    @JsonProperty(value = "business_id")
    String businessId;

    @ApiModelProperty("训练状态：1 训练中；0 训练完成；-1 训练失败")
    Integer status;

}
