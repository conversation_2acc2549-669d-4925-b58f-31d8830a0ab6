package com.dl.aiservice.biz.client.callback;

import com.dl.aiservice.share.videoproduce.dto.aliyun.ProduceMediaCompleteParamDTO;
import com.dl.framework.common.model.ResultModel;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;

@BaseRequest
public interface AliyunVideoProduceCallBackClient {
    @Post("{callbackUrl}")
    ResultModel callback(@Var("callbackUrl") String callbackUrl, @JSONBody ProduceMediaCompleteParamDTO paramDTO);
}
