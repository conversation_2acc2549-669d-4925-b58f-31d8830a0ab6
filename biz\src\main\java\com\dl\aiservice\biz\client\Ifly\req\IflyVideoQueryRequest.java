package com.dl.aiservice.biz.client.Ifly.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IflyVideoQueryRequest {

    /**
     * 本次创建的任务id，⽤于唯⼀标识本次任务
     */
    @JsonProperty(value = "task_id")
    private String taskId;
}
