package com.dl.aiservice.biz.client.guiji.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@NoArgsConstructor
@Data
public class GjCreateTrainingRequest implements Serializable {
    private static final long serialVersionUID = -240723204685689772L;
    /**
     * 训练任务ID，id为空表示提交新训练，id不为空表示更新训练
     */
    private Integer id;
    /**
     * 数字人名称
     */
    private String name;
    /**
     * 训练视频url，需公网可访问
     */
    private String videoUrl;
    /**
     * 训练状态回调地址
     */
    private String callbackUrl;


}