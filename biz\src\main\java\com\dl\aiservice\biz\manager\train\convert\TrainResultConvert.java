package com.dl.aiservice.biz.manager.train.convert;

import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.dal.po.TrainResultPO;
import com.dl.aiservice.biz.manager.train.bo.TrainResultBaseInfoSaveBO;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-28 19:07
 */
public class TrainResultConvert {

    public static TrainResultPO cnvTrainResultBaseInfoSaveBO2PO(TrainResultBaseInfoSaveBO input) {
        TrainResultPO result = new TrainResultPO();
        result.setName(input.getName());
        result.setExtModelCode(input.getExtModelCode());
        result.setChannel(input.getChannel());
        result.setTenantCode(input.getTenantCode());
        result.setTrainType(input.getTrainType());
        result.setStatus(input.getStatus());
        result.setTrainedNum(input.getTrainedNum());
        result.setIsDeleted(Const.ZERO);
        result.setCreateBy(Const.ZERO_LONG);
        result.setModifyBy(Const.ZERO_LONG);
        result.setCreateDt(new Date());
        result.setModifyDt(new Date());
        return result;
    }

}
