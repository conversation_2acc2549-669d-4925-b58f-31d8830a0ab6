package com.dl.aiservice.biz.manager.digitalasset.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.dal.mapper.DaVirtualVoiceMapper;
import com.dl.aiservice.biz.dal.po.DaTenantAuthPO;
import com.dl.aiservice.biz.dal.po.DaVirtualVoicePO;
import com.dl.aiservice.biz.manager.digitalasset.DaTenantAuthManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualVoiceManager;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoiceListQueryPairBO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoicePageBO;
import com.dl.aiservice.biz.manager.digitalasset.conf.DaOldNewBizIdConfig;
import com.dl.aiservice.biz.manager.digitalasset.enums.DaTenantAuthBizTypeEnum;
import com.dl.aiservice.biz.manager.voiceclone.enums.VoiceCloneEnum;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceChannelDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【da_virtual_voice(数字资产-数字声音信息表)】的数据库操作Service实现
 * @createDate 2023-06-02 13:53:14
 */
@Service
public class DaVirtualVoiceManagerImpl extends ServiceImpl<DaVirtualVoiceMapper, DaVirtualVoicePO>
        implements DaVirtualVoiceManager {

    @Resource
    private DaTenantAuthManager daTenantAuthManager;

    @Resource
    private DaOldNewBizIdConfig daOldNewBizIdConfig;

    @Override
    public DaVirtualVoicePO info(Long bizId) {
        //优先判断该bizId是否有对应的新bizId，若有则查询新bizId的数据
        if (daOldNewBizIdConfig.getVoiceOldNewBizIdMap().containsKey(bizId)) {
            bizId = daOldNewBizIdConfig.getVoiceOldNewBizIdMap().get(bizId);
        }
        return this.getOne(Wrappers.lambdaQuery(DaVirtualVoicePO.class).eq(DaVirtualVoicePO::getBizId, bizId)
                .eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO));
    }

    @Override
    public DaVirtualVoicePO infoByChannelVoiceKey(Integer channel, String voiceKey) {

        return this.getOne(Wrappers.lambdaQuery(DaVirtualVoicePO.class).eq(DaVirtualVoicePO::getChannel, channel)
                .eq(DaVirtualVoicePO::getVoiceKey, voiceKey).eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO));
    }

    @Override
    public List<DaVirtualVoicePO> listVoice(String tenantCode, Integer voiceType, List<Integer> channels) {
        List<DaTenantAuthPO> authVoiceList = daTenantAuthManager
                .list(Wrappers.lambdaQuery(DaTenantAuthPO.class).eq(DaTenantAuthPO::getTenantCode, tenantCode)
                        .eq(DaTenantAuthPO::getBizType, DaTenantAuthBizTypeEnum.VOICE.getType())
                        .eq(DaTenantAuthPO::getIsDeleted, Const.ZERO));
        if (CollectionUtils.isEmpty(authVoiceList)) {
            return Collections.emptyList();
        }
        return this.list(Wrappers.lambdaQuery(DaVirtualVoicePO.class).in(DaVirtualVoicePO::getBizId,
                authVoiceList.stream().map(DaTenantAuthPO::getBizId).collect(Collectors.toSet()))
                .eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO).eq(DaVirtualVoicePO::getIsEnabled, Const.ONE)
                .in(CollectionUtils.isNotEmpty(channels), DaVirtualVoicePO::getChannel, channels)
                .eq(Objects.nonNull(voiceType), DaVirtualVoicePO::getVoiceType, voiceType)
                .orderByAsc(DaVirtualVoicePO::getId));
    }

    @Override
    public IPage<DaVirtualVoicePO> pageVoice(DaVirtualVoicePageBO queryBO) {
        return getBaseMapper().pageVoice(queryBO);
    }

    @Override
    public List<DaVirtualVoiceChannelDTO> getVoiceChannel() {
        List<VoiceCloneEnum> list = Arrays.asList(VoiceCloneEnum.values());
        return list.stream().map(e -> {
            DaVirtualVoiceChannelDTO bo = new DaVirtualVoiceChannelDTO();
            bo.setChannel(e.getCode());
            bo.setDesc(e.getCnDesc());
            return bo;
        }).sorted(Comparator.comparing(DaVirtualVoiceChannelDTO::getDesc)).collect(Collectors.toList());
    }

    @Override
    public void voiceAuth(Long bizId, List<String> authTenantCodeList) {
        Assert.isTrue(CollectionUtils.isNotEmpty(authTenantCodeList), "授权租户列表不能为空");
        //授权
        DaVirtualVoicePO voicePO = this.lambdaQuery().eq(DaVirtualVoicePO::getBizId, bizId).one();
        Assert.notNull(voicePO, "声音不存在");

        List<DaTenantAuthPO> existTenantAuthPOList = daTenantAuthManager
                .list(Wrappers.lambdaQuery(DaTenantAuthPO.class).eq(DaTenantAuthPO::getBizId, bizId)
                        .eq(DaTenantAuthPO::getBizType, DaTenantAuthBizTypeEnum.VOICE.getType())
                        .eq(DaTenantAuthPO::getIsDeleted, Const.ZERO));
        Map<String, DaTenantAuthPO> tenantAuthMap = existTenantAuthPOList.stream()
                .collect(Collectors.toMap(DaTenantAuthPO::getTenantCode, Function.identity(), (a, b) -> b));

        // 分为 授权、取消授权 两种逻辑
        List<String> needAuthTenantCodeList = Lists.newArrayList();
        List<DaTenantAuthPO> needUpdateList = Lists.newArrayList();
        authTenantCodeList.stream().forEach(x -> {
            if (!tenantAuthMap.containsKey(x)) {
                needAuthTenantCodeList.add(x);
            }
        });
        tenantAuthMap.entrySet().stream().forEach(entry -> {
            DaTenantAuthPO value = entry.getValue();
            if (!authTenantCodeList.contains(entry.getKey())) {
                // 当前租户不在需要授权的名单内
                if (Objects.equals(Const.ZERO, value.getIsDeleted())) {
                    value.setIsDeleted(Const.ONE);
                    needUpdateList.add(value);
                }
            }
        });
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            daTenantAuthManager.updateBatchById(needUpdateList.stream().map(x -> {
                DaTenantAuthPO updatePO = new DaTenantAuthPO();
                updatePO.setIsDeleted(x.getIsDeleted());
                updatePO.setId(x.getId());
                return updatePO;
            }).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(needAuthTenantCodeList)) {
            return;
        }

        List<DaTenantAuthPO> needAuthPOList = needAuthTenantCodeList.stream().map(x -> {
            DaTenantAuthPO tenantAuthPO = new DaTenantAuthPO();
            tenantAuthPO.setTenantCode(x);
            tenantAuthPO.setBizId(bizId);
            tenantAuthPO.setIsDeleted(Const.ZERO);
            tenantAuthPO.setBizType(DaTenantAuthBizTypeEnum.VOICE.getType());
            return tenantAuthPO;
        }).collect(Collectors.toList());
        daTenantAuthManager.saveBatch(needAuthPOList);
    }

    @Override
    public List<DaVirtualVoicePO> listByChannelAndVoiceKeyList(List<DaVirtualVoiceListQueryPairBO> pairList) {
        if (CollectionUtils.isEmpty(pairList)) {
            return Collections.emptyList();
        }

        return getBaseMapper().listByChannelAndVoiceKeyList(pairList);
    }

}




