package com.dl.aiservice.biz.client.kimi;

import com.dl.aiservice.biz.client.kimi.interceptor.KimiInterceptor;
import com.dl.aiservice.biz.client.kimi.interceptor.KimiUploadFileInterceptor;
import com.dl.aiservice.biz.client.kimi.req.ChatCompletionRequest;
import com.dl.aiservice.biz.client.kimi.resp.ChatCompletionResponse;
import com.dl.aiservice.biz.client.kimi.resp.EstimateTokenCountResponse;
import com.dl.aiservice.biz.client.kimi.resp.FileContentResponse;
import com.dl.aiservice.biz.client.kimi.resp.FileDeleteResponse;
import com.dl.aiservice.biz.client.kimi.resp.FileUploadResponse;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.DataFile;
import com.dtflys.forest.annotation.Delete;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;

import java.io.File;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 11:21
 */
@BaseRequest(baseURL = "https://api.moonshot.cn/v1", interceptor = KimiInterceptor.class)
public interface KimiClient {

    String FILE_UPLOAD_URL = "/files";

    /**
     * 对话
     *
     * @param request
     * @return
     */
    @Post(url = "/chat/completions", connectTimeout = 80000, readTimeout = 80000)
    ChatCompletionResponse chatCompletion(@JSONBody ChatCompletionRequest request);

    /**
     * 流式对话
     *
     * @param request
     * @return
     */
    @Post(url = "/chat/completions", connectTimeout = 80000, readTimeout = 80000)
    InputStream chatCompletionStream(@JSONBody ChatCompletionRequest request);

    /**
     * 上传文件
     *
     * @param file
     * @param purpose
     * @return
     */
    @Post(url = "/files",
            contentType = "multipart/form-data",
            connectTimeout = 30000,
            readTimeout = 30000,
            interceptor = KimiUploadFileInterceptor.class)
    FileUploadResponse uploadAiFiles(@DataFile("file") File file, @Body("purpose") String purpose);

    /**
     * 根据文件id查看文件内容
     *
     * @param fileId
     * @return
     */
    @Get(url = "/files/{fileId}/content")
    FileContentResponse fileContent(@Var("fileId") String fileId);

    /**
     * 删除文件
     *
     * @param fileId
     * @return
     */
    @Delete(url = "/files/{fileId}")
    FileDeleteResponse fileDelete(@Var("fileId") String fileId);

    /**
     * 预估耗费token数量
     *
     * @param request
     * @return
     */
    @Post("/tokenizers/estimate-token-count")
    EstimateTokenCountResponse estimateTokenCount(@JSONBody ChatCompletionRequest request);
}
