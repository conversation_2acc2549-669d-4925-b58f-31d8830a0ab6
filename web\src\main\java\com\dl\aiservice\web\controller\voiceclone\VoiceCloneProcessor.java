package com.dl.aiservice.web.controller.voiceclone;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.TextUtil;
import com.dl.aiservice.biz.config.SubtitleConfig;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.dal.po.TrainResultPO;
import com.dl.aiservice.biz.manager.subtitle.SubtitleManager;
import com.dl.aiservice.biz.manager.train.TrainJobManager;
import com.dl.aiservice.biz.manager.train.TrainResultManager;
import com.dl.aiservice.biz.manager.train.bo.TrainJobPageBO;
import com.dl.aiservice.biz.manager.train.bo.TrainResultBaseInfoSaveBO;
import com.dl.aiservice.biz.manager.train.bo.TrainResultPageBO;
import com.dl.aiservice.biz.manager.train.enums.TrainTypeEnum;
import com.dl.aiservice.biz.manager.voiceclone.VoiceCloneHandlerManager;
import com.dl.aiservice.biz.manager.voiceclone.enums.VoiceCloneEnum;
import com.dl.aiservice.biz.register.VoiceCloneHelper;
import com.dl.aiservice.share.aichat.AiMultiChatMessageDTO;
import com.dl.aiservice.share.aichat.AiMultiChatRequestDTO;
import com.dl.aiservice.share.aichat.consts.AiChatKimiConst;
import com.dl.aiservice.share.aichat.errorcode.AiMultiChatResponseDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleAndReviseRequestDTO;
import com.dl.aiservice.share.subtitle.dto.RevisedAsrResponseDTO;
import com.dl.aiservice.share.subtitle.dto.RevisedAsrSubtitleDTO;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.aiservice.share.voiceclone.TtsSubtitleDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainJobDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainJobPageQueryDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultPageQueryDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultUpdateNameParamDTO;
import com.dl.aiservice.web.controller.aichat.AiChatProcess;
import com.dl.aiservice.web.controller.voiceclone.convert.VoiceCloneConvert;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName VoiceCloneServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/3/13 9:54
 * @Version 1.0
 **/
@Component
public class VoiceCloneProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(VoiceCloneProcessor.class);

    @Resource
    private VoiceCloneHelper voiceCloneHelper;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private SubtitleManager subtitleManager;
    @Resource
    private TrainResultManager trainResultManager;
    @Resource
    private TrainJobManager trainJobManager;
    @Resource
    private AiChatProcess aiChatProcess;
    @Resource
    private SubtitleConfig subtitleConfig;

    @Value("${dl.asr.channel}")
    private Integer asrChannel;

    private VoiceCloneHandlerManager getHandler() {
        VoiceCloneEnum e = VoiceCloneEnum.getByCode(channelUtil.getChannel());
        Assert.notNull(e, "该渠道暂不支持声纹克隆");
        return voiceCloneHelper.get(ServiceChannelEnum.getByCode(e.getCode()));
    }

    public ResultModel envCheck(String url) {
        return getHandler().envCheck(url);
    }

    public ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language) {
        return getHandler().audioCheck(url, text, language);
    }

    public ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request) {
        return getHandler().audioTrain(request);
    }

    public ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId) {
        return getHandler().queryAudioTrain(recordId);
    }

    public ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO request) {
        //移除所有\n\r
        String ttsText = request.getText().replaceAll("\n", "").replaceAll("\r", "");
        request.setText(ttsText);

        //进行tts
        ResultModel<TTSResponseDTO> resultModel = getHandler().ttsProduce(request);
        if (!resultModel.isSuccess()) {
            return resultModel;
        }
        TTSResponseDTO ttsResponseDTO = resultModel.getDataResult();
        if (Objects.isNull(ttsResponseDTO)) {
            return resultModel;
        }

        //若需要字幕 则生成字幕
        if (Const.ONE.equals(request.getNeedSubtitle())) {
            this.genSubtitlesByAsr(request, ttsResponseDTO);
            //若需要字幕关键词高亮
            if (Const.ONE.equals(request.getSubtitleKeyWordsHighlight())) {
                Long startTimes = System.currentTimeMillis();
                this.handleSubtitlesKeyWordsHighlight(request, ttsResponseDTO);
                Long endTimes = System.currentTimeMillis();
                LOGGER.info("mediaJobId:{},,,处理字幕关键词高亮共耗时:{}", resultModel.getDataResult().getMediaJobId(),
                        endTimes - startTimes);
            }
        }

        return resultModel;
    }

    public ResultPageModel<VoiceTrainResultDTO> pageTrainResult(VoiceTrainResultPageQueryDTO queryDTO) {
        TrainResultPageBO pageBO = new TrainResultPageBO();
        pageBO.setPageIndex(queryDTO.getPageIndex());
        pageBO.setPageSize(queryDTO.getPageSize());
        pageBO.setChannel(queryDTO.getChannel());
        pageBO.setStatus(queryDTO.getStatus());
        pageBO.setExtModelCode(queryDTO.getExtModelCode());
        pageBO.setName(queryDTO.getName());
        pageBO.setTrainType(TrainTypeEnum.TRAIN_VOICE.getType());
        IPage<TrainResultPO> pageResultPO = trainResultManager.page(pageBO);

        ResultPageModel<VoiceTrainResultDTO> resultPage = new ResultPageModel<>();
        resultPage.setDataResult(pageResultPO.getRecords().stream().map(VoiceCloneConvert::cnvTrainResultPO2DTO)
                .collect(Collectors.toList()));
        resultPage.setPageIndex(pageResultPO.getCurrent());
        resultPage.setPageSize(pageResultPO.getSize());
        resultPage.setTotal(pageResultPO.getTotal());
        resultPage.setTotalPage(pageResultPO.getPages());
        return resultPage;
    }

    public ResultModel<Void> updateTrainResultName(VoiceTrainResultUpdateNameParamDTO paramDTO) {
        //查询已有训练结果
        TrainResultPO existPO = trainResultManager
                .getOne(Wrappers.lambdaQuery(TrainResultPO.class).eq(TrainResultPO::getChannel, paramDTO.getChannel())
                        .eq(TrainResultPO::getTrainType, paramDTO.getTrainType())
                        .eq(TrainResultPO::getExtModelCode, paramDTO.getExtModelCode())
                        .eq(TrainResultPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(existPO)) {
            return ResultModel.error("-1", "该训练结果不存在");
        }

        TrainResultBaseInfoSaveBO saveBO = new TrainResultBaseInfoSaveBO();
        saveBO.setName(paramDTO.getName());
        saveBO.setChannel(paramDTO.getChannel());
        saveBO.setTrainType(paramDTO.getTrainType());
        saveBO.setExtModelCode(paramDTO.getExtModelCode());

        trainResultManager.saveTrainResultBaseInfo(saveBO);

        return ResultModel.success(null);
    }

    public ResultPageModel<VoiceTrainJobDTO> pageTrainResultJob(VoiceTrainJobPageQueryDTO queryDTO) {
        TrainJobPageBO pageBO = new TrainJobPageBO();
        pageBO.setChannel(queryDTO.getChannel());
        pageBO.setExtModelCode(queryDTO.getExtModelCode());
        pageBO.setTrainType(queryDTO.getTrainType());
        pageBO.setPageIndex(queryDTO.getPageIndex());
        pageBO.setPageSize(queryDTO.getPageSize());

        IPage<TrainJobPO> pageResultPO = trainJobManager.page(pageBO);

        ResultPageModel<VoiceTrainJobDTO> resultPage = new ResultPageModel<>();
        resultPage.setDataResult(pageResultPO.getRecords().stream().map(VoiceCloneConvert::cnvTrainJobPO2DTO)
                .collect(Collectors.toList()));
        resultPage.setPageIndex(pageResultPO.getCurrent());
        resultPage.setPageSize(pageResultPO.getSize());
        resultPage.setTotal(pageResultPO.getTotal());
        resultPage.setTotalPage(pageResultPO.getPages());
        return resultPage;
    }

    /**
     * 通过ASR生成字幕
     *
     * @param paramDTO
     * @param ttsResponseDTO
     */
    private void genSubtitlesByAsr(TTSProduceParamDTO paramDTO, TTSResponseDTO ttsResponseDTO) {
        //如果已有字幕，则无需处理
        if (CollectionUtils.isNotEmpty(ttsResponseDTO.getSubtitles())) {
            return;
        }
        LOGGER.info("通过ASR生成字幕paramDTO:{},,,,ttsResponseDTO:{}", JSONUtil.toJsonStr(paramDTO),
                JSONUtil.toJsonStr(ttsResponseDTO));
        AsrSubtitleAndReviseRequestDTO requestDTO = new AsrSubtitleAndReviseRequestDTO();
        requestDTO.setOriginalScript(handleScrpit(paramDTO.getText()));
        requestDTO.setSentenceMaxLength(paramDTO.getMaxLength());
        requestDTO.setAudioUrl(ttsResponseDTO.getAudioUrl());
        requestDTO.setChannel(asrChannel);
        RevisedAsrResponseDTO revisedAsrResponseDTO = null;
        try {
            revisedAsrResponseDTO = subtitleManager.asrAndRevise(requestDTO);
        } catch (Exception e) {
            //异常吃掉，不影响主流程
            LOGGER.error(
                    "对tts音频链接，进行asr生成字幕发生异常!worksBizId:{},,,requestDTO:{},,,mediaJobId:{},,,revisedAsrResponseDTO:{}",
                    paramDTO.getWorksBizId(), JSONUtil.toJsonStr(requestDTO), ttsResponseDTO.getMediaJobId(),
                    JSONUtil.toJsonStr(revisedAsrResponseDTO));
            return;
        }
        List<TtsSubtitleDTO> subtitles = new ArrayList<>();
        for (RevisedAsrSubtitleDTO revisedAsrSubtitleDTO : revisedAsrResponseDTO.getRevisedAsrSubtitles()) {
            TtsSubtitleDTO subtitleDTO = new TtsSubtitleDTO();
            subtitleDTO.setText(revisedAsrSubtitleDTO.getRevisedSubtitle());
            subtitleDTO.setBeginTime(revisedAsrSubtitleDTO.getTimePointStart().longValue());
            subtitleDTO.setEndTime(revisedAsrSubtitleDTO.getTimePointEnd().longValue());
            subtitles.add(subtitleDTO);
        }
        ttsResponseDTO.setSubtitles(subtitles);
    }

    /**
     * 处理字幕中的关键词高亮
     *
     * @param paramDTO
     * @param ttsResponseDTO
     */
    private void handleSubtitlesKeyWordsHighlight(TTSProduceParamDTO paramDTO, TTSResponseDTO ttsResponseDTO) {
        List<TtsSubtitleDTO> subtitles = ttsResponseDTO.getSubtitles();
        if (CollectionUtils.isEmpty(subtitles)) {
            LOGGER.warn("该tts无字幕,ttsResponseDTO:{}", JSONUtil.toJsonStr(ttsResponseDTO));
            return;
        }

        //拼接prompt和字幕
        StringBuffer askContent = new StringBuffer();
        askContent.append(subtitleConfig.getKeywordsHighlightAiPrompt());
        for (int i = 0; i < subtitles.size(); i++) {
            TtsSubtitleDTO subtitleDTO = subtitles.get(i);
            askContent.append(i + 1).append("、").append(subtitleDTO.getText()).append("\\n");
        }
        //askContent.append("\n注意：不要改变原文结构\n");

        //调用aigc接口标记关键词
        AiMultiChatRequestDTO aiMultiChatRequestDTO = new AiMultiChatRequestDTO();
        AiMultiChatMessageDTO chatMessageDTO = new AiMultiChatMessageDTO();
        chatMessageDTO.setText(askContent.toString());
        aiMultiChatRequestDTO.setMessages(Lists.newArrayList(chatMessageDTO));
        aiMultiChatRequestDTO.setModel(AiChatKimiConst.MOONSHOT_8K);
        aiMultiChatRequestDTO.setRespMaxToken(Const.ONE_ZERO_TWO_FOUR);
        ResultModel<AiMultiChatResponseDTO> resultModel = null;
        try {
            resultModel = aiChatProcess.multiChat(aiMultiChatRequestDTO);
        } catch (Exception e) {
            //吃掉异常，不影响主流程
            LOGGER.error(
                    "处理字幕中的关键词高亮异常!worksBizId:{},,,mediaProduceJobId:{},,,paramDTO:{},,,aiMultiChatRequestDTO:{},,,resultModel:{}",
                    paramDTO.getWorksBizId(), ttsResponseDTO.getMediaJobId(), JSONUtil.toJsonStr(paramDTO),
                    JSONUtil.toJsonStr(aiMultiChatRequestDTO), JSONUtil.toJsonStr(resultModel));
            return;
        }
        if (Objects.isNull(resultModel) || Objects.isNull(resultModel.getDataResult()) || StringUtils
                .isBlank(resultModel.getDataResult().getContent())) {
            LOGGER.error(
                    "处理字幕中的关键词高亮返回为空!worksBizId:{},,,mediaProduceJobId:{},,,paramDTO:{},,,aiMultiChatRequestDTO:{},,,resultModel:{}",
                    paramDTO.getWorksBizId(), JSONUtil.toJsonStr(paramDTO), JSONUtil.toJsonStr(aiMultiChatRequestDTO),
                    JSONUtil.toJsonStr(resultModel));
            return;
        }

        AiMultiChatResponseDTO responseDTO = resultModel.getDataResult();

        String[] aiSubtitleArray = responseDTO.getContent().split("[\n,\\n,\\\\n]");
        List<String> aiSubtitleList = new ArrayList<>();
        for (String part : aiSubtitleArray) {
            if (StringUtils.isNotBlank(part)) {
                aiSubtitleList.add(part);
            }
        }

        if (aiSubtitleList.size() != subtitles.size()) {
            LOGGER.error(
                    "ai处理的字幕关键词高亮数组大小和输入的字幕列表大小不一致!mediaProduceJobId:{},,,subtitleDTO.size:{},,,aiSubtitleList.size:{},,,subtitleDTO:{},,,aiResponseDTO.content:{}",
                    ttsResponseDTO.getMediaJobId(), subtitles.size(), aiSubtitleList.size(),
                    JSONUtil.toJsonStr(subtitles), responseDTO.getContent());
            return;
        }
        List<String> aiSubtileList = new ArrayList<>(aiSubtitleList.size());
        try {
            for (int i = 0; i < aiSubtitleList.size(); i++) {
                String aiSubtitle = aiSubtitleList.get(i);
                aiSubtileList.add(aiSubtitle.substring(aiSubtitle.indexOf("、") + 1));
            }
        } catch (Exception e) {
            LOGGER.error("ai处理字幕高亮发生异常!mediaProduceJobId:{},,,subtitleDTO:{},,,aiResponseDTO.content:{},,,e:{}",
                    ttsResponseDTO.getMediaJobId(), JSONUtil.toJsonStr(subtitles), responseDTO.getContent(), e);
            return;
        }
        for (int i = 0; i < subtitles.size(); i++) {
            TtsSubtitleDTO subtitleDTO = subtitles.get(i);
            //原字幕
            String originalSubtitle = subtitleDTO.getText();
            //ai字幕
            String aiSubtitle = aiSubtileList.get(i);
            //比较ai字幕去除<i></i>标签后是否与原字幕相等，若不等则说明ai处理的有问题
            String rmHighlightAiSubtitle = aiSubtitle.replaceAll("<i>", "").replaceAll("</i>", "");
            if (!StringUtils.equals(originalSubtitle, rmHighlightAiSubtitle)) {
                LOGGER.error(
                        "ai处理字幕高亮处理的有问题，与原字幕不符!mediaProduceJobId:{},,,originalSubtitle:{},,,aiSubtitle:{},,,subtitleDTO:{},,,aiResponseDTO.content:{}",
                        ttsResponseDTO.getMediaJobId(), originalSubtitle, aiSubtitle, JSONUtil.toJsonStr(subtitles),
                        responseDTO.getContent());
                return;
            }

            //替换字幕内容
            subtitleDTO.setText(aiSubtitle);
        }
    }

    private String handleScrpit(String originalScript) {
        //移除html标签（移除ssml标签）
        String rmHtmlScript = TextUtil.rmHtml(originalScript);
        //移除所有空格
        String script = rmHtmlScript.replaceAll(" ", "");
        //移除所有\n
        script = script.replaceAll("\n", "");
        //移除所有\r
        script = script.replaceAll("\r", "");
        return script;
    }

    public static void main(String[] args) {
        String a = "需要适当控住仓位为主\\n";
        System.out.println(a.replaceAll("\\n", ""));
        System.out.println(a.replaceAll("\n", ""));
        System.out.println(a.replaceAll("\\\\n", ""));
    }
}
