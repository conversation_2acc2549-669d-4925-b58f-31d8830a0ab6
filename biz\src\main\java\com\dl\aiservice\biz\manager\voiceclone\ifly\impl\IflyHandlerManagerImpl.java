package com.dl.aiservice.biz.manager.voiceclone.ifly.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import com.dl.aiservice.biz.client.Ifly.IflyDigitalClient;
import com.dl.aiservice.biz.client.Ifly.req.IflyBaseRequest;
import com.dl.aiservice.biz.client.Ifly.req.IflyRequestHeader;
import com.dl.aiservice.biz.client.Ifly.req.IflyTtsCreateRequest;
import com.dl.aiservice.biz.client.Ifly.resp.IflyBaseResponse;
import com.dl.aiservice.biz.client.Ifly.resp.IflyTtsCreateResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.MediaUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.manager.voiceclone.ifly.IflyHandlerManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class IflyHandlerManagerImpl implements IflyHandlerManager {

    @Resource
    private IflyDigitalClient iflyDigitalClient;
    @Resource
    private CosFileUploadManager cosFileUploadManager;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;

    @Override
    public ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO request) {

        Assert.notNull(request, "入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getVoiceName()), "voiceName入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getText()), "音频文本不能为空");
        IflyBaseRequest<IflyTtsCreateRequest> iflyRequest = buildTTSRequest(request);
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(hostTimeIdg.generateId().longValue());
        job.setTenantCode(channelUtil.getTenantCode());
        job.setWorksBizId(request.getWorksBizId());
        job.setChannel(channelUtil.getChannel());
        job.setJobType(Const.TWO);
        job.setJobContent(JsonUtils.toJSON(iflyRequest));
        job.setStatus(Const.ONE);
        job.setRequestDt(new Date());
        mediaProduceJobManager.save(job);
        MediaProduceJobPO updateJob = new MediaProduceJobPO();
        updateJob.setId(job.getId());
        try {
            IflyBaseResponse<IflyTtsCreateResponse> resp = iflyDigitalClient.ttsAudio(iflyRequest);
            if (resp.isSuccess() && Objects.nonNull(resp.getData().getUrl())) {
                updateJob.setResponseDt(new Date());
                byte[] bytes = HttpUtil.createGet(resp.getData().getUrl()).execute().bodyBytes();
                File audioFile = getAudioFile(iflyRequest.getBase().getSid(), bytes, StringUtils.isBlank(request.getAudioEncode()) ? "wav" : request.getAudioEncode());
                String audioUrl = cosFileUploadManager.uploadFile(audioFile, null, null);
                TTSResponseDTO result = new TTSResponseDTO();
                result.setMediaJobId(job.getMediaJobId());
                result.setAudioUrl(audioUrl);
                if (StringUtils.isNotBlank(request.getAudioEncode()) && request.getAudioEncode().equals("mp3")) {
                    Double duration = MediaUtil.getAudioDuration(audioFile);
                    result.setDuration(duration);
                    updateJob.setDuration(duration);
                }
                FileUtil.del(audioFile.getAbsolutePath());
                updateJob.setStatus(Const.ZERO);
                updateJob.setMediaUrl(audioUrl);
                updateJob.setExtJobId(iflyRequest.getBase().getSid());
                mediaProduceJobManager.updateById(updateJob);
                return ResultModel.success(result);
            }
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            updateJob.setStatus(-Const.ONE);
            // 失败原因
            updateJob.setFailReason(resp.getMsg());
            updateJob.setExtJobId(iflyRequest.getBase().getSid());
            updateJob.setResponseDt(new Date());
            mediaProduceJobManager.updateById(updateJob);
            return ResultModel.error("-1", "语音合成失败");
        } catch (Exception e) {
            log.error("语音合成失败", e);
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            job.setStatus(-Const.ONE);
            // 失败原因
            job.setFailReason(e.getMessage());
            job.setResponseDt(new Date());
            mediaProduceJobManager.updateById(job);
            return ResultModel.error("-1", "语音合成失败:" + e.getMessage());
        }
    }

    private File getAudioFile(String sessionId, byte[] data, String format) throws Exception {
        // 解码 Base64 数据
        File file = new File(sessionId + "." + format); // 文件路径
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data); // 将字节数组写入文件
        fos.close();
        return file;
    }

    private IflyBaseRequest<IflyTtsCreateRequest> buildTTSRequest(TTSProduceParamDTO request) {
        return IflyBaseRequest.<IflyTtsCreateRequest>builder()
                .base(IflyRequestHeader.builder()
                        .sid(hostTimeIdg.generateId().toString())
                        .build())
                .param(IflyTtsCreateRequest.builder()
                        .format(StringUtils.isNotBlank(request.getAudioFormat()) ? request.getAudioFormat() : "wav")
                        .vcn(request.getVoiceName())
                        .text(request.getText())
                        .spd(StringUtils.isNotBlank(request.getSpeed()) ? Integer.valueOf(request.getSpeed()) : null)
                        .vol(StringUtils.isNotBlank(request.getVolume()) ? Integer.valueOf(request.getVoiceName()) : null)
                        .pitch(StringUtils.isNotBlank(request.getPitch()) ? Integer.valueOf(request.getPitch()) : null)
                        .audioType(0)
                        .build())
                .build();
    }


    @Override
    public ResultModel envCheck(String url) {
        return null;
    }

    @Override
    public ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId) {
        return null;
    }

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.IFLY_TEK);
    }
}