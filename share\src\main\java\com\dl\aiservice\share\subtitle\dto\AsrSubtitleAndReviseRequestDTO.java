package com.dl.aiservice.share.subtitle.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-31 20:09
 */
@Data
public class AsrSubtitleAndReviseRequestDTO implements Serializable {
    private static final long serialVersionUID = 6802097874278576525L;

    @NotBlank(message = "音频url不能为空")
    @ApiModelProperty("音频url")
    private String audioUrl;

    @NotBlank(message = "原字幕不能为空")
    @ApiModelProperty("原字幕")
    private String originalScript;

    @NotNull(message = "ASR渠道不能为空")
    @ApiModelProperty("渠道，2-腾讯云，4-阿里云")
    private Integer channel;

    @ApiModelProperty("每句的最大长度")
    private Integer sentenceMaxLength;
}
