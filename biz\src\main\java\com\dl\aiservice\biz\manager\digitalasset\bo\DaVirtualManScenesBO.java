package com.dl.aiservice.biz.manager.digitalasset.bo;

import lombok.Data;

import java.util.Date;

/**
 * @ClassName DaVirtualManScenesBO
 * @Description
 * <AUTHOR>
 * @Date 2023/6/5 9:07
 * @Version 1.0
 **/
@Data
public class DaVirtualManScenesBO {

    /**
     * 数字人场景主键ID
     */
    private Long id;

    /**
     * 数字人唯一标识
     */
    private Long bizId;

    /**
     * 数字人形象代码
     */
    private String vmCode;

    /**
     * 数字人名称
     */
    private String vmName;

    /**
     * 仿真人自有声音代码
     */
    private String vmVoiceKey;

    /**
     * 关联合成音/克隆音的声音代码
     */
    private Long voiceBizId;

    /**
     * 性别：1 男; 2 女
     */
    private Integer gender;

    /**
     * 数字人头像地址url
     */
    private String headImg;

    /**
     * 数字人手机号
     */
    private String phone;

    /**
     * 数字人邮箱地址
     */
    private String email;

    /**
     * 数字人岗位名称
     */
    private String positionName;

    /**
     * 数智⼈类型：1 2d真⼈;2 3d真⼈
     */
    private Integer vmType;

    /**
     * 生效日期
     */
    private Date effectDt;

    /**
     * 仿真人失效期
     */
    private Date expiryDt;

    /**
     * 是否启用 0：否，1：是
     */
    private Integer isEnabled;

    /**
     * 渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    private Integer channel;

    /**
     * 场景id(重要，视频合成必填ID)
     */
    private String sceneId;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 服装信息：0 个人服饰 1 黑衣服 2 蓝礼服
     */
    private Integer cloth;

    /**
     * 姿态信息：1 坐姿; 2 半身站姿; 3 全身站姿
     */
    private Integer pose;

    /**
     * 分辨率：1 1080x1920; 2 1920x1080
     */
    private Integer resolution;

    /**
     * 场景封面地址
     */
    private String coverUrl;

    /**
     * 场景样例视频地址
     */
    private String exampleUrl;

    /**
     * 场景样例文本
     */
    private String exampleText;

    /**
     * 场景样例时长，毫秒
     */
    private Integer exampleDuration;
}
