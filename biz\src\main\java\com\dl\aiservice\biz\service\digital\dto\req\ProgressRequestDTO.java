package com.dl.aiservice.biz.service.digital.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("视频合成查询进度参数DTO")
public class ProgressRequestDTO implements Serializable {
    private static final long serialVersionUID = 6948618123402771591L;
    /**
     * 第三方任务id
     */
    @ApiModelProperty(value = "第三方任务id")
    private String taskId;

    /**
     * 媒体id 雪花算法
     */
    @ApiModelProperty(value = "媒体id 雪花算法")
    private Long mediaJobId;

    /**
     * 上层业务唯一id
     */
    @ApiModelProperty(value = "通用必填：上层业务唯一id", required = true)
    private Long worksBizId;

    /**
     * 重试次数
     */
    private Integer count;
}

