package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IvhGetSmallSampleAnchorRequest implements Serializable {

    private static final long serialVersionUID = -6693741507864259144L;

    @JsonProperty(value = "PageIndex", defaultValue = "1")
    private Integer pageIndex = 1;

    @JsonProperty(value = "PageSize", defaultValue = "100")
    private Integer pageSize = 100;

}
