package com.dl.aiservice.biz.client.Ifly.resp;

import com.dl.aiservice.biz.client.Ifly.enums.IflyErrCodeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IflyBaseResponse<T> implements Serializable {


    private static final long serialVersionUID = -6023833530442878791L;

    private String code;
    private String msg;

    @JsonProperty(value = "data")
    private T data;

    public boolean isSuccess() {
        return IflyErrCodeEnum.ERROR_CODE_0.getErrorCode().equals(getCode());
    }

}
