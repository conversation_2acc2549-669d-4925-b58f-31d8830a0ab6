package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class IvhSpeedRequest implements Serializable {


    private static final long serialVersionUID = -1301231693693196145L;
    /**
     * 语速（1.0为正常语速，范围[0.5-1.5]，值为0.5时播报语速最慢，值为1.5时播报语速最快，DriverType为⾳频驱动类型时，语速控制不⽣效）
     */
    @JsonProperty(value = "Speed")
    private Double speed;
    /**
     * ⾳⾊key，默认使⽤形象⾃有⾳⾊
     */
    @JsonProperty(value = "TimbreKey")
    private String timbreKey;

}
