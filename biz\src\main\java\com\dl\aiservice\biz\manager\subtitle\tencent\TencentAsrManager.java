package com.dl.aiservice.biz.manager.subtitle.tencent;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.DownloadUtil;
import com.dl.aiservice.biz.manager.subtitle.tencent.convert.TencentAsrConvert;
import com.dl.aiservice.biz.manager.subtitle.tencent.enums.TencentAsrFormatEnum;
import com.dl.aiservice.biz.manager.subtitle.utils.AsrUtil;
import com.dl.aiservice.biz.properties.cos.ApiProperties;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleDTO;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.tencent.SpeechClient;
import com.tencent.asr.model.Credential;
import com.tencent.asr.model.FlashRecognitionRequest;
import com.tencent.asr.model.FlashRecognitionResponse;
import com.tencent.asr.service.FlashRecognizer;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-08-29 17:18
 */
@Component
public class TencentAsrManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(TencentAsrManager.class);

    @Resource
    private ApiProperties apiProperties;

    @Value("${dl.fileTempPath}")
    public String localPathPrefix;

    @Value("${dl.tencentcloud.asr.customizationId}")
    private String customizationId;

    /**
     * 极速asr文件
     *
     * @param multipartFile
     * @return
     */
    public List<AsrSubtitleDTO> flashAsrFile(MultipartFile multipartFile, Integer sentenceMaxLength) {
        Credential credential = Credential.builder().secretId(apiProperties.getSecretId())
                .secretKey(apiProperties.getSecretKey()).build();
        FlashRecognizer recognizer = SpeechClient.newFlashRecognizer(apiProperties.getAppId(), credential);

        if (Objects.nonNull(sentenceMaxLength)) {
            Assert.isTrue(sentenceMaxLength >= 6 && sentenceMaxLength <= 40, "每句最多展示字数，取值范围：[6，40]");
        }

        String suffix = AsrUtil.extractAudioSuffix(multipartFile.getOriginalFilename());
        TencentAsrFormatEnum asrFormatEnum = TencentAsrFormatEnum.parse(suffix);
        Assert.notNull(asrFormatEnum, "不支持该音频格式");

        //传入识别语音数据同步获取结果
        FlashRecognitionRequest recognitionRequest = FlashRecognitionRequest.initialize();
        recognitionRequest.setConvertNumMode(1);
        recognitionRequest.setEngineType("16k_zh");
        recognitionRequest.setFirstChannelOnly(1);
        recognitionRequest.setVoiceFormat(asrFormatEnum.getFormat());
        recognitionRequest.setSpeakerDiarization(0);
        recognitionRequest.setFilterDirty(0);
        recognitionRequest.setFilterModal(0);
        recognitionRequest.setFilterPunc(0);
        recognitionRequest.setSentenceMaxLength(sentenceMaxLength);
        if (StringUtils.isNotBlank(customizationId)) {
            recognitionRequest.setCustomizationId(customizationId);
        }

        recognitionRequest.setWordInfo(3);

        FlashRecognitionResponse resp2 = null;
        try {
            resp2 = recognizer.recognize(recognitionRequest, multipartFile.getBytes());
        } catch (Exception e) {
            LOGGER.error("调用腾讯云极速ASR接口发生异常,fileName:{},,, e:{}", multipartFile.getOriginalFilename(), e);
            throw BusinessServiceException.getInstance("调用腾讯云极速ASR接口失败");
        }
        if (Objects.isNull(resp2) || !Const.ZERO.equals(resp2.getCode())) {
            LOGGER.error("调用腾讯云极速ASR接口失败,,,,fileName:{},,, resp:{}", multipartFile.getOriginalFilename(),
                    JSONUtil.toJsonStr(resp2));
            throw BusinessServiceException.getInstance("调用腾讯云极速ASR接口失败");
        }
        LOGGER.info("调用腾讯云极速ASR接口响应,,,fileName:{},,,wordInfo:3 :{}", multipartFile.getOriginalFilename(),
                JSONUtil.toJsonStr(resp2));
        List<AsrSubtitleDTO> asrSubtitleDTOList3 = TencentAsrConvert.buildAsrSubtitleDTOList(resp2);
        LOGGER.info("fileName:{},,,asrSubtitleDTOList3 :{}", multipartFile.getOriginalFilename(),
                JSONUtil.toJsonStr(asrSubtitleDTOList3));

        return asrSubtitleDTOList3;
    }

    /**
     * 极速asr链接
     *
     * @param audioUrl
     * @return
     */
    public List<AsrSubtitleDTO> flashAsr(String audioUrl, Integer sentenceMaxLength) {
        Credential credential = Credential.builder().secretId(apiProperties.getSecretId())
                .secretKey(apiProperties.getSecretKey()).build();
        FlashRecognizer recognizer = SpeechClient.newFlashRecognizer(apiProperties.getAppId(), credential);

        if (Objects.nonNull(sentenceMaxLength)) {
            Assert.isTrue(sentenceMaxLength >= 6 && sentenceMaxLength <= 40, "每句最多展示字数，取值范围：[6，40]");
        }

        String suffix = AsrUtil.extractAudioSuffix(audioUrl);
        TencentAsrFormatEnum asrFormatEnum = TencentAsrFormatEnum.parse(suffix);
        Assert.notNull(asrFormatEnum, "不支持该音频格式");

        File file = null;
        try {
            String decodeUrl = URLDecoder.decode(audioUrl, "UTF-8");
            String obejetKey = DownloadUtil.getCosKeyFromUrlString(decodeUrl);
            //要对obejctKey做下encode，不然一些字符无法识别。比如（）
            String encodeUrl = DownloadUtil.encodeCosKeyFromUrlString(decodeUrl);
            //文件下载
            file = DownloadUtil.downloadFile(encodeUrl, localPathPrefix + obejetKey);
        } catch (Exception e) {
            LOGGER.error("处理音频文件下载出现异常! audioUrl:{}, e:{}", audioUrl, e);
            FileUtils.deleteQuietly(file);
            throw BusinessServiceException.getInstance("处理音频文件下载出现异常,asr解析失败！");
        }

        //传入识别语音数据同步获取结果
        FlashRecognitionRequest recognitionRequest = FlashRecognitionRequest.initialize();
        recognitionRequest.setConvertNumMode(1);
        recognitionRequest.setEngineType("16k_zh");
        recognitionRequest.setFirstChannelOnly(1);
        recognitionRequest.setSpeakerDiarization(0);
        recognitionRequest.setVoiceFormat(asrFormatEnum.getFormat());
        recognitionRequest.setFilterModal(0);
        recognitionRequest.setFilterDirty(0);
        recognitionRequest.setFilterPunc(0);
        recognitionRequest.setSentenceMaxLength(sentenceMaxLength);
        if (StringUtils.isNotBlank(customizationId)) {
            recognitionRequest.setCustomizationId(customizationId);
        }

        recognitionRequest.setWordInfo(3);

        FlashRecognitionResponse resp2 = null;
        try {
            resp2 = recognizer.recognize(recognitionRequest, getBytesFromFile(file));
        } catch (Exception e) {
            LOGGER.error("调用腾讯云极速ASR接口发生异常,audioUrl:{},,, e:{}", audioUrl, e);
            FileUtils.deleteQuietly(file);
            throw BusinessServiceException.getInstance("调用腾讯云极速ASR接口失败");
        }
        if (Objects.isNull(resp2) || !Const.ZERO.equals(resp2.getCode())) {
            LOGGER.error("调用腾讯云极速ASR接口失败,audioUrl:{},,, resp:{}", audioUrl, JSONUtil.toJsonStr(resp2));
            FileUtils.deleteQuietly(file);
            throw BusinessServiceException.getInstance("调用腾讯云极速ASR接口失败");
        }
        LOGGER.info("调用腾讯云极速ASR接口响应,,,audioUrl:{},,, wordInfo:3 :{}", audioUrl, JSONUtil.toJsonStr(resp2));
        List<AsrSubtitleDTO> asrSubtitleDTOList3 = TencentAsrConvert.buildAsrSubtitleDTOList(resp2);
        LOGGER.info("audioUrl:{},,,asrSubtitleDTOList3 :{}", audioUrl, JSONUtil.toJsonStr(asrSubtitleDTOList3));

        FileUtils.deleteQuietly(file);
        return asrSubtitleDTOList3;
    }

    private static byte[] getBytesFromFile(File file) throws IOException {
        FileInputStream fis = new FileInputStream(file);
        byte[] fileBytes = new byte[(int) file.length()]; // 创建一个和文件长度相等的字节数组

        fis.read(fileBytes); // 将文件内容读取到字节数组中
        fis.close();

        return fileBytes;
    }
}
