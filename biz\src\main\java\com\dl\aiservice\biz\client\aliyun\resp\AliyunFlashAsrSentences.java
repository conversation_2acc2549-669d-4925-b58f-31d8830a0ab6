package com.dl.aiservice.biz.client.aliyun.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-31 15:13
 */
@Data
public class AliyunFlashAsrSentences {

    /**
     * 句子级别的识别结果。
     */
    private String text;

    /**
     * 句子中的词
     */
    private List<AliyunFlashAsrWords> words;

    /**
     * 句子的开始时间，单位：毫秒。
     */
    @JsonProperty("begin_time")
    private int beginTime;

    /**
     * 句子的结束时间，单位：毫秒。
     */
    @JsonProperty("end_time")
    private int endTime;

    /**
     * 多个声道的音频文件会区分返回识别结果，声道ID从0计数。
     */
    @JsonProperty("channel_id")
    private int channelId;
}
