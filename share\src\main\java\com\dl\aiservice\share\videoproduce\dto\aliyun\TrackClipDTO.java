package com.dl.aiservice.share.videoproduce.dto.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: TrackElementParam
 * @author: zhousx
 * @date: 2023/2/10 11:17
 */
@Data
public class TrackClipDTO {
    @JsonProperty("Type")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String elementType;

    @JsonProperty("SubType")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String subType;

    @JsonProperty("MediaId")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String mediaId;

    @JsonProperty("X")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double x;

    @JsonProperty("Y")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double y;

    @JsonProperty("Height")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double height;

    @JsonProperty("Width")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double width;

    @JsonProperty("In")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double in;

    @JsonProperty("Out")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double out;

    @JsonProperty("MaxOut")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double maxOut;

    @JsonProperty("Duration")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double duration;

    @JsonProperty("DyncFrames")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer dyncFrames;

    @JsonProperty("TimelineIn")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double timelineIn;

    @JsonProperty("TimelineOut")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double timelineOut;

    @JsonProperty("Speed")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double speed;

    @JsonProperty("Effects")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<EffectDTO> effects;

    @JsonProperty("LoopMode")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer loopMode;

    @JsonProperty("Content")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String content;

    @JsonProperty("Font")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String font;

    @JsonProperty("FontColor")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String fontColor;

    @JsonProperty("FontSize")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer fontSize;

    @JsonProperty("FontColorOpacity")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String fontColorOpacity;

    @JsonProperty("FontFace")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private FontFaceDTO fontFace;

    @JsonProperty("Spacing")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer spacing;

    @JsonProperty("Angle")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double angle;

    @JsonProperty("BorderStyle")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer borderStyle;

    @JsonProperty("Outline")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer outline;

    @JsonProperty("OutlineColour")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String outlineColour;

    @JsonProperty("Shadow")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer shadow;

    @JsonProperty("BackColor")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String backColor;

    @JsonProperty("Alignment")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String alignment;

    @JsonProperty("AdaptMode")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String adaptMode;

    @JsonProperty("TextWidth")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer textWidth;

    @JsonProperty("EffectColorStyle")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String effectColorStyle;

    @JsonProperty("AaiMotionInEffect")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String aaiMotionInEffect;

    @JsonProperty("AaiMotionIn")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double aaiMotionIn;

    @JsonProperty("AaiMotionOut")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double aaiMotionOut;

    @JsonProperty("Ratio")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double ratio;

    @JsonProperty("AaiMotionOutEffect")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String aaiMotionOutEffect;

    @JsonProperty("AaiMotionLoopEffect")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String aaiMotionLoopEffect;

    @JsonProperty("Voice")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String voice;

    @JsonProperty("Format")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String format;

    @JsonProperty("SpeechRate")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer speechRate;

    @JsonProperty("PitchRate")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer pitchRate;

    @JsonProperty("FontUrl")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String fontUrl;
}
