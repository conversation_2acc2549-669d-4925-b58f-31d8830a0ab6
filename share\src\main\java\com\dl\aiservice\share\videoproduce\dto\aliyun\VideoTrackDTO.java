package com.dl.aiservice.share.videoproduce.dto.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: ClipElementParam
 * @author: zhousx
 * @date: 2023/2/8 18:10
 */
@Data
public class VideoTrackDTO {
    @JsonProperty("Type")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String type;

    @JsonProperty("VideoTrackClips")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<TrackClipDTO> videoTrackClips;
}
