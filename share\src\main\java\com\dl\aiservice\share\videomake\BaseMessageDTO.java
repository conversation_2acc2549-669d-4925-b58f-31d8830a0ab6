package com.dl.aiservice.share.videomake;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName BaseMessageDTO
 * @Description
 * <AUTHOR>
 * @Date 2023/6/25 15:46
 * @Version 1.0
 **/
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class BaseMessageDTO {

    @ApiModelProperty(value = "第三方厂商模板唯一标识，指定厂商必填")
    @NotBlank(message = "第三方厂商模板ID必填")
    private String thirdTemplateId;

    @ApiModelProperty(value = "背景音音频url")
    private String bgmUrl;

    @ApiModelProperty(value = "回调url", required = true)
    @NotBlank(message = "回调url必填")
    private String callbackUrl;
}
