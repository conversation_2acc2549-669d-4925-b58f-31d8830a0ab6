package com.dl.aiservice.biz.service.digital;

import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.service.digital.bo.CreateTrainBO;
import com.dl.aiservice.biz.service.digital.bo.CreateTrainUpdateBO;
import com.dl.aiservice.biz.service.digital.bo.CreateVideoBO;
import com.dl.aiservice.biz.service.digital.bo.CreateVideoUpdateBO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;

/**
 * @author: xuebin
 * @description 智能数字人RPC接口
 * @Date: 2023/2/28 17:50
 */
public interface DigitalDBService {


     /**
      * 调用视频合成前接口
      */
     MediaProduceJobPO saveVideoCreate(CreateVideoBO createVideoBO);

     /**
      * 更新操作
      */
     void updateVideoById(CreateVideoUpdateBO createVideoUpdateBO);

     /**
      * 调用训练合成前接口
      */
     TrainJobPO saveTrainCreate(CreateTrainBO createVideoBO);

     /**
      * 更新训练
      */
     void updateTrainById(CreateTrainUpdateBO createVideoUpdateBO);

     /**
      * 根据第三方业务id查询进度
      */
     MediaProduceJobPO getWorksBizId(TaskRequestDTO taskRequestDTO);
}

