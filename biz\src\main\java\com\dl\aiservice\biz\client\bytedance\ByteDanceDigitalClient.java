package com.dl.aiservice.biz.client.bytedance;

import com.dl.aiservice.biz.client.bytedance.interceptor.ByteDanceDigitalInterceptor;
import com.dl.aiservice.biz.client.bytedance.req.ByteDanceDmQueryRequest;
import com.dl.aiservice.biz.client.bytedance.req.ByteDanceDmSubmitRequest;
import com.dl.aiservice.biz.client.bytedance.resp.ByteDanceDmQueryResponse;
import com.dl.aiservice.biz.client.bytedance.resp.ByteDanceDmSubmitResponse;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

/**
 * @describe: ByteDanceDigitalClient
 * @author: zhousx
 * @date: 2023/10/8 14:08
 */
@BaseRequest(baseURL = "https://openspeech.bytedance.com/api/v1/virtual_human/async", interceptor = ByteDanceDigitalInterceptor.class)
public interface ByteDanceDigitalClient {
    @Post(url = "/submit")
    ByteDanceDmSubmitResponse submit(@JSONBody ByteDanceDmSubmitRequest request);

    @Get(url = "/query")
    ByteDanceDmQueryResponse query(@JSONBody ByteDanceDmQueryRequest request);
}
