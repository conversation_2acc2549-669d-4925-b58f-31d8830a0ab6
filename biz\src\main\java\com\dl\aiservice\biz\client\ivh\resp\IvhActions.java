package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class IvhActions {
    /**
     * 数智⼈动作名称
     */
    @JsonProperty(value = "ActionName")
    private String actionName;
    /**
     * 数智⼈动作code，对应ssml动作标签
     */
    @JsonProperty(value = "ActionCode")
    private String actionCode;
    /**
     * 数智⼈动作样例url
     */
    @JsonProperty(value = "ActionSample")
    private String actionSample;


}