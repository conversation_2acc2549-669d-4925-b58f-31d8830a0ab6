package com.dl.aiservice.biz.client.kimi.interceptor;

import com.dl.aiservice.biz.client.kimi.KimiClient;
import com.dl.aiservice.biz.client.kimi.config.KimiConfig;
import com.dl.aiservice.biz.client.kimi.consts.KimiConst;
import com.dl.aiservice.biz.client.kimi.enums.KimiErrorHttpCodeEnum;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.share.aichat.errorcode.AiChatErrorCodeEnum;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 11:35
 */
@Slf4j
public class KimiInterceptor implements Interceptor {

    @Override
    public boolean beforeExecute(ForestRequest request) {
        KimiConfig config = ApplicationContextUtils.getContext().getBean(KimiConfig.class);
        request.addHeader("Authorization", "Bearer " + config.getApikey());
        log.info("KimiInterceptor before execute:\nrequest: {}", request.getBody().nameValuesMapWithObject());
        return Boolean.TRUE;
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        if (isIgnore(request)) {
            return;
        }
        log.info("KimiInterceptor after execute:\nrequest: {},\nhttpStatus:{},\nresponse: {}",
                request.getBody().nameValuesMapWithObject(), response.getStatusCode(), response.getContent());
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        if (isIgnore(request)) {
            return;
        }

        Interceptor.super.onError(ex, request, response);

        //获取请求响应状态码
        int status = response.getStatusCode();
        KimiErrorHttpCodeEnum errorCodeEnum = KimiErrorHttpCodeEnum.parse(status);
        if (Objects.nonNull(errorCodeEnum)) {
            switch (errorCodeEnum) {
            case rate_limit_reached_error:
                throw BusinessServiceException
                        .getInstance(AiChatErrorCodeEnum.OVERLOAD.getCode(), AiChatErrorCodeEnum.OVERLOAD.getMessage());
            case exceeded_current_quota_error:
                throw BusinessServiceException.getInstance(AiChatErrorCodeEnum.INSUFFICIENT_BALANCE.getCode(),
                        AiChatErrorCodeEnum.INSUFFICIENT_BALANCE.getMessage());
            default:
                throw BusinessServiceException.getInstance(errorCodeEnum.getDesc());
            }
        }

        throw BusinessServiceException.getInstance(KimiConst.INTERFACE_ERROR_MSG);
    }

    private boolean isIgnore(ForestRequest request) {
        String url = request.getMethod().getMetaRequest().getUrl();
        if (StringUtils.equals(KimiClient.FILE_UPLOAD_URL, url)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
