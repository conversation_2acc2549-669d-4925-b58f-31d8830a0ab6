package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class IvhSentences {

    /**
     * 拆句得出的句⼦
     */
    @JsonProperty(value = "Sentence")
    public String sentence;
    /**
     * 句⼦中每个字的信息
     */
    @JsonProperty(value = "Words")
    public List<IvhWords> words;

}