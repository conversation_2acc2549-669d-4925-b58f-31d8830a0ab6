package com.dl.aiservice.biz.common.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * @ClassName ApplicationContextUtils
 * @Description
 * <AUTHOR>
 * @Date 2023/3/15 8:56
 * @Version 1.0
 **/
@Component
public class ApplicationContextUtils implements ApplicationContextAware {

    //放置在获取bean的时候提示空指针，将其定义为静态变量
    private static ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationContextUtils.context = applicationContext;
    }

    /**
     * 在这一步的时候一定要注意，此时可调用这个方法的时候
     * context可能为空，会提示空指针异常，需要将其定义成静态的，这样类加载的时候context就已经存在了
     *
     * @param beanName
     * @return
     */
    public static Object getBean(String beanName) {
        return context.getBean(beanName);
    }

    public static ApplicationContext getContext() {
        return context;
    }
}
