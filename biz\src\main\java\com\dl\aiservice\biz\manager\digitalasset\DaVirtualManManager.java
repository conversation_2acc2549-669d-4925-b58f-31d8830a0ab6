package com.dl.aiservice.biz.manager.digitalasset;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.aiservice.biz.dal.po.DaVirtualManPO;
import com.dl.aiservice.share.digitalasset.DaVirtualManDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManPageRequestDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【da_virtual_man(数字资产-仿真人信息表)】的数据库操作Service
 * @createDate 2023-06-02 13:53:14
 */
public interface DaVirtualManManager extends IService<DaVirtualManPO> {

    /**
     * 查询数字人信息
     * 优先判断该bizId是否有对应的新bizId，若有则查询新bizId的数据。
     * 强烈建议所有查询单个数字人信息的地方都走这个接口！！！
     *
     * @param bizId
     * @return
     */
    DaVirtualManPO info(Long bizId);

    /**
     * 分页查询信息
     *
     * @param param
     * @return
     */
    IPage<DaVirtualManDTO> pageVm(DaVirtualManPageRequestDTO param);

    /**
     * 数字人授权租户
     */
    void vmAuth(String vmCode, Integer channel, List<String> authTenantCodeList);
}
