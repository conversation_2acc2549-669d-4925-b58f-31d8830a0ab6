package com.dl.aiservice.biz.client.Ifly.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IflyVideoCreateResponse {

    /**
     * 本次创建的任务id，⽤于唯⼀标识本次任务
     */
    private String taskId;
}
