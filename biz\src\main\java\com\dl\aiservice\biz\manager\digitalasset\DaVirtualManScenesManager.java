package com.dl.aiservice.biz.manager.digitalasset;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.aiservice.biz.dal.po.DaVirtualManSceneQueryPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManScenesPO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManScenesBO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【da_virtual_man_scenes(数字资产-仿真人场景信息表)】的数据库操作Service
 * @createDate 2023-06-02 13:53:14
 */
public interface DaVirtualManScenesManager extends IService<DaVirtualManScenesPO> {

    /**
     * 根据数字人bizId，或者租户编码查询数字人场景列表
     *
     * @param tenantCode
     * @param bizId
     * @return
     */
    List<DaVirtualManScenesBO> listVirtualManScene(String tenantCode, Long bizId, Integer enableFilter);

    /**
     * 根据数字人bizId，或者租户编码查询数字人场景列表
     *
     * @param tenantCode
     * @param bizIdList
     * @return
     */
    List<DaVirtualManScenesBO> listVirtualManSceneByBizIds(String tenantCode, List<Long> bizIdList, Integer enableFilter);

    /**
     * 根据数字人bizId查询数字人场景列表
     * 免租户授权校验
     *
     * @param bizId
     * @return
     */
    List<DaVirtualManScenesBO> listVirtualManSceneWithoutAuthCheck(Long bizId, Integer enableFilter);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    IPage<DaVirtualManScenesBO> pageVirtualMan(DaVirtualManSceneQueryPO query);

    /**
     * 根据数字人场景id列表和渠道列表查询数字人场景列表
     * 免租户授权校验
     *
     * @return
     */
    List<DaVirtualManScenesBO> listVirtualManSceneBySceneIdsWithoutAuthCheck(List<String> sceneIdList,
            List<Integer> channelList, Integer enableFilter);

}
