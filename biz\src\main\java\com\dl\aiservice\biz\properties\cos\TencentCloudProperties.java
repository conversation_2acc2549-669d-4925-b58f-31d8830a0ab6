package com.dl.aiservice.biz.properties.cos;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 腾讯云配置
 */
@ConfigurationProperties(prefix = "dl.tencentcloud")
@Configuration
@Data
public class TencentCloudProperties {

    @Autowired
    private ApiProperties api;

    @Autowired
    private CosProperties cosProperties;

    /**
     * DefaultRegion 默认区域
     */
    private String defaultRegion;

}
