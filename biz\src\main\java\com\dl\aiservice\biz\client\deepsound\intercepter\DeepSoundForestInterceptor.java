package com.dl.aiservice.biz.client.deepsound.intercepter;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.deepsound.DeepSoundClient;
import com.dl.aiservice.biz.client.deepsound.enums.DsAudioCheckErrCodeEnum;
import com.dl.aiservice.biz.client.deepsound.enums.DsEvnCheckErrCodeEnum;
import com.dl.aiservice.biz.client.deepsound.resp.DsAudioCheckResponse;
import com.dl.aiservice.biz.client.deepsound.resp.DsBaseResponse;
import com.dl.aiservice.biz.client.deepsound.resp.DsTtsResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Objects;

@Slf4j
public class DeepSoundForestInterceptor implements Interceptor {

    private static final String X_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
            +
            ".eyJhcHBpZCI6IjAwYTU3MjQ0NjNmZTVlNGM1ODNiNzAxZWU1MjE5YzkzIiwiZXhwIjoiMTcwNzc1MzU5OSIsImlhdCI6IjE2NzYyODAwMDAiLCJpc3MiOiJkZWVwc291bmQuY24iLCJzdWIiOiJ0dHMifQ.MTc3MDNmZmU3YjYzMDYzMTU4ZDAwMjk5Yjk1NmYzNzA5YTE3YTRjNDVlYjA1NDIzYmY4NDExNTJlNDNmYjYzMg";
    private static final String RESP_CODE = "code";
    private static final String RESP_ERR_CODE = "error_code";
    private static final String RESP_MSG = "msg";

    @SneakyThrows
    @Override
    public boolean beforeExecute(ForestRequest request) {
        request.addHeader(DeepSoundClient.APP_ID, DeepSoundClient.X_APPID);
        request.addHeader(DeepSoundClient.APP_KEY, DeepSoundClient.X_APP_KEY);
        request.addHeader(DeepSoundClient.APP_TOKEN, X_TOKEN);
        return Boolean.TRUE;
    }

    /**
     * 成功判断方式
     *
     * @param data
     * @param request
     * @param response
     */
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        JSONObject responseCnt = JSONUtil.parseObj(response.getContent());
        log.info("onSuccess,ForestResp-cnt>>{}", responseCnt);
        if (Objects.nonNull(responseCnt)) {
            // response 响应的content = {"code":"401","msg":"Auth Error 4"}
            String code = responseCnt.getStr(RESP_CODE);
            if (StringUtils.isBlank(code)) {
                code = responseCnt.getStr(RESP_ERR_CODE);
            }
            if (!StringUtils.equals(Const.ZERO_STR, code)) {
                String msg = responseCnt.getStr(RESP_MSG);
                if (data instanceof DsBaseResponse) {
                    DsBaseResponse resp = (DsBaseResponse) data;
                    if (NumberUtils.isNumber(code)) {
                        resp.setCode(Integer.valueOf(code));
                    } else {
                        resp.setCode(DsAudioCheckErrCodeEnum.ERROR_CODE_500000.getErrorCode());
                    }
                    resp.setErrMsg(msg);
                } else {
                    DsTtsResponse ttsRsp = (DsTtsResponse) data;
                    if (NumberUtils.isNumber(code)) {
                        ttsRsp.setErrCode(Integer.valueOf(code));
                    } else {
                        ttsRsp.setErrCode(DsAudioCheckErrCodeEnum.ERROR_CODE_500000.getErrorCode());
                    }
                    ttsRsp.setErrMsg(msg);
                }
            }
        }
        fillErrMsg(data);
        log.info("onSuccess,ForestResp-data>>{}", JSONUtil.toJsonStr(data));
    }

    private void fillErrMsg(Object data) {
        if (!(data instanceof DsBaseResponse)) {
            return;
        }
        DsBaseResponse resp = (DsBaseResponse) data;
        if (resp instanceof DsAudioCheckResponse) {
            DsAudioCheckErrCodeEnum errCodeEnum = DsAudioCheckErrCodeEnum.errorCode(resp.getCode());
            if (errCodeEnum != DsAudioCheckErrCodeEnum.UNKNOWN) {
                resp.setErrMsg(errCodeEnum.getErrorDesc());
            }
            return;
        }
        DsEvnCheckErrCodeEnum errCodeEnum = DsEvnCheckErrCodeEnum.errorCode(resp.getCode());
        if (errCodeEnum != DsEvnCheckErrCodeEnum.UNKNOWN) {
            resp.setErrMsg(errCodeEnum.getErrorDesc());
        }
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        Interceptor.super.onError(ex, request, response);
        if (isIgnore(request)) {
            return;
        }
        JSONObject responseCnt = JSONUtil.parseObj(response.getContent());
        log.info("onError,ForestResp-cnt>>{}", responseCnt);
        Integer code = DsAudioCheckErrCodeEnum.UNKNOWN.getErrorCode();
        if (Objects.nonNull(responseCnt)) {
            code = responseCnt.getInt(RESP_CODE);
        }
        DsEvnCheckErrCodeEnum e = DsEvnCheckErrCodeEnum.errorCode(code);
        if (Objects.nonNull(e)) {
            throw BusinessServiceException.getInstance(e.getErrorCode().toString(), e.getErrorDesc());
        }
        throw BusinessServiceException.getInstance(code.toString(), ex.getMessage());
    }

    private boolean isIgnore(ForestRequest request) {
        String url = request.getMethod().getMetaRequest().getUrl();
        if (StringUtils.equals(DeepSoundClient.TTS_PATH, url)) {
            return Boolean.TRUE;
        }
        String type = request.getMethod().getMetaRequest().getType();
        if (StringUtils.equals(DeepSoundClient.AUDIO_TRAIN_PATH, url) && StringUtils.equals(
                DeepSoundClient.REQUEST_TYPE, type)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
