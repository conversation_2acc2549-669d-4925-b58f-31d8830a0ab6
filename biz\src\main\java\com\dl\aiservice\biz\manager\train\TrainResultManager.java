package com.dl.aiservice.biz.manager.train;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.aiservice.biz.dal.po.TrainResultPO;
import com.dl.aiservice.biz.manager.train.bo.TrainResultBaseInfoSaveBO;
import com.dl.aiservice.biz.manager.train.bo.TrainResultPageBO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-28 15:43
 */
public interface TrainResultManager extends IService<TrainResultPO> {

    /**
     * 保存训练结果基本信息
     *
     * @param saveBO
     * @return
     */
    TrainResultPO saveTrainResultBaseInfo(TrainResultBaseInfoSaveBO saveBO);

    /**
     * 分页查询训练结果
     *
     * @param pageBO
     * @return
     */
    IPage<TrainResultPO> page(TrainResultPageBO pageBO);

}
