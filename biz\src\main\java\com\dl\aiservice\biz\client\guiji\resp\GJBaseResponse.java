package com.dl.aiservice.biz.client.guiji.resp;

import com.dl.aiservice.biz.client.guiji.enums.GjErrCodeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class GJBaseResponse<T> implements Serializable {

    private static final long serialVersionUID = 1961425384297568194L;

    private Integer code;

    private String message;

    private T data;

    public boolean isSuccess() {
        return GjErrCodeEnum.ERROR_CODE_0.getErrorCode().equals(getCode());
    }
}
