package com.dl.aiservice.biz.client.Ifly.intercepter;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.Ifly.enums.IflyErrCodeEnum;
import com.dl.aiservice.biz.client.Ifly.req.IflyBaseRequest;
import com.dl.aiservice.biz.client.Ifly.req.IflyRequestHeader;
import com.dl.aiservice.biz.client.Ifly.resp.IflyBaseResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

@Slf4j
public class IflyDigitalInterceptor implements Interceptor {

    @SneakyThrows
    @Override
    public boolean beforeExecute(ForestRequest request) {
        AiConfig config = ApplicationContextUtils.getContext().getBean(AiConfig.class);
        String apiSecret = config.getIflyApiSecret();
        String appid = config.getIflyAppid();
        IflyBaseRequest requestArgument = (IflyBaseRequest) request.getArgument(Const.ZERO);
        IflyRequestHeader base = requestArgument.getBase();
        base.setAppId(appid);
        base.setTimestamp(System.currentTimeMillis());
        requestArgument.setBase(base);
        String sign = buildSign(JSONUtil.parseObj(requestArgument), apiSecret);
        base.setSign(sign);
        requestArgument.setBase(base);
        request.addBody(JSONUtil.toJsonStr(requestArgument));
        log.info("before execute:\nrequest: {}", JSONUtil.toJsonStr(JSONUtil.toJsonStr(requestArgument)));
        return Boolean.TRUE;
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        throw BusinessServiceException.getInstance(ex.getMessage());
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("after execute:\nrequest: {}\nresponse: {}", JSONUtil.toJsonStr(request.getBody().nameValuesMapWithObject()), response.getContent());
    }

    /**
     * 成功判断方式
     *
     * @param data
     * @param request
     * @param response
     */
    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        fillErrMsg(data);
    }

    private void fillErrMsg(Object data) {
        if (!(data instanceof IflyBaseResponse)) {
            return;
        }
        IflyBaseResponse resp = (IflyBaseResponse) data;
        IflyErrCodeEnum errCodeEnum = IflyErrCodeEnum.errorCode(resp.getCode());
        if (errCodeEnum != IflyErrCodeEnum.ERROR_CODE_0) {
            throw BusinessServiceException.getInstance(errCodeEnum.getErrorCode(), errCodeEnum.getErrorDesc());
        }
    }


    /**
     * 构建签名
     *
     * @param request
     * @param secret
     * @return
     */
    public static String buildSign(JSONObject request, String secret) {
        if (request == null) {
            return null;
        }
        String signStr = buildSignStr(request) + SEPARATOR + secret;
        return DigestUtils.md5Hex(signStr);
    }

    private static final String SEPARATOR = "&";

    /**
     * 构建用于签名的字符串
     *
     * @param request
     * @return
     */
    private static String buildSignStr(JSONObject request) {
        StringBuilder builder = new StringBuilder();
        List<String> keys = new LinkedList<>(request.keySet());
        keys.remove("sign");
        Collections.sort(keys);
        for (String key : keys) {
            Object obj = request.get(key);
            if (obj instanceof JSONObject) {
                if (((JSONObject) obj).size() == 0) {
                    continue;
                }
                builder.append(key).append("=").append(buildSignStr((JSONObject) obj)).append(SEPARATOR);
            } else if (obj instanceof JSONArray) {
                StringBuilder sb = new StringBuilder();
                JSONArray array = (JSONArray) obj;
                array.forEach(json -> sb.append(json instanceof String ? json : buildSignStr((JSONObject) json)).append(","));
                if (sb.length() > 0) {
                    sb.deleteCharAt(sb.length() - 1);
                }
                builder.append(key).append("=").append(sb).append(SEPARATOR);
            } else {
                if (obj instanceof CharSequence && StringUtils.isBlank((CharSequence) obj)) {
                    continue;
                }
                builder.append(key).append("=").append(obj).append(SEPARATOR);
            }
        }
        int lenth = builder.length();
        if (lenth > 0) {
            builder.deleteCharAt(lenth - 1);
        }
        return builder.toString();
    }
}
