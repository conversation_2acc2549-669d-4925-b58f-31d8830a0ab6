package com.dl.aiservice.biz.service.digital.chanjing.impl;

import com.dl.aiservice.biz.properties.cos.ChanjingProperties;
import com.dl.aiservice.biz.service.digital.chanjing.ChanjingDigitalService;
import com.dl.aiservice.biz.service.digital.dto.req.*;
import com.dl.aiservice.biz.service.digital.dto.resp.*;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.model.ResultPageModel;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description：TODO
 * @author： Pelot
 * @create： 2025/9/3 11:19
 */
@Service
public class ChanjingDigitalServiceImpl implements ChanjingDigitalService {
    @Resource
    private ChanjingProperties chanjingProperties;
    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.CHAN_JING);
    }

    @Override
    public CreateResponseDTO videoCreate(CreateRequestDTO gjVideoCreateRequestDTO) {
        return null;
    }

    @Override
    public ResultPageModel<RobotResponseDTO> robotPageList(PageRequestDTO pageRequestDTO) {
        return null;
    }

    @Override
    public RobotDetailResponseDTO robotDetail(RobotDetailRequestDTO requestDTO) {
        return null;
    }

    @Override
    public TrainResponseDTO getTrain(TrainRequestDTO gjTrainRequestDTO) {
        return null;
    }

    @Override
    public ProgressResponseDTO getProgress(TaskRequestDTO taskRequestDTO) {
        return null;
    }

    @Override
    public CreateTrainingResponseDTO createTraining(CreateTrainingRequestDTO request) {
        return null;
    }

    @Override
    public void callBackBiz(String tenantCode, Long worksBizId, String callBackUrl, DigitalVideoCallbackDTO digitalCallbackDTO, String extCallbackRespBody) {

    }
}
