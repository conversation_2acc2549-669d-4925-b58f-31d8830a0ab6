package com.dl.aiservice.biz.client.guiji.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@NoArgsConstructor
@Data
public class GjVideoCreate3DRequest implements Serializable {
    private static final long serialVersionUID = -6727712564242004694L;
    /**
     * 音频地址，需公网可访问
     */
    private String audioUrl;
    /**
     * 场景ID，从模特列表接口获取
     */
    private String sceneId;
    /**
     * 合成作品名称
     */
    private String videoName;
    /**
     * 横向分辨率（默认’1080’）
     */
    private String width;
    /**
     * 纵向分辨率（默认’1920’）
     */
    private String height;
    /**
     * 合成结果回调地址
     */
    private String callbackUrl;
}