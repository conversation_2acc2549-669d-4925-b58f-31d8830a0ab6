package com.dl.aiservice.biz.manager.digitalasset.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-11 14:27
 */
public enum DaTenantAuthBizTypeEnum {

    DIGITAL_MAN(1, "数字人"),
    VOICE(2, "声音");

    private Integer type;

    private String desc;

    DaTenantAuthBizTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
