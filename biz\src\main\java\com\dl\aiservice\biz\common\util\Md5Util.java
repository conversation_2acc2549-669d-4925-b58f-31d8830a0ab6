package com.dl.aiservice.biz.common.util;

import com.dl.aiservice.biz.common.constant.Const;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Encoder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-02 14:44
 */
@Component
@Slf4j
public class Md5Util {

    /**
     * 再使用标准md5算法，计算上述拼接字符串的 md5 值，32 位小写；如： 72cab41e2361c67e2d2 bdadfc7b7fb9b。
     *
     * @param str 加密字段
     * @return
     */
    public static String encrypt(String str) {
        StringBuilder result = new StringBuilder(StringUtils.EMPTY);
        MessageDigest md5;
        try {
            md5 = MessageDigest.getInstance("MD5");
            byte[] bytes = md5.digest(str.getBytes());
            for (byte b : bytes) {
                String temp = Integer.toHexString(b & 0xff);
                if (temp.length() == Const.ONE) {
                    temp = Const.ZERO_STR + temp;
                }
                result.append(temp);
            }
        } catch (NoSuchAlgorithmException e) {
            log.error("", e);
        }
        return result.toString();
    }


    /**
     * HmacSha256 加密
     */
    public static byte[] hmacSHA256(String data, String key) throws Exception {
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        return sha256_HMAC.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 进行 base64 编码。
     */
    public static String base64Encode(byte[] key) {
        //base64	encode
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(key);
    }
}
