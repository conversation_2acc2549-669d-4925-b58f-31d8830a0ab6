package com.dl.aiservice.share.videoproduce.dto.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: Timeline
 * @author: zhousx
 * @date: 2023/2/11 11:22
 */
@Data
public class TimelineDTO {
    @JsonProperty("FECanvas")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private FECanvasDTO fECanvas = new FECanvasDTO();

    @JsonProperty("VideoTracks")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<VideoTrackDTO> videoTracks;

    @JsonProperty("AudioTracks")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<AudioTrackDTO> audioTracks;

    @JsonProperty("ImageTracks")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<ImageTrackDTO> imageTracks;

    @JsonProperty("SubtitleTracks")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SubtitleTrackDTO> subtitleTracks;

    @JsonProperty("EffectTracks")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<EffectTrackDTO> effectTracks;
}
