package com.dl.aiservice.biz.manager.voiceclone.aliyun.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-06-13 09:23
 */
public enum AliyunTtsErrorStatusEnum {

    STATUS_40000000(40000000, "用户使用了不合理的参数或者调用逻辑。"),
    STATUS_40000001(40000001, "设置了错误的发音人名称。"),
    STATUS_40000002(40000002, "无效或者错误的报文消息。"),
    STATUS_40000003(40000003, "用户传递的参数有误，一般常见于RESTful接口调用。"),
    STATUS_40000004(40000004, "请求建立链接后，长时间没有发送任何数据，超过10s后服务端会返回此错误信息。"),
    STATUS_40000005(40000005, "并发请求过多。"),
    STATUS_40000009(40000009, "错误的消息头。"),
    //STATUS_40000009(40000009, "传输的语音WAV头不合法。"),
    STATUS_40000010(40000010, "试用期已结束，并且未开通商用版、或账号欠费。"),
    STATUS_40010001(40010001, "不支持的接口或参数。"),
    STATUS_40010003(40010003, "没有设置有效的待合成文本文字。"),
    STATUS_40010004(40010004, "在请求处理完成前客户端主动结束。"),
    STATUS_40010005(40010005, "客户端发送了当前不支持的消息指令。"),
    STATUS_40020105(40020105, "使用了不存在的Appkey。"),
    STATUS_40020106(40020106, "调用时传递的Appkey和Token并非同一个账号UID所创建，导致不匹配。"),
    STATUS_403(403, "使用的Token无效，例如Token不存在或者已过期"),
    STATUS_41000003(41000003, "无法获取该Appkey的路由信息。"),
    STATUS_41010101(41010101, "不支持的采样率格式。"),
    STATUS_41040201(41040201, "获取客户端发送的数据超时失败。"),
    STATUS_50000000(50000000, "受机器负载、网络等因素导致的异常，通常为偶发出现。"),
    STATUS_50000001(50000001, "受机器负载、网络等因素导致的异常，通常为偶发出现。"),
    STATUS_52010001(52010001, "受机器负载、网络等因素导致的异常，通常为偶发出现。"),
    STATUS_41020001(41020001, "可能有多个错误消息，需根据对应的错误消息调整"),
    STATUS_51020001(51020001, "受机器负载或网络等因素导致的异常，通常为偶发出现。"),
    ;

    private int status;

    private String reason;

    AliyunTtsErrorStatusEnum(int status, String reason) {
        this.status = status;
        this.reason = reason;
    }

    public int getStatus() {
        return status;
    }

    public String getReason() {
        return reason;
    }

    public static final AliyunTtsErrorStatusEnum parse(int status) {
        if (Objects.isNull(status)) {
            return null;
        }
        for (AliyunTtsErrorStatusEnum statusEnmu : AliyunTtsErrorStatusEnum.values()) {
            if (Objects.equals(statusEnmu.status, status)) {
                return statusEnmu;
            }
        }
        return null;
    }
}
