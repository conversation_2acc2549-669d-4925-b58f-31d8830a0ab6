package com.dl.aiservice.biz.manager.subtitle.utils;

import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-08-29 18:04
 */
public class AsrUtil {

    /**
     * 从音频url中提取后缀
     *
     * @param audioUrl
     * @return
     */
    public static String extractAudioSuffix(String audioUrl) {
        int index = StringUtils.lastIndexOf(audioUrl, ".");
        if (index == -1) {
            throw BusinessServiceException.getInstance("音频url格式有误!");
        }
        if (index == audioUrl.length() - 1) {
            throw BusinessServiceException.getInstance("音频url格式有误!");
        }
        return audioUrl.substring(index + 1);
    }
}
