package com.dl.aiservice.web.controller.digital.guiji;

import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.service.digital.dto.req.VideoCreate3DRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.VideoCreate3DTssRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.FreeRobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.SpeakersResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.TrainResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.UserInfoResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.UserResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.VideoCreateResponseDTO;
import com.dl.aiservice.biz.service.digital.guiji.GJDigitalService;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description : 硅基特有接口
 * @date :2022-08-19 13:52:25
 */
@Slf4j
@RestController
@RequestMapping("/digital/guiji")
public class GJDigitalController {

    @Resource
    private GJDigitalService gjDigitalService;
    @Resource
    private ChannelUtil channelUtil;

    @GetMapping("/getAccessToken")
    public String getAccessToken() {
        return gjDigitalService.getAccessToken(channelUtil.getChannel());
    }

    @GetMapping("/allSpeaker")
    public ResultModel<SpeakersResponseDTO> allSpeaker() {
        return ResultModel.success(gjDigitalService.allSpeaker());
    }

    @GetMapping("/robotFreeList")
    public ResultModel<FreeRobotResponseDTO> robotFreeList() {
        return ResultModel.success(gjDigitalService.robotFreeList());
    }

    @PostMapping("/videoPageList")
    public ResultPageModel<ProgressResponseDTO> videoPageList(@RequestBody PageRequestDTO pageRequestDTO) {
        return gjDigitalService.videoPageList(pageRequestDTO);
    }

    @GetMapping("/getUser")
    public ResultModel<UserInfoResponseDTO> getUser() {
        return ResultModel.success(gjDigitalService.getUser());
    }

    @PostMapping("/allUserList")
    public ResultPageModel<UserResponseDTO> allUserList(@RequestBody PageRequestDTO pageRequestDTO) {
        return gjDigitalService.allUserList(pageRequestDTO);
    }

    @PostMapping("/create3D")
    public ResultModel<VideoCreateResponseDTO> create3D(@RequestBody VideoCreate3DRequestDTO VideoRequestDTO) {
        return ResultModel.success(gjDigitalService.create3D(VideoRequestDTO));
    }

    @PostMapping("/create3DTss")
    public ResultModel<VideoCreateResponseDTO> create3DTss(@RequestBody VideoCreate3DTssRequestDTO VideoRequestDTO) {
        return ResultModel.success(gjDigitalService.create3DTss(VideoRequestDTO));
    }


    @PostMapping("/trainPageList")
    public ResultPageModel<TrainResponseDTO> trainPageList(@RequestBody PageRequestDTO pageRequestDTO) {
        return gjDigitalService.trainPageList(pageRequestDTO);
    }
}
