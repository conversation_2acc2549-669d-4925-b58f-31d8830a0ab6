package com.dl.aiservice.biz.service.digital.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class TaskRequestDTO implements Serializable {

    private static final long serialVersionUID = -7489182217568683768L;
    /**
     * 提交的合成任务ID(路径参数)
     * 外部任务id
     */
    @ApiModelProperty(value = "提交的合成任务ID(路径参数)")
    private String taskId;

    /**
     * 上层业务唯一id
     */
    @ApiModelProperty(value = "通用必填：上层业务唯一id", required = true)
    @NotNull(message = "上层业务唯一id不能为空")
    private Long worksBizId;

    /**
     * media_produce_job表media_job_id
     */
    @ApiModelProperty("media_produce_job表media_job_id")
    private Long mediaJobId;

}