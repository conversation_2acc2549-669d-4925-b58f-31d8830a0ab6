package com.dl.aiservice.biz.service.digital.dto.req.guiji;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class GJ<PERSON>reateCallbackDTO implements Serializable {
    private static final long serialVersionUID = -5040616107365918691L;
    /**
     * 提交的任务ID
     */
    private String id;
    /**
     * 视频名称
     */
    private String videoName;
    /**
     * success, fail
     */
    private String result;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 视频格式
     */
    private String videoFormat;
    /**
     * 合成视频的URL
     */
    private String videoUrl;
    /**
     * 水平分辨率
     */
    private Integer horizontal;
    /**
     * 垂直分辨率
     */
    private Integer vertical;
    /**
     * 时长(单位：秒，如11.32)
     */
    private String duration;
    /**
     * 文件尺寸(单位MB，只精确到MB)
     */
    private Integer videoSize;
    /**
     * 封面图地址
     */
    private String coverUrl;
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * mask压缩文件地址, (仅当启用mask时有值)
     */
    private String maskUrl;

}
