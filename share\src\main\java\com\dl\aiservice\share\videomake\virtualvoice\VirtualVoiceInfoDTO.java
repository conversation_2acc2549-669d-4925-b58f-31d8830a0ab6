package com.dl.aiservice.share.videomake.virtualvoice;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName VirtualVoiceInfoDTO
 * @Description
 * <AUTHOR>
 * @Date 2023/6/19 11:07
 * @Version 1.0
 **/
@Data
public class VirtualVoiceInfoDTO implements Serializable {
    private static final long serialVersionUID = 1195371608393222611L;

    @ApiModelProperty(value = "数字声音内部唯一标识")
    private Long voiceBizId;

    @ApiModelProperty(value = "数字声音语速")
    private String speed;

    @ApiModelProperty(value = "数字声音音调")
    private String pitch;

    @ApiModelProperty(value = "数字声音声音大小")
    private String volume;

    /**
     * 情感
     */
    @ApiModelProperty(value = "neutral(中性)、sad(悲伤)、happy(高兴)、angry(生气)、fear(恐惧)、news(新闻)、story(故事)、radio(广播)、poetry"
            + "(诗歌)、call(客服)")
    private String emotionCategory;

    /**
     * 控制合成音频情感程度，取值范围为[50,200],默认为100；只有EmotionCategory不为空时生效
     */
    @ApiModelProperty(value = "控制合成音频情感程度，取值范围为[50,200],默认为100；只有EmotionCategory不为空时生效")
    private Long emotionIntensity;
}
