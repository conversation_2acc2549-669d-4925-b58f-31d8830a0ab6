package com.dl.aiservice.biz.client.Ifly.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class IflyVideoCreateRequest {

    /**
     * 音频地址，有值时，不校验text\vcn等相关参数
     */
    private String audioUrl;
    /**
     * 发音人，例如：xiaopei，audioUrl为空时该值不能为空
     */
    private String vcn;
    /**
     * 语速，取值范围：0-100 默认50
     */
    private Integer spd;
    /**
     * 音量，取值范围：0-100 默认100
     */
    private Integer vol;
    /**
     * 视频类型  0 动画  1真人 默认为0
     */
    private Integer type;
    /**
     * 视频格式  mp4、avi、webm  默认mp4
     */
    private String format;
    /**
     * 输入文本，utf8编码，最大支持5000
     */
    private String text;
    /**
     * 虚拟主播位置 0代表中间 例如-100 ，100    默认0
     */
    private String location;
    /**
     * 虚拟主播大小，取值0.1~1.0
     */
    private Double scale;
    /**
     * 虚拟主播id，需要服务方提供
     */
    private Integer anchorId;
    /**
     * 背景url 默认绿幕
     */
    private String bgUrl;
    /**
     * 背景类型 0 图片 1 视频 默认0
     */
    private Integer bgType;
    /**
     * 是否需要字幕 0 不需要  1 需要 默认0
     */
    private Integer subtitles;
    /**
     * 视频宽度，数值需要为4的倍数，支持分辨率有width:height
     * 16:9(1920:1080,1280:720,856:480)
     * 9:16(1080:1920,720:1280,480:856)
     * 4:3(1440:1080, 960:720,640:480)
     * 3:4(1080:1440,720:960,480:640)
     * 2:3(1080:1620,720:1080,480:720)
     */
    private String width;
    /**
     * 视频高度，数值需要为4的倍数
     */
    private String height;
    /**
     * 0:标准码率  1:高码率  默认为0
     */
    private Integer quality;
    /**
     * 回调地址 没有地址则不进行回调
     */
    private String callbackUrl;
    /**
     * 背景音url 支持mp3 wav
     */
    private String BgAudioUrl;
    /**
     * 背景音量  0-1
     */
    private String BgAudioVol;
    /**
     * 是否为多语种文本（配置text多语种标签用） 默认0
     */
    private Integer lanType;
    /**
     * 视频主播形象 默认0   100 向上偏移200像素
     */
    private String vertical;
    /**
     * 形象裁剪参数 [0,0,1920,1080] 配置location、vertical使用 可以实现形象的任意拖动
     */
    private String mask;

}
