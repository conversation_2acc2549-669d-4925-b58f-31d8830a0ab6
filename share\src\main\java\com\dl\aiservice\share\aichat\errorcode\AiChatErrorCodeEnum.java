package com.dl.aiservice.share.aichat.errorcode;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-26 16:00
 */
public enum AiChatErrorCodeEnum {
    CONTENT_LENGTH_TOO_LONG("AI_CHAT_001", "内容过长，请稍作删减"),
    OVERLOAD("AI_CHAT_002", "当前请求过多，请稍后再试"),
    INSUFFICIENT_BALANCE("AI_CHAT_003", "账号余额不足，请联系管理员充值");

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    AiChatErrorCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static AiChatErrorCodeEnum parse(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (AiChatErrorCodeEnum errorCodeEnum : AiChatErrorCodeEnum.values()) {
            if (StringUtils.equals(errorCodeEnum.getCode(), code)) {
                return errorCodeEnum;
            }
        }
        return null;
    }
}
