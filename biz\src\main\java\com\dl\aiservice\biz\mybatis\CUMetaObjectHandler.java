package com.dl.aiservice.biz.mybatis;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.function.Supplier;

/**
 * 自定义的创建时间、人员和更新时间、人员的mybatisplus赋值过滤器
 * 强制赋值
 */
@Slf4j
@Component
public class CUMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        Date now = new Date();
        fillValue(metaObject, "createDt", () -> now);
        fillValue(metaObject, "modifyDt", () -> now);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Date now = new Date();
        fillValue(metaObject, "modifyDt", () -> now, true);
    }

    private void fillValue(MetaObject metaObject, String fieldName, Supplier<Object> valueSupplier) {
        fillValue(metaObject, fieldName, valueSupplier, false);
    }

    private void fillValue(MetaObject metaObject, String fieldName, Supplier<Object> valueSupplier, boolean override) {
        if (!metaObject.hasGetter(fieldName)) {
            return;
        }
        Object sidObj = metaObject.getValue(fieldName);
        if ((override || sidObj == null) && metaObject.hasSetter(fieldName) && valueSupplier != null) {
            setFieldValByName(fieldName, valueSupplier.get(), metaObject);
        }
    }

}
