package com.dl.aiservice.biz.mq.delay;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.mq.AiChannels;
import com.dl.aiservice.biz.mq.enums.DelayLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 改进的延迟消息服务 - 基于TaskScheduler
 * 相比Thread.sleep方案，这个方案更高效，不会阻塞线程
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Slf4j
@Service
public class ImprovedDelayedMessageService {

    @Autowired
    private AiChannels aiChannels;
    
    @Autowired
    private TaskScheduler taskScheduler;

    /**
     * 发送延迟消息到数字人进度队列
     * 
     * @param payload 消息载荷
     * @param delayLevel 延迟级别
     * @return 调度任务的Future对象，可用于取消任务
     */
    public ScheduledFuture<?> sendDelayedDigitalProgressMessage(Object payload, DelayLevelEnum delayLevel) {
        long delayMillis = calculateDelayMillis(delayLevel);
        Instant executeTime = Instant.now().plusMillis(delayMillis);
        
        return taskScheduler.schedule(() -> {
            try {
                Message message = MessageBuilder.withPayload(payload)
                        .setHeader(KafkaHeaders.TOPIC, "digital-progress-topic")
                        .setHeader("delayLevel", delayLevel.getValue())
                        .setHeader("scheduledTime", System.currentTimeMillis())
                        .build();
                        
                boolean sendResult = aiChannels.digitalprogressproducer().send(message, 1000L);
                log.info("延迟发送数字人查询进度请求的消息,delayLevel:{},actualDelay:{}ms,payload:{},sendResult:{}", 
                        delayLevel, delayMillis, JSONUtil.toJsonStr(payload), sendResult);
                        
            } catch (Exception e) {
                log.error("延迟消息发送失败,delayLevel:{},payload:{}", delayLevel, JSONUtil.toJsonStr(payload), e);
            }
        }, executeTime);
    }

    /**
     * 发送延迟消息到声音训练进度队列
     * 
     * @param payload 消息载荷
     * @param delayLevel 延迟级别
     * @return 调度任务的Future对象，可用于取消任务
     */
    public ScheduledFuture<?> sendDelayedVoiceTrainProgressMessage(Object payload, DelayLevelEnum delayLevel) {
        long delayMillis = calculateDelayMillis(delayLevel);
        Instant executeTime = Instant.now().plusMillis(delayMillis);
        
        return taskScheduler.schedule(() -> {
            try {
                Message message = MessageBuilder.withPayload(payload)
                        .setHeader(KafkaHeaders.TOPIC, "voice-train-progress-topic")
                        .setHeader("delayLevel", delayLevel.getValue())
                        .setHeader("scheduledTime", System.currentTimeMillis())
                        .build();
                        
                boolean sendResult = aiChannels.voicetrainprogressproducer().send(message, 1000L);
                log.info("延迟发送声音训练查询进度请求的消息,delayLevel:{},actualDelay:{}ms,payload:{},sendResult:{}", 
                        delayLevel, delayMillis, JSONUtil.toJsonStr(payload), sendResult);
                        
            } catch (Exception e) {
                log.error("延迟消息发送失败,delayLevel:{},payload:{}", delayLevel, JSONUtil.toJsonStr(payload), e);
            }
        }, executeTime);
    }

    /**
     * 根据延迟级别计算延迟时间（毫秒）
     * 
     * @param delayLevel 延迟级别
     * @return 延迟时间（毫秒）
     */
    private long calculateDelayMillis(DelayLevelEnum delayLevel) {
        // RocketMQ延迟级别对应的时间：1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
        switch (delayLevel) {
            case ONE_SECEND:
                return TimeUnit.SECONDS.toMillis(1);
            case FIVE_SECEND:
                return TimeUnit.SECONDS.toMillis(5);
            case TEN_SECEND:
                return TimeUnit.SECONDS.toMillis(10);
            case THIRTY_SECEND:
                return TimeUnit.SECONDS.toMillis(30);
            case ONE_MINUTE:
                return TimeUnit.MINUTES.toMillis(1);
            case TWO_MINUTE:
                return TimeUnit.MINUTES.toMillis(2);
            case TRHEE_MINUTE:
                return TimeUnit.MINUTES.toMillis(3);
            case FOUR_MINUTE:
                return TimeUnit.MINUTES.toMillis(4);
            case FIVE_MINUTE:
                return TimeUnit.MINUTES.toMillis(5);
            case SIX_MINUTE:
                return TimeUnit.MINUTES.toMillis(6);
            case SEVEN_MINUTE:
                return TimeUnit.MINUTES.toMillis(7);
            case EIGHT_MINUTE:
                return TimeUnit.MINUTES.toMillis(8);
            case NINE_MINUTE:
                return TimeUnit.MINUTES.toMillis(9);
            case TEN_MINUTE:
                return TimeUnit.MINUTES.toMillis(10);
            case TWENTY_MINUTE:
                return TimeUnit.MINUTES.toMillis(20);
            case THIRTY_MINUTE:
                return TimeUnit.MINUTES.toMillis(30);
            case ONE_HOUR:
                return TimeUnit.HOURS.toMillis(1);
            case TWO_HOUR:
                return TimeUnit.HOURS.toMillis(2);
            default:
                return TimeUnit.SECONDS.toMillis(5); // 默认5秒
        }
    }
}
