package com.dl.aiservice.biz.manager.train;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.manager.train.bo.TrainJobAddBO;
import com.dl.aiservice.biz.manager.train.bo.TrainJobPageBO;

/**
 * <AUTHOR>
 * @description 针对表【train_job(训练任务表)】的数据库操作Service
 * @createDate 2023-03-10 17:01:41
 */
public interface TrainJobManager extends IService<TrainJobPO> {

    /**
     * 添加任务
     *
     * @param bo
     * @return
     */
    TrainJobPO addJob(TrainJobAddBO bo);

    IPage<TrainJobPO> page(TrainJobPageBO bo);

}
