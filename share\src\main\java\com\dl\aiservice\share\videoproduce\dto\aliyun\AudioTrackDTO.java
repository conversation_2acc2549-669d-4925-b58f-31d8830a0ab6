package com.dl.aiservice.share.videoproduce.dto.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: AudioTrackParam
 * @author: zhousx
 * @date: 2023/2/10 11:41
 */
@Data
public class AudioTrackDTO {
    @JsonProperty("AudioTrackClips")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<TrackClipDTO> audioTrackClips;
}
