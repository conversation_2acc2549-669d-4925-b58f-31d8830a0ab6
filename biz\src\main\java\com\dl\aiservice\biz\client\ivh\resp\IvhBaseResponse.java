package com.dl.aiservice.biz.client.ivh.resp;

import com.dl.aiservice.biz.client.ivh.enums.IvhErrCodeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class IvhBaseResponse<T> implements Serializable {

    private static final long serialVersionUID = 1961425384297568194L;
    @JsonProperty(value = "Header")
    private IvhHeaderResponse header;

    @JsonProperty(value = "Payload")
    private T payload;

    public boolean isSuccess() {
        return IvhErrCodeEnum.ERROR_CODE_0.getErrorCode().equals(getHeader().getCode());
    }

}
