package com.dl.aiservice.biz.client.volcengine.resp;

import lombok.Data;

/**
 * @describe: TtsResponse
 * @author: zhousx
 * @date: 2023/5/10 13:42
 */
@Data
public class VolcEngineTtsResp {

    /**
     * 请求 ID,与传入的参数中 reqid 一致
     */
    private String reqid;

    private Integer code;

    private String message;

    private String operation;

    /**
     * 音频段序号
     * 负数表示合成完毕
     */
    private int sequence;

    /**
     * 合成音频
     * 返回的音频数据，base64 编码
     */
    private String data;

    /**
     * 额外信息父节点
     */
    private VolcEngineTtsAddition addition;
    
}
