package com.dl.aiservice.share.digitalman;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DigitalManComposeResponseDTO implements Serializable {

    private static final long serialVersionUID = 6308230476015261602L;

    /**
     * 媒体id 雪花算法
     */
    @ApiModelProperty(value = "媒体id 雪花算法")
    private Long mediaJobId;

    /**
     * 上层业务唯一id
     */
    @ApiModelProperty(value = "通用必填：上层业务唯一id", required = true)
    private Long worksBizId;

}

