package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class IvhGetAnchorRequest implements Serializable {


    private static final long serialVersionUID = -1301231693693196145L;
    /**
     * 为false时查询当前appkey对应资源，为true时查询当前⽤户所有资源
     */
    @JsonProperty(value = "GetAllResource")
    private Boolean getAllResource;
    /**
     * 数智⼈类型code：real_man_2d：2d真⼈real_man_3d：3d真⼈
     */
    @JsonProperty(value = "VirtualmanTypeCode")
    private String virtualmanTypeCode;

}
