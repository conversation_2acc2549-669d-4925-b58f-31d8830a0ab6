package com.dl.aiservice.biz.client.volcengine.interceptor;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.volcengine.VolcEngineVoiceClient;
import com.dl.aiservice.biz.client.volcengine.config.VolcEngineVoiceConfig;
import com.dl.aiservice.biz.client.volcengine.resp.VolcEngineVoiceTrainResp;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.dal.po.TrainResultPO;
import com.dl.aiservice.biz.manager.train.TrainJobManager;
import com.dl.aiservice.biz.manager.train.TrainResultManager;
import com.dl.aiservice.biz.manager.train.enums.TrainStatusEnum;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 火山引擎声音克隆拦截器
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-26 14:07
 */
@Slf4j
public class VolcEngineVoiceMegaTtsInterceptor implements Interceptor {

    @Override
    public boolean beforeExecute(ForestRequest request) {
        VolcEngineVoiceConfig config = ApplicationContextUtils.getContext().getBean(VolcEngineVoiceConfig.class);

        request.addHeader("Authorization", "Bearer;" + config.getAccessToken());
        request.addHeader("Resource-Id", "volc.megatts.voiceclone");
        //log.info("VolcEngineVoiceMegaTtsInterceptor before execute:\n request: {}", request.getBody().nameValuesMapWithObject());
        return Boolean.TRUE;
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("VolcEngineVoiceMegaTtsInterceptor after execute:\nrequest: {},\nhttpStatus:{},\nresponse: {}",
                request.getBody().nameValuesMapWithObject(), response.getStatusCode(), response.getContent());
    }

    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        trainProcess(data, request, response);
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        trainProcess(ex, request, response);
    }

    private void trainProcess(Object data, ForestRequest request, ForestResponse response) {
        if (!trainCheck(request)) {
            return;
        }
        TrainResultManager trainResultManager = ApplicationContextUtils.getContext().getBean(TrainResultManager.class);
        TrainJobManager trainJobManager = ApplicationContextUtils.getContext().getBean(TrainJobManager.class);

        TrainResultPO trainResult = new TrainResultPO();
        Long trainResultBizId = (Long) request.getArgument(Const.ZERO);

        TrainJobPO job = new TrainJobPO();
        Long trainJobId = (Long) request.getArgument(Const.ONE);

        //job.setJobContent(ForestLogUtil.requestLoggingContent(request.getRequestLogMessage()));
        if (data instanceof VolcEngineVoiceTrainResp) {
            VolcEngineVoiceTrainResp resp = (VolcEngineVoiceTrainResp) data;
            //提交训练成功
            if (Const.ZERO.equals(resp.getBaseResp().getStatusCode())) {
                log.info("火山云声音训练提交成功。trainResultBizId:{},,,trainJobId:{},,,resp:{}", trainResultBizId, trainJobId,
                        JSONUtil.toJsonStr(resp));
                return;
            }

            //训练失败
            trainResult.setStatus(TrainStatusEnum.FAIL.getCode());
            job.setStatus(TrainStatusEnum.FAIL.getCode());
            // 三方错误码
            job.setFailCode(String.valueOf(resp.getBaseResp().getStatusCode()));
            // 失败原因
            trainResult.setLatestFailReason(resp.getBaseResp().getStatusMessage());
            job.setFailReason(resp.getBaseResp().getStatusMessage());

        } else {
            trainResult.setStatus(TrainStatusEnum.FAIL.getCode());
            job.setStatus(TrainStatusEnum.FAIL.getCode());
            //失败原因  最大截取255个字符
            String failReason = StringUtils
                    .substring(response.getContent(), 0, Math.min(StringUtils.length(response.getContent()), 255));
            trainResult.setLatestFailReason(failReason);
            job.setFailReason(failReason);
        }
        trainResult.setModifyDt(new Date());
        job.setModifyDt(new Date());
        trainResultManager.lambdaUpdate().eq(TrainResultPO::getBizId, trainResultBizId).update(trainResult);
        trainJobManager.lambdaUpdate().eq(TrainJobPO::getTrainJobId, trainJobId).update(job);

        throw BusinessServiceException.getInstance(job.getFailCode(), job.getFailReason());
    }

    private boolean trainCheck(ForestRequest request) {
        String url = request.getMethod().getMetaRequest().getUrl();
        return StringUtils.equals(VolcEngineVoiceClient.AUDIO_TRAIN_PATH, url);
    }
}
