package com.dl.aiservice.biz.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.aiservice.biz.common.annotation.BaseDao;
import com.dl.aiservice.biz.dal.po.DaVirtualVoicePO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoiceListQueryPairBO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoicePageBO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【da_virtual_voice(数字资产-数字声音信息表)】的数据库操作Mapper
 * @createDate 2023-06-02 13:53:14
 * @Entity com.dl.aiservice.biz.dal.po.DaVirtualVoice
 */
@BaseDao
public interface DaVirtualVoiceMapper extends BaseMapper<DaVirtualVoicePO> {

    IPage<DaVirtualVoicePO> pageVoice(@Param("param") DaVirtualVoicePageBO queryBO);

    List<DaVirtualVoicePO> listByChannelAndVoiceKeyList(@Param("list") List<DaVirtualVoiceListQueryPairBO> list);

}




