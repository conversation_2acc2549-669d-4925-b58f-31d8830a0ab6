package com.dl.aiservice.web.controller.digital.ivh.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class IvhCallbackVO implements Serializable {
    private static final long serialVersionUID = -5040616107365918691L;
    /**
     * 制作进度-1代表生成失败，100代表生成成功（预留字段，目前不具备参考意义）
     */
    @JsonProperty(value = "Progress")
    private Integer progress;
    /**
     * 音视频结果地址
     */
    @JsonProperty(value = "MediaUrl")
    private String mediaUrl;
    /**
     * SRT字幕地址
     */
    @JsonProperty(value = "SubtitlesUrl")
    private String subtitlesUrl;
    /**
     * 制作状态"SUCCESS"：制作成功"FAIL"：制作失败
     */
    @JsonProperty(value = "Status")
    private String status;
    /**
     * 制作失败返回的失败原因，便于排查问题
     */
    @JsonProperty(value = "FailMessage")
    private String failMessage;
    /**
     * 请求视频制作返回的TaskId
     */
    @JsonProperty(value = "TaskId")
    private String taskId;

    /**
     * 时长：毫秒
     */
    @JsonProperty(value = "Duration")
    private Integer duration;
}
