package com.dl.aiservice.biz.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.aiservice.biz.common.po.BasePO;
import lombok.Data;

import java.util.Date;

/**
 * 媒体合成记录表
 *
 * @TableName media_produce_job
 */
@TableName(value = "media_produce_job")
@Data
public class MediaProduceJobPO extends BasePO {
    private static final long serialVersionUID = 4964582771801733175L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 媒体id 雪花算法
     */
    @TableField(value = "media_job_id")
    private Long mediaJobId;

    /**
     * 第三方媒体id
     */
    @TableField(value = "ext_job_id")
    private String extJobId;

    /**
     * 租户代码
     */
    @TableField(value = "tenant_code")
    private String tenantCode;

    /**
     * 渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云 5 heyGen
     */
    @TableField(value = "channel")
    private Integer channel;

    /**
     * 合成类型：0-视频合成 1-数字人 2-TTS音频
     *
     * @see: MediaProduceJobTypeEnum
     */
    @TableField(value = "job_type")
    private Integer jobType;

    /**
     * 任务状态：1 合成中；0 合成完成；-1 合成失败；2-待合成
     *
     * @See:MediaProduceJobStatusEnum
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 回调业务url
     */
    @TableField(value = "callback_url")
    private String callbackUrl;

    /**
     * 请求三方报文
     */
    @TableField(value = "job_content")
    private String jobContent;

    /**
     * 合成后url
     */
    @TableField(value = "media_url")
    private String mediaUrl;

    /**
     * 封面图url
     */
    @TableField(value = "cover_url")
    private String coverUrl;

    /**
     * 时长(单位：秒，如11.32)
     */
    @TableField(value = "duration")
    private Double duration;

    /**
     * 作品名称
     */
    @TableField(value = "media_name")
    private String mediaName;

    /**
     * 三方错误码
     */
    @TableField(value = "fail_code")
    private String failCode;

    /**
     * 失败原因
     */
    @TableField(value = "fail_reason")
    private String failReason;

    /**
     * 上层业务作品唯一标识
     */
    @TableField(value = "works_biz_id")
    private Long worksBizId;

    /**
     * 发起第三方请求的时间
     */
    @TableField(value = "request_dt")
    private Date requestDt;

    /**
     * 第三方响应时间
     */
    @TableField(value = "response_dt")
    private Date responseDt;

    /**
     * 超时状态，0-未超时，1-超时，2-超时,外部成功  3-超时,外部失败
     *
     * @see: com.dl.aiservice.biz.manager.enums.MediaProduceJobTimeoutStatusEnum
     */
    @TableField(value = "timeout_status")
    private Integer timeoutStatus;

    /**
     * 快视频合成任务记录表唯一标识 - job_id
     */
    @TableField(value = "video_task_job_id")
    private Long videoTaskJobId;

    /**
     * 字幕信息
     */
    @TableField(value = "subtitle_detail")
    private String subtitleDetail;
}