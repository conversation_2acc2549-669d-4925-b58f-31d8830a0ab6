package com.dl.aiservice.web.controller.digitalasset.convert;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.dal.po.DaVirtualManPO;
import com.dl.aiservice.biz.dal.po.DaVirtualVoicePO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManScenesBO;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceBaseInfoDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceLinkDTO;
import com.dl.aiservice.share.digitalman.DigitalManAggregationInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManBaseInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManSceneBaseInfoDTO;
import com.dl.aiservice.share.digitalman.SceneInfoDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-14 10:28
 */
public class DigitalAssetConvert {

    public static DigitalManInfoDTO cnvDaVirtualManScenesBO2DTO(List<DaVirtualManScenesBO> digitalManList) {
        if (CollectionUtils.isEmpty(digitalManList)) {
            return null;
        }
        DaVirtualManScenesBO daVirtualManScenesBO = digitalManList.get(Const.ZERO);
        DigitalManInfoDTO result = new DigitalManInfoDTO();
        result.setDigitalManId(daVirtualManScenesBO.getBizId());
        result.setDgAvatar(daVirtualManScenesBO.getVmCode());
        result.setDgName(daVirtualManScenesBO.getVmName());
        result.setDgPer(daVirtualManScenesBO.getVmVoiceKey());
        result.setDgPerBizId(daVirtualManScenesBO.getVoiceBizId());
        result.setGender(daVirtualManScenesBO.getGender());
        result.setChannel(daVirtualManScenesBO.getChannel());
        result.setValidityStartDt(daVirtualManScenesBO.getEffectDt());
        result.setValidityEndDt(daVirtualManScenesBO.getExpiryDt());
        result.setSceneList(digitalManList.stream().map(x -> {
            SceneInfoDTO sceneInfoDTO = new SceneInfoDTO();
            sceneInfoDTO.setSceneId(x.getSceneId());
            sceneInfoDTO.setSceneName(x.getSceneName());
            sceneInfoDTO.setCoverUrl(x.getCoverUrl());
            sceneInfoDTO.setExampleUrl(x.getExampleUrl());
            return sceneInfoDTO;
        }).collect(Collectors.toList()));
        return result;
    }

    public static DigitalManBaseInfoDTO cnvDaVirtualManPO2BaseInfoDTO(DaVirtualManPO input) {
        if (Objects.isNull(input)) {
            return null;
        }
        DigitalManInfoDTO result = new DigitalManInfoDTO();
        fillDigitalManBaseInfoDTO(input, result);
        return result;
    }

    public static void fillDigitalManBaseInfoDTO(DaVirtualManPO input, DigitalManBaseInfoDTO result) {
        result.setDgName(input.getVmName());
        result.setDigitalManId(input.getBizId());
        result.setDgAvatar(input.getVmCode());
        result.setDgPer(input.getVmVoiceKey());
        result.setDgPerBizId(input.getVoiceBizId());
        result.setChannel(input.getChannel());
        result.setGender(input.getGender());
        result.setValidityStartDt(input.getEffectDt());
        result.setValidityEndDt(input.getExpiryDt());
        result.setDefaultSpeed(input.getDefaultSpeed());
        result.setIsEnableSpeed(input.getIsEnableSpeed());
        result.setHeadImg(input.getHeadImg());
        result.setVmType(input.getVmType());
    }

    public static DigitalManAggregationInfoDTO buildDigitalManAggregationInfoDTO(DaVirtualManPO daVirtualManPO,
            List<DaVirtualManScenesBO> sceneBOList, DaVirtualVoicePO voicePO) {
        DigitalManAggregationInfoDTO result = new DigitalManAggregationInfoDTO();
        fillDigitalManBaseInfoDTO(daVirtualManPO, result);

        if (Objects.nonNull(voicePO)) {
            DaVirtualVoiceBaseInfoDTO voiceInfo = new DaVirtualVoiceBaseInfoDTO();
            fillDaVirtualVoiceDTO(voicePO, null, voiceInfo);
            result.setVoiceInfo(voiceInfo);
        }

        if (CollectionUtils.isNotEmpty(sceneBOList)) {
            List<DigitalManSceneBaseInfoDTO> sceneList = sceneBOList.stream().map(sceneBO -> {
                DigitalManSceneBaseInfoDTO sceneDTO = new DigitalManSceneBaseInfoDTO();
                fillDigitalManSceneBaseInfoDTO(sceneBO, sceneDTO);
                return sceneDTO;
            }).collect(Collectors.toList());
            result.setSceneList(sceneList);
        }

        return result;
    }

    public static DaVirtualVoiceDTO cnvDaVirtualVoicePO2DTO(DaVirtualVoicePO input, String tenantCode) {
        DaVirtualVoiceDTO result = new DaVirtualVoiceDTO();
        fillDaVirtualVoiceDTO(input, tenantCode, result);
        return result;
    }

    public static DaVirtualVoiceBaseInfoDTO cnvDaVirtualVoicePO2BaseDTO(DaVirtualVoicePO input, String tenantCode) {
        DaVirtualVoiceBaseInfoDTO result = new DaVirtualVoiceBaseInfoDTO();
        fillDaVirtualVoiceDTO(input, tenantCode, result);
        return result;
    }

    public static void fillDaVirtualVoiceDTO(DaVirtualVoicePO input, String tenantCode,
            DaVirtualVoiceBaseInfoDTO result) {
        result.setBizId(input.getBizId());
        result.setTenantCode(tenantCode);
        result.setChannel(input.getChannel());
        result.setVoiceKey(input.getVoiceKey());
        result.setVoiceName(input.getVoiceName());
        result.setGender(input.getGender());
        result.setVoiceDesc(input.getVoiceDesc());
        result.setVoiceType(input.getVoiceType());
        result.setVoiceCategory(input.getVoiceCategory());
        result.setSampleLink(input.getSampleLink());
        result.setEffectDt(input.getEffectDt());
        result.setExpiryDt(input.getExpiryDt());
        result.setMaxVoiceLink(input.getMaxVoiceLink());
        result.setSpeed(input.getSpeed());
        result.setVolume(input.getVolume());
        result.setDuration(input.getDuration());
        result.setHeadImg(input.getHeadImg());
        result.setIsEnabled(input.getIsEnabled());

        if (StringUtils.isNotBlank(input.getVoiceLinks())) {
            List<DaVirtualVoiceLinkDTO> voiceLinkDTOList = JSONObject
                    .parseObject(input.getVoiceLinks(), new TypeReference<List<DaVirtualVoiceLinkDTO>>() {
                    });
            result.setVoiceLinks(voiceLinkDTOList);
        }

    }

    public static DaVirtualVoicePO cnvDaVirtualVoiceDTO2PO(DaVirtualVoiceDTO input) {
        DaVirtualVoicePO result = new DaVirtualVoicePO();
        result.setBizId(input.getBizId());
        result.setTenantCode(input.getTenantCode());
        result.setChannel(input.getChannel());
        result.setVoiceKey(input.getVoiceKey());
        result.setVoiceName(input.getVoiceName());
        result.setGender(input.getGender());
        result.setVoiceType(input.getVoiceType());
        result.setVoiceDesc(input.getVoiceDesc());
        result.setVoiceCategory(input.getVoiceCategory());
        result.setIsEnabled(input.getIsEnabled());
        result.setSampleLink(input.getSampleLink());
        result.setEffectDt(input.getEffectDt());
        result.setExpiryDt(input.getExpiryDt());
        result.setMaxVoiceLink(input.getMaxVoiceLink());
        result.setVolume(input.getVolume());
        result.setSpeed(input.getSpeed());
        result.setHeadImg(input.getHeadImg());
        result.setDuration(input.getDuration());

        if (CollectionUtils.isNotEmpty(input.getVoiceLinks())) {
            result.setVoiceLinks(JSONUtil.toJsonStr(input.getVoiceLinks()));
        }
        return result;
    }

    public static DaVirtualManScenesDTO cnvDaVirtualManScenesBO2DTO(DaVirtualManScenesBO input, String tenantCode) {
        DaVirtualManScenesDTO result = new DaVirtualManScenesDTO();
        result.setId(input.getId());
        result.setBizId(input.getBizId());
        result.setIsEnabled(input.getIsEnabled());
        result.setTenantCode(tenantCode);
        result.setVmCode(input.getVmCode());
        result.setVmName(input.getVmName());
        result.setVmVoiceKey(input.getVmVoiceKey());
        result.setVoiceBizId(input.getVoiceBizId());
        result.setGender(input.getGender());
        result.setHeadImg(input.getHeadImg());
        result.setPhone(input.getPhone());
        result.setEmail(input.getEmail());
        result.setPositionName(input.getPositionName());
        result.setVmType(input.getVmType());
        result.setEffectDt(input.getEffectDt());
        result.setExpiryDt(input.getExpiryDt());
        result.setChannel(input.getChannel());
        fillDigitalManSceneBaseInfoDTO(input, result);
        return result;
    }

    public static void fillDigitalManSceneBaseInfoDTO(DaVirtualManScenesBO input, DigitalManSceneBaseInfoDTO result) {
        result.setIsEnabled(input.getIsEnabled());
        result.setSceneId(input.getSceneId());
        result.setSceneName(input.getSceneName());
        result.setCloth(input.getCloth());
        result.setPose(input.getPose());
        result.setResolution(input.getResolution());
        result.setCoverUrl(input.getCoverUrl());
        result.setExampleUrl(input.getExampleUrl());
        result.setExampleText(input.getExampleText());
        result.setExampleDuration(input.getExampleDuration());
    }
}
