package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IvhVideoMakeAdvancedRequest implements Serializable {


    private static final long serialVersionUID = 5665417990096231528L;

    /**
     * 定义播报的⻆⾊、服装、姿态、分辨率等信息，参数为枚举值。
     */
    @JsonProperty(value = "VirtualmanKey")
    private String virtualmanKey;
    /**
     * 播报的⽂本内容，⽀持ssml标签，⽀持的标签类型参照附录2，标签写法参照示例，内容不能换⾏，符参数类型必须说明号需转义。2d播报上限1万字；3d播报上限500字。DriverType为空、或Text时，该字段必填
     */
    @JsonProperty(value = "InputSsml")
    private String inputSsml;
    /**
     * 定义⾳频的详细参数
     */
    @JsonProperty(value = "SpeechParam")
    public IvhSpeedRequest speechParam;
    /**
     * 定义合成视频的详细参数，
     */
    @JsonProperty(value = "VideoParam")
    public IvhVideoAdvancedRequest videoParam;


    /**
     * 当⽤户增加回调url时，将把视频制
     * 作结果以固定格式发送post请求到
     * 该url地址，固定格式⻅附录三，需
     * 注意：1、限制CallbackUrl⻓度⼩
     * 于2002、只发送⼀次请求，⽆论
     * 是哪种问题导致的请求失败，都不
     * 会再进⾏发送。
     */
    @JsonProperty(value = "CallbackUrl")
    public String callbackUrl;



    /**
     * 驱动类型，默认Text1. Text：⽂本
     * 驱动，要求InputSsml字段必填2.
     * OriginalVoice：原声⾳频驱动，要
     * 求InputAudioUrl字段必填3.
     * ModulatedVoice：变声⾳频驱
     * 动，可通过Speech.TimbreKey指
     * 定⾳⾊，未填写时使⽤主播默认⾳
     * ⾊
     */
    @JsonProperty(value = "DriverType")
    public String DriverType;


    /**
     * 驱动数智⼈的⾳频url，当
     * DriverType为OriginalVoice、
     * ModulatedVoice时，该字段必
     * 填。⾳频格式要求（待确认）：
     * 1、时⻓不超过10分钟，不少于0.5
     * 秒2、⽀持格式：mp3、wav
     */
    @JsonProperty(value = "InputAudioUrl")
    public String InputAudioUrl;

    @JsonProperty(value = "VideoStorageS3Url")
    public String videoStorageS3Url;
}
