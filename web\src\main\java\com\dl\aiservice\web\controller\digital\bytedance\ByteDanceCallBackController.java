package com.dl.aiservice.web.controller.digital.bytedance;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.client.bytedance.enums.ByteDanceCallBackStatusEnum;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.enums.MediaProduceJobTimeoutStatusEnum;
import com.dl.aiservice.biz.service.digital.bytedance.ByteDanceDigitalService;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.aiservice.share.enums.MediaProduceJobStatusEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.web.controller.digital.bytedance.vo.ByteDanceCallbackVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * @describe: 字节数字人回调
 * @author: zhousx
 * @date: 2023/10/8 15:43
 */
@Slf4j
@RestController
@RequestMapping("/bytedance")
public class ByteDanceCallBackController {
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private ByteDanceDigitalService byteDanceDigitalService;

    /**
     * 合成任务回调接口
     */
    @PostMapping("/callback")
    public void videoCreateCallBack(@RequestBody ByteDanceCallbackVO request) {
        log.info("字节数字人回调，回调参数：{}", JSONObject.toJSONString(request));
        MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.getOne(Wrappers.<MediaProduceJobPO>lambdaUpdate()
                .eq(MediaProduceJobPO::getChannel, ServiceChannelEnum.VOLC_ENGINE.getCode())
                .eq(MediaProduceJobPO::getExtJobId, request.getTaskId()));
        if (Objects.isNull(mediaProduceJobPO)) {
            log.info("字节数字人回调,未找到对应合成任务，三方任务id:{},具体回调信息：{}", request.getTaskId(),
                    JSONObject.toJSONString(request));
            return;
        }

        //是否已超时
        Boolean hasTimeout = !MediaProduceJobTimeoutStatusEnum.UN.getStatus()
                .equals(mediaProduceJobPO.getTimeoutStatus());

        this.fillMediaProduceJobStatus(request, mediaProduceJobPO, hasTimeout);
        mediaProduceJobPO.setMediaUrl(request.getVideoUrl());
        mediaProduceJobPO.setFailReason(request.getFailureReason());
        mediaProduceJobPO.setResponseDt(new Date());
        if(Objects.nonNull(request.getDuration())) {
            mediaProduceJobPO.setDuration(request.getDuration() * 1.0/1000);
        }
        mediaProduceJobManager.updateById(mediaProduceJobPO);
        Long worksBizId = mediaProduceJobPO.getWorksBizId();
        DigitalVideoCallbackDTO digitalCallbackDTO = new DigitalVideoCallbackDTO();
        BeanUtils.copyProperties(mediaProduceJobPO, digitalCallbackDTO);

        //已超时则不回调业务
        if (hasTimeout) {
            return;
        }

        //回调业务
        byteDanceDigitalService.callBackBiz(mediaProduceJobPO.getTenantCode(), worksBizId, mediaProduceJobPO.getCallbackUrl(),
                digitalCallbackDTO, JSONUtil.toJsonStr(request));

    }

    private void fillMediaProduceJobStatus(ByteDanceCallbackVO request, MediaProduceJobPO mediaProduceJobPO,
                                           Boolean hasTimeout) {
        ByteDanceCallBackStatusEnum byteDanceCallBackStatusEnum = ByteDanceCallBackStatusEnum.getEnum(request.getTaskStatus());

        //先判断是否已经超时，若已超时，则修改timeout_status，不处理status
        if (hasTimeout) {
            if (ByteDanceCallBackStatusEnum.SUCCESS == byteDanceCallBackStatusEnum) {
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_SUCCESS.getStatus());
                return;
            }
            if (ByteDanceCallBackStatusEnum.FAIL == byteDanceCallBackStatusEnum) {
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_FAIL.getStatus());
                return;
            }
            return;
        }

        if (ByteDanceCallBackStatusEnum.SUCCESS == byteDanceCallBackStatusEnum) {
            mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.SUCCESS.getStatus());
        } else {
            mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.FAIL.getStatus());
        }
    }
}
