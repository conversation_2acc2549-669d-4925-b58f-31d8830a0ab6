package com.dl.aiservice.web.controller.aichat;

import com.dl.aiservice.share.aichat.AiFileContentAndTitleRespDTO;
import com.dl.aiservice.share.aichat.AiMultiChatRequestDTO;
import com.dl.aiservice.share.aichat.AiSingleChatRequestDTO;
import com.dl.aiservice.share.aichat.AiSingleChatResponseDTO;
import com.dl.aiservice.share.aichat.errorcode.AiMultiChatResponseDTO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 17:00
 */
@RestController
@RequestMapping("/aichat")
public class AiChatController {

    @Resource
    private AiChatProcess aiChatProcess;

    /**
     * 单条对话
     * 等待aigc全部响应完后再响应
     * <p>
     * 通用接口
     *
     * @param requestDTO
     * @return
     */
    @ApiOperation("单条对话")
    @PostMapping("/singlechat")
    public ResultModel<AiSingleChatResponseDTO> singleChat(@RequestBody @Validated AiSingleChatRequestDTO requestDTO) {
        return aiChatProcess.singleChat(requestDTO);
    }

    /**
     * 多条对话，只返回最后的响应
     * 等待aigc全部响应完后再响应
     * <p>
     * 通用接口
     *
     * @param requestDTO
     */
    @ApiOperation("多条对话，只返回最后的响应")
    @PostMapping("/multichat")
    public ResultModel<AiMultiChatResponseDTO> multiChat(@RequestBody @Validated AiMultiChatRequestDTO requestDTO) {
        return aiChatProcess.multiChat(requestDTO);
    }

    /**
     * 流式地单条对话
     * <p>
     * 通用接口
     *
     * @param requestDTO
     * @return
     */
    @ApiOperation("流式地单条对话")
    @PostMapping(value = { "/stream/singlechatstream", "/singlechatstream" })
    public void singleChatStream(HttpServletResponse resp, @RequestBody @Validated AiSingleChatRequestDTO requestDTO)
            throws IOException {
        try (OutputStream out = resp.getOutputStream()) {
            aiChatProcess.singleChatStream(resp, requestDTO);
        }
    }

    /**
     * 多条对话，最后的响应以流式返回
     * <p>
     * 通用接口
     *
     * @param resp
     * @param requestDTO
     */
    @ApiOperation("多条对话，最后的响应以流式返回")
    @PostMapping("/stream/multichatfinalstream")
    public void multiChatFinalStream(HttpServletResponse resp, @RequestBody @Validated AiMultiChatRequestDTO requestDTO)
            throws IOException {
        try (OutputStream out = resp.getOutputStream()) {
            aiChatProcess.multiChatFinalStream(resp, requestDTO);
        }
    }

    /**
     * 提取文件内容和标题
     * <p>
     * 通用接口
     *
     * @param file
     * @param presupposeText
     * @param userId
     * @return
     */
    @ApiOperation("提取文件内容和标题")
    @PostMapping("/extractfilecontentandtitle")
    public ResultModel<AiFileContentAndTitleRespDTO> extractFileContentAndTitle(MultipartFile file,
            @RequestParam(required = false) String presupposeText, @RequestParam Long userId,
            @RequestParam(required = false) String model, @RequestParam(required = false) Integer respMaxToken) {
        return aiChatProcess.extractFileContentAndTitle(file, presupposeText, userId, model, respMaxToken);
    }

}
