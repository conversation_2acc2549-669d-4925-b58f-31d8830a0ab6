package com.dl.aiservice.biz.service.digital.dto.req.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AliyunVideoOutputRequestDTO {

    /**
     * 地址
     */
    @JsonProperty("MediaURL")
    private String MediaURL;
    /**
     * 宽
     */
    @JsonProperty("Width")
    private Integer Width;
    /**
     * 高
     */
    @JsonProperty("Height")
    private Integer Height;
}