package com.dl.aiservice.biz.client.aliyun.interceptor;

import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-31 15:25
 */
@Slf4j
public class AliyunAsrInterceptor implements Interceptor {

    @Override
    public boolean beforeExecute(ForestRequest request) {
        return Boolean.TRUE;
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("AliyunAsrInterceptor after execute:\nrequest: {}\nresponse: {}",
                request.getBody().nameValuesMapWithObject(), response.getContent());
    }

    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {

    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {

    }
}
