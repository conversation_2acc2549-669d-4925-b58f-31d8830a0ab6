package com.dl.aiservice.share.digitalasset;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DaVirtualVoiceAuthorizeDTO implements Serializable {

    private static final long serialVersionUID = 3435015445940055700L;

    @ApiModelProperty(value = "数字声音唯一标识")
    private Long bizId;

    /**
     * 授权集合
     */
    private List<String> tenantCodeList;
}