package com.dl.aiservice.biz.client.deepsound.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName DsAudioCheckResponse
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 15:47
 * @Version 1.0
 **/
@Data
public class DsAudioCheckResponse extends DsBaseResponse {

    private static final long serialVersionUID = -255119982193376160L;

    /**
     * 语音识别文本
     */
    @JsonProperty("asr_text")
    private String asrText;

    /**
     * array[Object]
     * 语音识别检测多漏错读提示信息。
     */
    @JsonProperty("diff_info")
    private List<DsDiffInfo> diffInfo;
}
