package com.dl.aiservice.biz.client.deepsound.resp;

import com.dl.aiservice.biz.client.deepsound.enums.DsEvnCheckErrCodeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class DsBaseResponse implements Serializable {

    private static final long serialVersionUID = -6442816271939461816L;

    private Integer code;

    private String errMsg;

    private String msg;

    public boolean isSuccess() {
        if (DsEvnCheckErrCodeEnum.ERROR_CODE_0.getErrorCode().equals(getCode())) {
            return true;
        }
        return false;
    }
}
