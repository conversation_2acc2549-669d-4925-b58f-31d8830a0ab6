package com.dl.aiservice.biz.service.digital.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class RobotDetailResponseDTO implements Serializable {

    private static final long serialVersionUID = 6660662421497170627L;

    /**
     * 免费模特详情
     */
    @ApiModelProperty(value = "免费模特详情")
    private List<RobotDetailDTO> robotDetailList;

}

