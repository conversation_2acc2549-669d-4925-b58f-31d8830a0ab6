package com.dl.aiservice.job.executor.digitalman;

import com.dl.aiservice.biz.digitaljobhandler.IvhDigitalManJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-13 15:52
 */
@Component
public class IvhDigitalManTask {

    @Resource
    private IvhDigitalManJobHandler ivhDigitalManJobHandler;

//    @XxlJob("ivhDmVideoJob")
    public void ivhDmVideoJob() {
        ivhDigitalManJobHandler.handleDmVideoCreate();
    }

}
