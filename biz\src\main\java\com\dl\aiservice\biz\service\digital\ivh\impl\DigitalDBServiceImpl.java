package com.dl.aiservice.biz.service.digital.ivh.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.train.TrainJobManager;
import com.dl.aiservice.biz.service.digital.DigitalDBService;
import com.dl.aiservice.biz.service.digital.bo.CreateTrainBO;
import com.dl.aiservice.biz.service.digital.bo.CreateTrainUpdateBO;
import com.dl.aiservice.biz.service.digital.bo.CreateVideoBO;
import com.dl.aiservice.biz.service.digital.bo.CreateVideoUpdateBO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * @author: xuebin
 * @description 智能数字人RPC接口
 * @Date: 2023/2/28 17:50
 */
@Service
public class DigitalDBServiceImpl implements DigitalDBService {

    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private ChannelUtil channelUtil;
    @Resource
    private TrainJobManager trainJobManager;

    @Override
    public MediaProduceJobPO saveVideoCreate(CreateVideoBO createVideoBO) {
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(hostTimeIdg.generateId().longValue());
        job.setTenantCode(channelUtil.getTenantCode());
        job.setChannel(channelUtil.getChannel());
        job.setJobType(Const.ONE);
        job.setStatus(Const.TWO);
        job.setCallbackUrl(createVideoBO.getCallbackUrl());
        job.setMediaName(createVideoBO.getVideoName());
        job.setWorksBizId(createVideoBO.getWorksBizId());
        job.setVideoTaskJobId(createVideoBO.getVideoTaskJobId());
        job.setRequestDt(new Date());
        mediaProduceJobManager.save(job);
        return job;
    }

    @Override
    public void updateVideoById(CreateVideoUpdateBO bo) {
        LambdaUpdateWrapper<MediaProduceJobPO> eq = Wrappers.<MediaProduceJobPO>lambdaUpdate()
                .set(Objects.nonNull(bo.getTaskId()), MediaProduceJobPO::getExtJobId, bo.getTaskId())
                .set(Objects.nonNull(bo.getStatus()), MediaProduceJobPO::getStatus, bo.getStatus())
                .set(Objects.nonNull(bo.getRequestDt()), MediaProduceJobPO::getRequestDt, bo.getRequestDt())
                .eq(MediaProduceJobPO::getId, bo.getId());
        mediaProduceJobManager.update(eq);

    }

    @Override
    public TrainJobPO saveTrainCreate(CreateTrainBO createVideoBO) {
        TrainJobPO trainJobPO = new TrainJobPO();
        trainJobPO.setTenantCode(channelUtil.getTenantCode());
        trainJobPO.setChannel(channelUtil.getChannel());
        trainJobPO.setJobType(Const.ZERO);
        trainJobPO.setTrainJobId(hostTimeIdg.generateId().longValue());
        trainJobPO.setStatus(Const.ONE);
        trainJobPO.setCallbackUrl(createVideoBO.getCallbackUrl());
        trainJobManager.save(trainJobPO);
        return trainJobPO;
    }

    @Override
    public void updateTrainById(CreateTrainUpdateBO bo) {
        LambdaUpdateWrapper<TrainJobPO> eq = Wrappers.<TrainJobPO>lambdaUpdate()
                .set(Objects.nonNull(bo.getTaskId()), TrainJobPO::getExtJobId, bo.getTaskId())
                .eq(TrainJobPO::getId, bo.getId());
        trainJobManager.update(eq);
    }

    @Override
    public MediaProduceJobPO getWorksBizId(TaskRequestDTO taskRequestDTO) {
        LambdaQueryWrapper<MediaProduceJobPO> eq = Wrappers.<MediaProduceJobPO>lambdaQuery()
                .eq(MediaProduceJobPO::getWorksBizId, taskRequestDTO.getWorksBizId());
        return mediaProduceJobManager.getOne(eq);
    }

}

