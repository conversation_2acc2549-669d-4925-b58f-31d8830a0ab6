package com.dl.aiservice.share.digitalman;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName DigitalManInfoDTO
 * @Description
 * <AUTHOR>
 * @Date 2023/3/27 15:30
 * @Version 1.0
 **/
@Data
public class DigitalManInfoDTO extends DigitalManBaseInfoDTO implements Serializable {
    private static final long serialVersionUID = -3165940954541777060L;

    @ApiModelProperty(value = "仿真人场景列表")
    private List<SceneInfoDTO> sceneList;

}
