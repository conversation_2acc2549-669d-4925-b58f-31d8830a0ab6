package com.dl.aiservice.biz.client.guiji.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


@Data
public class GJTokenResponse {

    private static final long serialVersionUID = -255119982193376160L;

    /**
     * 返回的token值
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * Token过期时间，单位“秒”
     */
    @JsonProperty("expires_in")
    private Integer expiresIn;
}

