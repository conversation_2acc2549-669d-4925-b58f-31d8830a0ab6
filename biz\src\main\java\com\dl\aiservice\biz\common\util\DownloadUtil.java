package com.dl.aiservice.biz.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;

@Slf4j
public class DownloadUtil {

    /**
     * url转MultipartFile
     *
     * @url:图片URL
     * @fileName:文件名
     * @return:返回的文件
     */
    public static MultipartFile urlToMultipartFile(String url,String fileName) {
        if(StringUtils.isBlank(url)) {
            return null;
        }
        MultipartFile multipartFile = null;
        InputStream inputStream = null;
        try {
            HttpURLConnection httpUrl = (HttpURLConnection) new URL(url).openConnection();
            inputStream = httpUrl.getInputStream();

            multipartFile = getMultipartFile(inputStream, fileName);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);
                }
            }
        }
        return multipartFile;
    }

    /**
     * 获取封装得MultipartFile
     *
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return MultipartFile
     */
    public static MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
        FileItem fileItem = createFileItem(inputStream, fileName);
        //CommonsMultipartFile是feign对multipartFile的封装，但是要FileItem类对象
        return new CommonsMultipartFile(fileItem);
    }

    /**
     * FileItem类对象创建
     *
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return FileItem
     */
    public static FileItem createFileItem(InputStream inputStream, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "file";
        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        OutputStream os = null;
        //使用输出流输出输入流的字节
        try {
            os = item.getOutputStream();
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            inputStream.close();
        } catch (IOException e) {
            log.error("Stream copy exception", e);
            throw new IllegalArgumentException("文件上传失败");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);
                }
            }
        }
        return item;
    }

    public static byte[] getData(String urlStr) {
        URL url;
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            url = new URL(urlStr);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            inputStream = connection.getInputStream();

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            byte[] fileContent = outputStream.toByteArray();
            return fileContent;
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public static File buildFile(byte[] fileContent,String sessionId,String format){
        FileOutputStream fos = null;
        File file = new File(sessionId + "." + format); // 文件路径

        try {
            fos = new FileOutputStream(file);
            fos.write(fileContent); // 将字节数组写入文件
            return file;
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    /**
     * 将远程文件的资源下载到固定地址
     *
     * @param fileUrl  远程文件地址
     * @param savePath 保存的路径
     * @throws IOException
     */
    public static File downloadFile(String fileUrl, String savePath) throws IOException {
        URL url = new URL(fileUrl);
        File file = new File(savePath);
        try (BufferedInputStream in = new BufferedInputStream(url.openStream());
                FileOutputStream fileOutputStream = new FileOutputStream(savePath)) {

            byte[] dataBuffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(dataBuffer, 0, 1024)) != -1) {
                fileOutputStream.write(dataBuffer, 0, bytesRead);
            }
        } catch (Exception e) {
            log.error("文件下载失败！fileUrl:{},,,savePath:{},,,e:", fileUrl, savePath, e);
            throw e;
        }
        return file;
    }

    public static String getCosKeyFromUrlString(String urlStr) {
        URL url = null;
        try {
            url = new URL(urlStr);
            String path = url.getPath();
            if (StringUtils.isNotBlank(path) && path.length() > 1) {
                String[] arr = path.split("\\/");
                int last = arr.length;
                return arr[last - 1];
            }
        } catch (MalformedURLException e) {
            log.error("获取对象key失败,url={}", url, e);
        }
        return null;
    }

    public static String encodeCosKeyFromUrlString(String urlStr) {
        URL url = null;
        try {
            url = new URL(urlStr);
            String path = url.getPath();
            if (StringUtils.isNotBlank(path) && path.length() > 1) {
                String[] arr = path.split("\\/");
                int last = arr.length;
                String cosKey = arr[last - 1];
                String other = urlStr.replace(cosKey, "");
                String encode = URLEncoder.encode(cosKey, "UTF-8");
                encode = encode.replaceAll("\\+", "%20");
                return other + encode;
            }
        } catch (MalformedURLException | UnsupportedEncodingException e) {
            log.error("urlencode对象key失败,url={}", url, e);
        }
        return null;
    }

}
