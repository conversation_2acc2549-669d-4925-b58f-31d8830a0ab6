package com.dl.aiservice.biz.client.kimi.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 10:41
 */
public enum ChatMessageRoleEnum {

    SYSTEM("system", "系统"),
    USER("user", "用户"),
    ASSISTANT("assistant", "ai助手");

    private String code;

    private String desc;

    ChatMessageRoleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
