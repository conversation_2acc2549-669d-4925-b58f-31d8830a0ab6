package com.dl.aiservice.biz.client.guiji.enums;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;

public enum GJCallBackEnum {

    /**
     * 视频合成任务回调
     */
    VIDEO_SYNTHESIS(0, "video-synthesis"),

    /**
     * 视频训练任务回调
     */
    VIDEO_TRAINING(1, "video-training");


    private final Integer code;
    private final String desc;

    GJCallBackEnum(Integer errorCode, String errorDesc) {
        this.code = errorCode;
        this.desc = errorDesc;
    }

    public static GJCallBackEnum getEnum(String desc) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getDesc(), desc)).findFirst().orElseThrow(() -> BusinessServiceException.getInstance("枚举异常"));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
