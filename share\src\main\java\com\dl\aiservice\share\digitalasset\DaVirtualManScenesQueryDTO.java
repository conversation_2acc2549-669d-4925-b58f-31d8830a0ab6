package com.dl.aiservice.share.digitalasset;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 19:40
 */
@Data
public class DaVirtualManScenesQueryDTO {

    @NotEmpty(message = "数字人列表不能为空")
    @ApiModelProperty(value = "数字人场景id列表")
    private List<String> sceneIds;

    @NotEmpty(message = "渠道列表不能为空")
    @ApiModelProperty(value = "渠道列表")
    private List<Integer> channels;

    @ApiModelProperty(value = "判断是否查询is_enable=0的数据，默认null时仅查询值为1的数据")
    private Integer enableFilter;
}
