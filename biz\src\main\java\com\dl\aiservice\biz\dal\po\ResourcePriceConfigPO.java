package com.dl.aiservice.biz.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资源单价配置
 *
 * @TableName resource_price_config
 */
@TableName(value = "resource_price_config")
@Data
public class ResourcePriceConfigPO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 资源类型 1-视频合成，2-数字人，3-克隆音
     */
    private Integer resourceType;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 渠道 0-新华智云 1-硅基 2-腾讯云 3-深声科技 4-阿里云
     */
    private Integer channel;

    /**
     * 每分钟单价，单位分
     */
    private Long price;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 修改人
     */
    private Long modifyBy;

    /**
     * 创建时间
     */
    private Date createDt;

    /**
     * 修改时间
     */
    private Date modifyDt;

    /**
     * 状态 0-生效 1-无效
     */
    private Integer status;

    /**
     * 同 tenant_code、resource_type下，不同的channel之间的排序
     * 注意：mysql不允许使用order关键字
     */
    @TableField(value = "sort_order")
    private Integer sortOrder;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}