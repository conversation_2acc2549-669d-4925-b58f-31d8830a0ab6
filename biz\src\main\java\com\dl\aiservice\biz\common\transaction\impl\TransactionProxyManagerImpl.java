package com.dl.aiservice.biz.common.transaction.impl;

import com.dl.aiservice.biz.common.transaction.TransactionProxyManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.function.Function;

/**
 * @ClassName TransactionManager
 * @Description
 * <AUTHOR>
 * @Date 2022/5/6 17:54
 * @Version 1.0
 **/
@Service
public class TransactionProxyManagerImpl implements TransactionProxyManager {

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void process(Runnable runnable) {
        if (Objects.nonNull(runnable)) {
            runnable.run();
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public <T, R> R process(T t, Function<T, R> fun) {
        Objects.requireNonNull(t);
        Objects.requireNonNull(fun);
        return fun.apply(t);
    }
}
