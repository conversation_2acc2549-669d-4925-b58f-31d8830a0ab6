package com.dl.aiservice.share.videoproduce.dto.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: ImageTrackParam
 * @author: zhousx
 * @date: 2023/2/10 11:42
 */
@Data
public class ImageTrackDTO {
    @JsonProperty("ImageTrackClips")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<TrackClipDTO> imageTrackClips;
}
