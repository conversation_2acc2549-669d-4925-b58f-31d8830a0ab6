package com.dl.aiservice.share.digitalman;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@NoArgsConstructor
@Data
public class DigitalManComposeRequestDTO implements Serializable {

    private static final long serialVersionUID = -7519248337934371242L;

    /**
     * 上层业务唯一id
     */
    @ApiModelProperty(value = "通用必填：上层业务唯一id", required = true)
    private Long worksBizId;

    /**
     * 必填 合成结果回调地址
     */
    @ApiModelProperty(value = "通用必填：合成结果回调地址", required = true)
    private String callbackUrl;

    /**
     * 必填 0音频 1文本
     */
    @ApiModelProperty(value = "通用必填：必填 0音频 1文本", required = true)
    private Integer type = 0;

    //**************************************硅基必填参数**********************************************
    /**
     * 必填 场景ID，从模特列表接口获取
     */
    @ApiModelProperty(value = "硅基必填： 场景ID，从模特列表接口获取")
    private String sceneId;

    /**
     * 音频地址 与文本二选一
     */
    @ApiModelProperty(value = "硅基：音频地址 与文本二选一 type为0 必填")
    private String audioUrl;

    /**
     * 发音人id 合成文本时必填
     */
    @ApiModelProperty(value = "硅基：发音人id type为1 必填")
    private String speakerId;

    //**************************************腾讯云必填参数**********************************************
    /**
     * 必填 定义播报的⻆⾊、服装、姿态、分辨率等信息，参数为枚举值。
     */
    @ApiModelProperty(value = "腾讯云必填：定义播报的⻆⾊、服装、姿态、分辨率等信息，参数为枚举值")
    private String virtualmanKey;

    /**
     * 必填 语速（1.0为正常语速，范围[0.5-1.5]，值为0.5时播报语速最慢，值为1.5时播报语速最快，DriverType为⾳频驱动类型时，语速控制不⽣效）
     */
    @ApiModelProperty(value = "腾讯云必填： 语速")
    private Double speed;

    /**
     * 合成的文本
     */
    @ApiModelProperty(value = "文本")
    private String text;

    @ApiModelProperty(value = "是否使用自定义地址存储音频 0-否 1-是")
    private Integer customStoreUrl;
}