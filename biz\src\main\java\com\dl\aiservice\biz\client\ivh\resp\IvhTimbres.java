package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class IvhTimbres {

    /**
     * 数智⼈⾳⾊code
     */
    @JsonProperty(value = "TimbreKey")
    private String timbreKey;
    /**
     * 数智⼈⾳⾊名称
     */
    @JsonProperty(value = "TimbreName")
    private String timbreName;
    /**
     * 数智⼈⾳⾊试听样例url
     */
    @JsonProperty(value = "TimbreSample")
    private String timbreSample;
    /**
     * 数智⼈⾳⾊描述
     */
    @JsonProperty(value = "TimbreDesc")
    private String timbreDesc;

}