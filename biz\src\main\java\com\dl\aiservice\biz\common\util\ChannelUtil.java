package com.dl.aiservice.biz.common.util;

import com.dl.aiservice.share.common.auth.AuthTokenDTO;
import org.springframework.stereotype.Component;

@Component
public class ChannelUtil {

    private static ThreadLocal<AuthTokenDTO> channelHolder = new ThreadLocal<>();

    public void init(AuthTokenDTO token) {
        channelHolder.set(token);
    }

    /**
     * 多线程中使用,其它场景不建议使用
     *
     * @param tenantCode
     * @param channel
     */
    public void init(String tenantCode, Integer channel) {
        AuthTokenDTO authToken = new AuthTokenDTO();
        authToken.setTenantCode(tenantCode);
        authToken.setChannel(channel);
        channelHolder.set(authToken);
    }

    public AuthTokenDTO getAuthToken() {
        return channelHolder.get();
    }

    public Integer getChannel() {
        return channelHolder.get().getChannel();
    }

    public String getTenantCode() {
        return channelHolder.get().getTenantCode();
    }

    public void remove() {
        channelHolder.remove();
    }
}
