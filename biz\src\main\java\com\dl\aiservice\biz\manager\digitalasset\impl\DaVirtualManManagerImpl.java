package com.dl.aiservice.biz.manager.digitalasset.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.mapper.DaVirtualManMapper;
import com.dl.aiservice.biz.dal.po.DaTenantAuthPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManQueryPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManScenesPO;
import com.dl.aiservice.biz.manager.digitalasset.DaTenantAuthManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualManManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualManScenesManager;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManBO;
import com.dl.aiservice.biz.manager.digitalasset.conf.DaOldNewBizIdConfig;
import com.dl.aiservice.biz.manager.digitalasset.enums.DaTenantAuthBizTypeEnum;
import com.dl.aiservice.share.digitalasset.DaVirtualManDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManPageRequestDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【da_virtual_man(数字资产-仿真人信息表)】的数据库操作Service实现
 * @createDate 2023-06-02 13:53:14
 */
@Service
public class DaVirtualManManagerImpl extends ServiceImpl<DaVirtualManMapper, DaVirtualManPO>
        implements DaVirtualManManager {

    @Resource
    private ChannelUtil channelUtil;
    @Resource
    private DaVirtualManScenesManager daVirtualManScenesManager;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private DaTenantAuthManager daTenantAuthManager;
    @Resource
    private DaOldNewBizIdConfig daOldNewBizIdConfig;

    @Override
    public DaVirtualManPO info(Long bizId) {
        //优先判断该bizId是否有对应的新bizId，若有则查询新bizId的数据
        if (daOldNewBizIdConfig.getVmOldNewBizIdMap().containsKey(bizId)) {
            bizId = daOldNewBizIdConfig.getVmOldNewBizIdMap().get(bizId);
        }
        return this.getOne(Wrappers.lambdaQuery(DaVirtualManPO.class).eq(DaVirtualManPO::getBizId, bizId)
                .eq(DaVirtualManPO::getIsDeleted, Const.ZERO));
    }

    @Override
    public IPage<DaVirtualManDTO> pageVm(DaVirtualManPageRequestDTO param) {
        DaVirtualManQueryPO pageQuery = convert(param);
        Integer totalCount = this.baseMapper.pageVmCount(pageQuery);
        if (Objects.isNull(totalCount) || totalCount <= Const.ZERO) {
            return new Page(pageQuery.getPageIndex(), pageQuery.getPageSize(), Const.ZERO);
        }
        List<DaVirtualManBO> tmpResults = this.baseMapper.pageVm(pageQuery);

        List<Long> bizIds = tmpResults.stream().map(DaVirtualManBO::getBizId).collect(Collectors.toList());

        List<DaVirtualManScenesPO> scenesPOList = daVirtualManScenesManager
                .list(Wrappers.lambdaQuery(DaVirtualManScenesPO.class).in(DaVirtualManScenesPO::getVmBizId, bizIds)
                        .eq(DaVirtualManScenesPO::getIsDeleted, Const.ZERO));
        Map<Long, List<DaVirtualManScenesPO>> scenesPOMap = scenesPOList.stream()
                .collect(Collectors.groupingBy(DaVirtualManScenesPO::getVmBizId));

        List<DaTenantAuthPO> tenantAuthPOList = daTenantAuthManager
                .list(Wrappers.lambdaQuery(DaTenantAuthPO.class).in(DaTenantAuthPO::getBizId, bizIds)
                        .eq(DaTenantAuthPO::getBizType, DaTenantAuthBizTypeEnum.DIGITAL_MAN.getType())
                        .eq(DaTenantAuthPO::getIsDeleted, Const.ZERO));
        Map<Long, List<DaTenantAuthPO>> tenantAuthPOMap = tenantAuthPOList.stream()
                .collect(Collectors.groupingBy(DaTenantAuthPO::getBizId));

        Page<DaVirtualManDTO> page = new Page(pageQuery.getPageIndex(), pageQuery.getPageSize(), totalCount);
        page.setRecords(tmpResults.stream()
                .map(x -> this.convert(x, scenesPOMap.get(x.getBizId()), tenantAuthPOMap.get(x.getBizId())))
                .collect(Collectors.toList()));
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void vmAuth(String vmCode, Integer channel, List<String> authTenantCodeList) {
        DaVirtualManPO existVmPO = this.lambdaQuery().eq(DaVirtualManPO::getVmCode, vmCode)
                .eq(DaVirtualManPO::getChannel, channel).eq(DaVirtualManPO::getIsDeleted, Const.ZERO).one();
        Assert.notNull(existVmPO, String.format("数字人[%s]不存在", vmCode));

        List<DaTenantAuthPO> existTenantAuthPOList = daTenantAuthManager
                .list(Wrappers.lambdaQuery(DaTenantAuthPO.class).eq(DaTenantAuthPO::getBizId, existVmPO.getBizId())
                        .eq(DaTenantAuthPO::getBizType, DaTenantAuthBizTypeEnum.DIGITAL_MAN.getType())
                        .eq(DaTenantAuthPO::getIsDeleted, Const.ZERO));
        Map<String, DaTenantAuthPO> tenantAuthMap = existTenantAuthPOList.stream()
                .collect(Collectors.toMap(DaTenantAuthPO::getTenantCode, Function.identity(), (a, b) -> b));

        // 分为 授权、取消授权 两种逻辑
        List<String> needAuthTenantCodeList = Lists.newArrayList();
        List<DaTenantAuthPO> needUpdateList = Lists.newArrayList();
        authTenantCodeList.stream().forEach(x -> {
            if (!tenantAuthMap.containsKey(x)) {
                needAuthTenantCodeList.add(x);
            }
        });
        tenantAuthMap.entrySet().stream().forEach(entry -> {
            DaTenantAuthPO value = entry.getValue();
            if (!authTenantCodeList.contains(entry.getKey())) {
                // 当前租户不在需要授权的名单内
                if (Objects.equals(Const.ZERO, value.getIsDeleted())) {
                    value.setIsDeleted(Const.ONE);
                    needUpdateList.add(value);
                }
            }
        });
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            daTenantAuthManager.updateBatchById(needUpdateList.stream().map(x -> {
                DaTenantAuthPO updatePO = new DaTenantAuthPO();
                updatePO.setIsDeleted(x.getIsDeleted());
                updatePO.setId(x.getId());
                return updatePO;
            }).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(needAuthTenantCodeList)) {
            return;
        }

        List<DaTenantAuthPO> needAuthPOList = needAuthTenantCodeList.stream().map(x -> {
            DaTenantAuthPO tenantAuthPO = new DaTenantAuthPO();
            tenantAuthPO.setTenantCode(x);
            tenantAuthPO.setBizId(existVmPO.getBizId());
            tenantAuthPO.setIsDeleted(Const.ZERO);
            tenantAuthPO.setBizType(DaTenantAuthBizTypeEnum.DIGITAL_MAN.getType());
            return tenantAuthPO;
        }).collect(Collectors.toList());
        daTenantAuthManager.saveBatch(needAuthPOList);
    }

    private DaVirtualManQueryPO convert(DaVirtualManPageRequestDTO source) {
        if (source == null) {
            return null;
        }
        DaVirtualManQueryPO daVirtualManQueryPO = new DaVirtualManQueryPO();
        daVirtualManQueryPO.setVmCode(source.getVmCode());
        daVirtualManQueryPO.setVmName(source.getVmName());
        daVirtualManQueryPO.setChannel(source.getChannel());
        daVirtualManQueryPO.setPageIndex(source.getPageIndex());
        daVirtualManQueryPO.setPageSize(source.getPageSize());
        return daVirtualManQueryPO;
    }

    private DaVirtualManDTO convert(DaVirtualManBO source, List<DaVirtualManScenesPO> scenesList,
            List<DaTenantAuthPO> tenantAuthList) {
        if (source == null) {
            return null;
        }
        DaVirtualManDTO daVirtualManDTO = new DaVirtualManDTO();
        daVirtualManDTO.setId(source.getId());
        daVirtualManDTO.setBizId(source.getBizId());
        daVirtualManDTO.setTenantCode(source.getTenantCode());
        daVirtualManDTO.setVmCode(source.getVmCode());
        daVirtualManDTO.setVmName(source.getVmName());
        daVirtualManDTO.setVmVoiceKey(source.getVmVoiceKey());
        daVirtualManDTO.setVoiceBizId(source.getVoiceBizId());
        daVirtualManDTO.setHeadImg(source.getHeadImg());
        daVirtualManDTO.setGender(source.getGender());
        daVirtualManDTO.setVmType(source.getVmType());
        daVirtualManDTO.setEffectDt(source.getEffectDt());
        daVirtualManDTO.setExpiryDt(source.getExpiryDt());
        daVirtualManDTO.setChannel(source.getChannel());
        daVirtualManDTO.setIsEnabled(source.getIsEnabled());
        daVirtualManDTO.setEnableSpeed(source.getIsEnableSpeed());
        daVirtualManDTO.setDefaultSpeed(source.getDefaultSpeed());
        if (CollectionUtils.isNotEmpty(scenesList)) {
            daVirtualManDTO.setSceneNameList(String.join(",",
                    scenesList.stream().map(DaVirtualManScenesPO::getSceneName).collect(Collectors.toList())));
        }

        if (CollectionUtils.isNotEmpty(tenantAuthList)) {
            daVirtualManDTO.setAuthTenantCode(String.join(",",
                    tenantAuthList.stream().map(DaTenantAuthPO::getTenantCode).collect(Collectors.toList())));
        }
        return daVirtualManDTO;
    }
}




