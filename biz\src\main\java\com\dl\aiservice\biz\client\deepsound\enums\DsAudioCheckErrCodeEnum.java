package com.dl.aiservice.biz.client.deepsound.enums;

import java.util.Objects;

public enum DsAudioCheckErrCodeEnum {

    ERROR_CODE_0(0, "响应成功"),
    ERROR_CODE_1001(1001, "录音时长太短，小于 3s"),
    ERROR_CODE_1002(1002, "录音时长过长，大于 30s"),
    ERROR_CODE_1003(1003, "录音音量过小(Total RMS Amplitude < -35dB)"),
    ERROR_CODE_1004(1004, "录音音量过大(Total RMS Amplitude > -10dB)"),
    ERROR_CODE_1005(1005, "有较多喷麦情况(建议与手机麦克风保持 10cm 的距离)"),
    ERROR_CODE_1006(1006, "信噪比过低，小于 15dB"),
    ERROR_CODE_1007(1007, "语音识别出的文本与录音文本不匹配"),
    ERROR_CODE_1008(1008, "语音识别功能错误"),
    ERROR_CODE_1009(1009, "文件格式错误"),
    ERROR_CODE_1010(1010, "音频文件编码格式错误"),
    ERROR_CODE_400000(400000, "参数有误"),
    ERROR_CODE_400001(400001, "身份校验错误"),
    //  如果偶现，可以忽略；重复请联系 <EMAIL>
    ERROR_CODE_500000(500000, "系统内部错误"),
    // 如果偶现，可以忽略；重复请联系 <EMAIL>
    ERROR_CODE_500003(500003, "拒绝服务"),
    UNKNOWN(9999999, "未知异常");

    private final Integer errorCode;
    private final String errorDesc;

    DsAudioCheckErrCodeEnum(Integer errorCode, String errorDesc) {
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }

    public static String getErrorDesc(Integer errorCode) {
        for (DsAudioCheckErrCodeEnum wxErrorCodeEnum : values()) {
            if (Objects.equals(wxErrorCodeEnum.errorCode, errorCode)) {
                return wxErrorCodeEnum.errorDesc;
            }
        }
        return null;
    }

    public static DsAudioCheckErrCodeEnum errorCode(Integer code) {
        for (DsAudioCheckErrCodeEnum value : values()) {
            if (Objects.nonNull(code)) {
                if (value.getErrorCode().equals(code)) {
                    return value;
                }
            }
        }
        return UNKNOWN;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorDesc() {
        return errorDesc;
    }
}
