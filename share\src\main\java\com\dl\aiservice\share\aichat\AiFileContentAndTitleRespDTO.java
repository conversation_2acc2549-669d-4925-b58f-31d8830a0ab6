package com.dl.aiservice.share.aichat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-11 16:16
 */
@Data
@ApiModel("提取文件内容和标题-响应DTO")
public class AiFileContentAndTitleRespDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("文件标题")
    private String title;

    @ApiModelProperty("文件内容")
    private String content;

}
