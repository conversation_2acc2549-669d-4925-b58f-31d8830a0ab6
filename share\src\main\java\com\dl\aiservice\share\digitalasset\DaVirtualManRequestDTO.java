package com.dl.aiservice.share.digitalasset;

import com.dl.aiservice.share.common.BizIdRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName DaVirtualManRequestDTO
 * @Description
 * <AUTHOR>
 * @Date 2023/6/6 9:31
 * @Version 1.0
 **/
@Data
public class DaVirtualManRequestDTO extends BizIdRequestDTO {

    @Deprecated
    @ApiModelProperty(value = "当指定租户返回数据空时，是否返回Default租户下的数据")
    private boolean dlShow;

    @ApiModelProperty(value = "渠道列表")
    private List<Integer> channels;

    @ApiModelProperty(value = "判断是否查询is_enable=0的数据，默认null时仅查询值为1的数据")
    private Integer enableFilter;
}
