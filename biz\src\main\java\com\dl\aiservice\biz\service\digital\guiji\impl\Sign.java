package com.dl.aiservice.biz.service.digital.guiji.impl;

import sun.misc.BASE64Encoder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 通过
 * 1、appKey:44267b7032ee11eb91f2bf08db5c7bd7
 * 2、accessToken：9f745387cff24da797e2dbfe5db820b9
 * 3、timeStamp：1650425709
 * 求得
 * sign:jLWdxRwpJObwKTg2d1cRP6gI1uDxAvHdX57GsTOedA0%3D
 *
 * <AUTHOR>
 * @Title: Sign
 * @date 2022/5/56:56	 下午
 */
public class Sign {
    public static void main(String[] args) throws Exception {
        String appKey = "25025948332f4ab4a6a1757f03fc6cc5";
        String accessToken = "6e8f2fa99bb54769b5f592a142fd92ae";
        //秒级时间戳
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        System.out.println(timeStamp);
//        String timeStamp = "1650425709";
        //	 获取待计算签名的内容
        String signingContent = "appkey=" + appKey + "&timestamp=" + timeStamp;
        //	 计算 HMAC-SHA256	 值
        byte[] hmacSHA256Result = hmacSHA256(signingContent, accessToken);
        //base64	encode
        BASE64Encoder encoder = new BASE64Encoder();
        String base64EncodeResult = encoder.encode(hmacSHA256Result);
        //	URL	encode
        String urlEncodeResult = URLEncoder.encode(base64EncodeResult, "UTF-8");
        //最终 sign
        System.out.println(urlEncodeResult);
    }

    private static byte[] hmacSHA256(String data, String key) throws Exception {
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new
                SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        return sha256_HMAC.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }
}