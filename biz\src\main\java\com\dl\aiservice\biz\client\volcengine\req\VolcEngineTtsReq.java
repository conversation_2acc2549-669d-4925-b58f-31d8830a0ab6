package com.dl.aiservice.biz.client.volcengine.req;

import lombok.Data;

@Data
public class VolcEngineTtsReq {
    /**
     * 应用相关配置
     */
    private App app = new App();
    /**
     * 用户相关配置
     */
    private User user = new User();
    /**
     * 音频相关配置
     */
    private Audio audio = new Audio();
    /**
     * 请求相关配置
     */
    private Request request = new Request();

    @Data
    public static class App {
        /**
         * 应用标识
         */
        private String appid = "2299740059";
        /**
         * 应用令牌
         * 不可为空，传入值可以通过服务端日志追溯
         */
        private String token = "hrUeNUJF2quoF6MI9cwL3LQpmbwCNf1F";
        /**
         * volcano_tts，复刻需要根据使用的集群替换
         */
        private String cluster = "volcano_tts";
    }

    @Data
    public static class User {
        /**
         * 用户标识
         * 不可为空，传入值可以通过服务端日志追溯
         */
        private String uid;
    }

    @Data
    public static class Audio {
        /**
         * 音色类型
         * 通用合成音见https://www.volcengine.com/docs/6561/97465
         * 复刻音色使用声音ID(speaker id)
         */
        private String voice_type;

        /**
         * 音频采样率
         * 默认为 24000
         */
        private int rate = 24000;

        /**
         * 音频编码格式
         * wav / pcm / ogg_opus / mp3，火山引起默认为 pcm。我们默认用mp3。
         */
        private String encoding = "mp3";
        /**
         * 语速
         * [0.2,3]，默认为1，通常保留一位小数即可
         */
        private float speed_ratio = 1;
        /**
         * 音量
         * [0.1, 3]，默认为1，通常保留一位小数即可
         */
        private float volume_ratio = 1;
        /**
         * 音高
         * [0.1, 3]，默认为1，通常保留一位小数即可
         */
        private float pitch_ratio = 1;

        /*
        //情感/风格
        private String emotion;

        /*
        //语言类型
        private String language = "cn";*/
    }

    @Data
    public static class Request {
        /**
         * 需要保证每次调用传入值唯一，建议使用 UUID
         */
        private String reqid;
        /**
         * 合成语音的文本，长度限制 1024 字节（UTF-8编码）。复刻音色没有此限制，但是HTTP接口有60s超时限制
         */
        private String text;
        /**
         * 文本类型
         * plain / ssml, 默认为plain
         */
        private String text_type = "plain";
        /**
         * 操作 query（非流式，http只能query） / submit（流式）
         */
        private String operation = "query";

        /**
         * 时间戳相关
         * 当with_frontend为1且frontend_type为unitTson的时候，返回音素级时间戳
         */
        private int with_frontend;
        /**
         * 时间戳相关
         * 当with_frontend为1且frontend_type为unitTson的时候，返回音素级时间戳
         */
        private int frontend_type;
        /**
         * 时间戳相关
         * 新版时间戳参数，可用来替换with_frontend和frontend_type，可返回原文本的时间戳，而非TN后文本，即保留原文中的阿拉伯数字或者特殊符号等。注意：原文本中的多个标点连用或者空格依然会被处理，但不影响时间戳连贯性
         */
        private int with_timestamp;

        /**
         * 复刻音色语速优化
         * 仅当使用复刻音色时设为1，可优化语速过快问题。有可能会导致时间戳多次返回。
         */
        private int split_sentence;

        /**
         * 英文前端优化
         * 当pure_english_opt为1的时候，中文音色读纯英文时可以正确处理文本中的阿拉伯数字
         */
        private int pure_english_opt;

    }
}