package com.dl.aiservice.biz.client.guiji.resp;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class GJUserResponse implements Serializable {

    private static final long serialVersionUID = -4008346018428112091L;

    /**
     * 用户ID
     */
    private String id;
    /**
     * 用户名
     */
    private String username;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 企业ID
     */
    private String corpId;
    /**
     * 企业名称
     */
    private String corpName;
    /**
     * 一级组织ID
     */
    private String primaryOrgId;
    /**
     * 一级组织名称
     */
    private String primaryOrgName;
    /**
     * 二级组织ID
     */
    private String secondaryOrgId;
    /**
     * 二级组织名称
     */
    private String secondaryOrgName;
    /**
     * 角色ID
     */
    private String roleId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邮箱地址
     */
    private String email;
}

