ALTER TABLE `da_virtual_man`
DROP COLUMN `tenant_code`;


ALTER TABLE `da_virtual_voice`
DROP COLUMN `tenant_code`;


CREATE TABLE `da_tenant_auth` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tenant_code` varchar(100) NOT NULL COMMENT '租户编号',
  `biz_id` bigint NOT NULL COMMENT '数字资产业务id',
  `biz_type` tinyint NOT NULL COMMENT '数字资产类型，1-数字人，2-声音',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '修改时间',
  `delete_dt` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_tenantCode` (`tenant_code`) USING BTREE,
  KEY `idx_bizId` (`biz_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='数字资产租户授权表';