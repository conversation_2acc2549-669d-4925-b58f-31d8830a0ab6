package com.dl.aiservice.biz.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.aiservice.biz.common.annotation.BaseDao;
import com.dl.aiservice.biz.dal.po.DaVirtualManSceneQueryPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManScenesPO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManScenesBO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【da_virtual_man_scenes(数字资产-仿真人场景信息表)】的数据库操作Mapper
 * @createDate 2023-06-02 13:53:14
 * @Entity com.dl.aiservice.biz.dal.po.DaVirtualManScenes
 */
@BaseDao
public interface DaVirtualManScenesMapper extends BaseMapper<DaVirtualManScenesPO> {

    /**
     * 根据数字人bizId，或者租户编码查询数字人场景列表
     *
     * @return
     */
    List<DaVirtualManScenesBO> listVirtualManScene(@Param("bizIdList") List<Long> bizIdList,
            @Param("enableFilter") Integer enableFilter);

    /**
     * 根据数字人场景id列表和渠道列表查询数字人场景列表
     *
     * @return
     */
    List<DaVirtualManScenesBO> listVirtualManSceneBySceneIds(@Param("sceneIdList") List<String> sceneIdList,
            @Param("channelList") List<Integer> channelList, @Param("enableFilter") Integer enableFilter);

    Integer countVirtualManScene(DaVirtualManSceneQueryPO query);

    List<DaVirtualManScenesBO> pageVirtualManScene(DaVirtualManSceneQueryPO query);
}




