package com.dl.aiservice.biz.client.ivh.intercepter;

import com.dl.aiservice.biz.client.ivh.IvhDigitalClient;
import com.dl.aiservice.biz.client.ivh.enums.IvhErrCodeEnum;
import com.dl.aiservice.biz.client.ivh.resp.IvhBaseResponse;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.Md5Util;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;

@Slf4j
public class IvhDigitalInterceptor implements Interceptor {

    @SneakyThrows
    @Override
    public boolean beforeExecute(ForestRequest request) {
        AiConfig config = ApplicationContextUtils.getContext().getBean(AiConfig.class);
        ChannelUtil channelUtil = ApplicationContextUtils.getContext().getBean(ChannelUtil.class);
        Integer channel = channelUtil.getChannel();

        String appKey = null;
        String accessToken = null;
        if (channel.equals(ServiceChannelEnum.FUJIA_IVH.getCode())) {
            appKey = config.getFujiaIvhVideoKey();
            accessToken = config.getFujiaIvhVideoSecret();
        } else if (channel.equals(ServiceChannelEnum.DLSY_IVH.getCode())) {
            appKey = config.getDlsyIvhVideoKey();
            accessToken = config.getDlsyIvhVideoSecret();
        } else {
            appKey = config.getIvhVideoKey();
            accessToken = config.getIvhVideoSecret();
        }

        //秒级时间戳
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        request.addQuery(IvhDigitalClient.APP_KEY, appKey);
        request.addQuery(IvhDigitalClient.TIMESTAMP, timeStamp);
        String signingContent = "appkey=" + appKey + "&timestamp=" + timeStamp;
        String signature = URLEncoder
                .encode(Md5Util.base64Encode(Md5Util.hmacSHA256(signingContent, accessToken)), "UTF-8");
        request.addQuery(IvhDigitalClient.SIGNATURE, signature);
        log.info("before execute:\nrequest: {}", request.getBody().nameValuesMapWithObject());
        return Boolean.TRUE;
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("after execute:\nrequest: {}\nresponse: {}", request.getBody().nameValuesMapWithObject(), response.getContent());
    }

    /**
     * 成功判断方式
     *
     * @param data
     * @param request
     * @param response
     */
    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        if (isIgnore(request)) {
            return;
        }
        fillErrMsg(data);
    }

    private void fillErrMsg(Object data) {
        if (!(data instanceof IvhBaseResponse)) {
            return;
        }
        IvhBaseResponse resp = (IvhBaseResponse) data;
        IvhErrCodeEnum errCodeEnum = IvhErrCodeEnum.errorCode(resp.getHeader().getCode());
        if (errCodeEnum != IvhErrCodeEnum.ERROR_CODE_0) {
            throw BusinessServiceException.getInstance(errCodeEnum.getErrorCode().toString(), errCodeEnum.getErrorDesc());
        }
    }

    private boolean isIgnore(ForestRequest request) {
        String url = request.getMethod().getMetaRequest().getUrl();
        if (StringUtils.equals(IvhDigitalClient.VIDEO_PATH, url)) {
            return Boolean.TRUE;
        }
        if (StringUtils.equals(IvhDigitalClient.TTS_PATH, url)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
