package com.dl.aiservice.share.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * 数字人渠道枚举类，属于ServiceChannelEnum的子集
 */
public enum DigitalManChannelEnum {
    /**
     * 新华智云
     */
    XHZY(0, "XHZY", "新华智云"),
    /**
     * 硅基智能
     */
    GUI_JI(1, "GUI_JI", "硅基智能"),
    /**
     * 腾讯云数智人
     */
    IVH(2, "IVH", "腾讯云数智人"),
    /**
     * 火山引擎/字节跳动
     */
    VOLC_ENGINE(6, "VOLC_ENGINE", "火山引擎"),
    /**
     * 科大讯飞
     */
    IFLY_TEK(8, "IFLY_TEK", "科大讯飞"),
    /**
     * 阿里云数字人，克隆音
     */
    ALIYUN_DIGIITAL(9, "ALIYUN_DIGIITAL", "阿里云数字人"),

    /**
     * HeyGen数字人
     */
    HeyGen(10, "HeyGen", "HeyGen数字人"),
    /**
     * 华为云
     */
    HUAWEI(12, "HUAWEI", "华为云"),
    /**
     * 孚嘉科技的腾讯云数智人
     * 孚嘉是一个客户，他们有自己的腾讯云数智人账号
     */
    FUJIA_IVH(14, "FUJIA_IVH", "孚嘉科技的腾讯云数智人"),
    /**
     * 创金合信的硅基智能
     * 创金合信是一个客户，他们有自己的硅基智能账号
     */
    CJHX_GUIJI(15, "CJHX_GUIJI", "创金合信的硅基智能"),

    /**
     * 定力数影的腾讯云数智人
     */
    DLSY_IVH(16, "DLSY_IVH", "定力数影的腾讯云数智人"),

    /**
     *
     */
    CHAN_JING(18, "CHAN_JING", "蝉镜"),;

    private Integer code;
    private String desc;
    private String name;

    DigitalManChannelEnum(Integer code, String desc, String name) {
        this.code = code;
        this.desc = desc;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getName() {
        return name;
    }

    public static DigitalManChannelEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> Objects.equals(code, e.getCode())).findAny().orElse(null);
    }
}
