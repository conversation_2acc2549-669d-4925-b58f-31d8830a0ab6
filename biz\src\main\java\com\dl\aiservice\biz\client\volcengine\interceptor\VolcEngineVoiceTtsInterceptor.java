package com.dl.aiservice.biz.client.volcengine.interceptor;

import com.dl.aiservice.biz.client.volcengine.config.VolcEngineVoiceConfig;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;

/**
 * 火山引擎TTS拦截器
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-26 14:09
 */
@Slf4j
public class VolcEngineVoiceTtsInterceptor implements Interceptor {

    @Override
    public boolean beforeExecute(ForestRequest request) {
        VolcEngineVoiceConfig config = ApplicationContextUtils.getContext().getBean(VolcEngineVoiceConfig.class);
        request.addHeader("Authorization", "Bearer; " + config.getAccessToken());
        log.info("VolcEngineVoiceTtsInterceptor before execute:\nrequest: {}",
                request.getBody().nameValuesMapWithObject());
        return Boolean.TRUE;
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        //此处不能打印日志，因为tts音频是base64格式的，太长了
        /*log.info("VolcEngineVoiceTtsInterceptor after execute:\nrequest: {},\nhttpStatus:{},\nresponse: {}",
                request.getBody().nameValuesMapWithObject(), response.getStatusCode(), response.getContent());*/
    }

    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {

    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        log.error("VolcEngineVoiceTtsInterceptor error:\nrequest: {}", request.getBody().nameValuesMapWithObject());
        Interceptor.super.onError(ex, request, response);

        //获取请求响应状态码
        int status = response.getStatusCode();
        //不为200
        if (!Const.TWO_HUNDREDS.equals(status)) {
            log.error("VolcEngineVoiceTtsInterceptor http status is {},,,\nresponse: {}", status,
                    response.getContent());
        }

    }
}
