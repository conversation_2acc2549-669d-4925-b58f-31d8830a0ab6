package com.dl.aiservice.biz.manager.enums;

/**
 * 媒体合成记录的超时状态枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-14 09:44
 */
public enum MediaProduceJobTimeoutStatusEnum {

    UN(0, "未超时"),
    TIMEOUT(1, "超时,外部未回调"),
    TIMEOUT_SUCCESS(2, "超时,外部回调成功"),
    TIMEOUT_FAIL(3, "超时,外部回调失败");

    private Integer status;

    private String desc;

    MediaProduceJobTimeoutStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
