package com.dl.aiservice.share.videoproduce.dto.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @describe: Effect
 * @author: zhousx
 * @date: 2023/2/11 11:30
 */
@Data
public class EffectDTO {
    @JsonProperty("Type")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String type;

    @JsonProperty("SubType")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String subType;

    @JsonProperty("X")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double x;

    @JsonProperty("Y")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double y;

    @JsonProperty("FixedX")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double fixedX;

    @JsonProperty("FixedY")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double fixedY;

    @JsonProperty("In")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double in;

    @JsonProperty("Out")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double out;

    @JsonProperty("TimelineIn")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double timelineIn;

    @JsonProperty("TimelineOut")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double timelineOut;

    @JsonProperty("Content")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String content;

    @JsonProperty("Font")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String font;

    @JsonProperty("FontColor")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String fontColor;

    @JsonProperty("FontSize")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer fontSize;

    @JsonProperty("FixedFontSize")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer fixedFontSize;

    @JsonProperty("FontColorOpacity")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String fontColorOpacity;

    @JsonProperty("FontFace")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private FontFaceDTO fontFace;

    @JsonProperty("Spacing")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer spacing;

    @JsonProperty("Angle")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double angle;

    @JsonProperty("BorderStyle")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer borderStyle;

    @JsonProperty("Outline")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer outline;

    @JsonProperty("OutlineColour")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String outlineColour;

    @JsonProperty("Shadow")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer shadow;

    @JsonProperty("BackColor")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String backColor;

    @JsonProperty("Alignment")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String alignment;

    @JsonProperty("AdaptMode")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String adaptMode;

    @JsonProperty("TextWidth")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer textWidth;

    @JsonProperty("EffectColorStyle")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String effectColorStyle;

    @JsonProperty("AaiMotionInEffect")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String aaiMotionInEffect;

    @JsonProperty("AaiMotionIn")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double aaiMotionIn;

    @JsonProperty("AaiMotionOut")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double aaiMotionOut;

    @JsonProperty("Ratio")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double ratio;

    @JsonProperty("AaiMotionOutEffect")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String aaiMotionOutEffect;

    @JsonProperty("AaiMotionLoopEffect")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String aaiMotionLoopEffect;

    @JsonProperty("Left")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double left;

    @JsonProperty("Right")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double right;

    @JsonProperty("Top")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double top;

    @JsonProperty("Bottom")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double bottom;

    @JsonProperty("Color")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String color;

    @JsonProperty("Radius")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double radius;

    @JsonProperty("Duration")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double duration;

    @JsonProperty("ExtParams")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String extParams;

    @JsonProperty("Gain")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double gain;

    @JsonProperty("Curve")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String curve;

    @JsonProperty("Degree")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer degree;

    @JsonProperty("Auto")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer auto;

    @JsonProperty("Thres")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double thres;

    @JsonProperty("ClipDuration")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double clipDuration;

    @JsonProperty("Direction")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String direction;
}
