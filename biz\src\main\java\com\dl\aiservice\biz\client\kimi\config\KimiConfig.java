package com.dl.aiservice.biz.client.kimi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;

import static com.dl.aiservice.biz.common.constant.Const.ONE_ZERO_TWO_FOUR;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 11:42
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dl.kimi")
public class KimiConfig {

    private String apikey;

    /**
     * 目前是 moonshot-v1-8k,moonshot-v1-32k,moonshot-v1-128k 其一
     */
    private String model;

    /**
     * 响应的最大token数
     */
    private Integer respMaxToken;

    public Integer getRespMaxToken() {
        //响应的最大token数，默认为1024
        return Objects.nonNull(respMaxToken) ? respMaxToken : ONE_ZERO_TWO_FOUR;
    }
}
