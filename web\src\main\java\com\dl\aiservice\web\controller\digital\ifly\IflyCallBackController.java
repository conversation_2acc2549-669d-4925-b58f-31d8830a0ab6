package com.dl.aiservice.web.controller.digital.ifly;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.client.Ifly.enums.IflyErrCodeEnum;
import com.dl.aiservice.biz.client.Ifly.req.IflyBaseRequest;
import com.dl.aiservice.biz.client.Ifly.resp.IflyVideoQueryResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.enums.MediaProduceJobTimeoutStatusEnum;
import com.dl.aiservice.biz.register.DigitalHelper;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.aiservice.biz.service.digital.ifly.IflyDigitalService;
import com.dl.aiservice.share.enums.MediaProduceJobStatusEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description :
 */
@Slf4j
@RestController
@RequestMapping("/ifly")
public class IflyCallBackController {

    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private DigitalHelper digitalHelper;
    @Resource
    private IflyDigitalService iflyDigitalService;

    /**
     * 合成任务回调接口
     */
    @PostMapping("/callback")
    public void videoCreateCallBack(@RequestBody IflyBaseRequest<IflyVideoQueryResponse> callback) {
        log.info("科大讯飞回调，回调参数：{}", JSONUtil.toJsonStr(callback));
        MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.getOne(Wrappers.<MediaProduceJobPO>lambdaQuery()
                .eq(MediaProduceJobPO::getExtJobId, callback.getParam().getTaskId()));
        if (Objects.isNull(mediaProduceJobPO)) {
            return;
        }

        this.fillMediaProduceJobStatus(callback.getParam(), mediaProduceJobPO);

        if (callback.getParam().getStatus().equals(Const.ONE)) {
            if (mediaProduceJobPO.getStatus().equals(Const.ZERO)) {
                return;
            }
            iflyDigitalService.buildFileUrl(callback.getParam());
        } else {
            mediaProduceJobPO.setStatus(MediaProduceJobStatusEnum.FAIL.getStatus());
            mediaProduceJobPO.setFailCode(IflyErrCodeEnum.UNKNOWN.getErrorCode());
            mediaProduceJobPO.setFailReason("合成失败，错误未知");
            mediaProduceJobPO.setResponseDt(new Date());
            mediaProduceJobManager.updateById(mediaProduceJobPO);
        }
        DigitalVideoCallbackDTO digitalCallbackDTO = new DigitalVideoCallbackDTO();
        BeanUtils.copyProperties(mediaProduceJobPO, digitalCallbackDTO);
        digitalHelper.get(ServiceChannelEnum.getByCode(mediaProduceJobPO.getChannel()))
                .callBackBiz(mediaProduceJobPO.getTenantCode(), mediaProduceJobPO.getWorksBizId(),
                        mediaProduceJobPO.getCallbackUrl(), digitalCallbackDTO, JSONUtil.toJsonStr(callback));
    }

    private void fillMediaProduceJobStatus(IflyVideoQueryResponse IflyVideoQueryResponse, MediaProduceJobPO mediaProduceJobPO) {

        //先判断是否已经超时，若已超时，则修改timeout_status，不处理status
        if (!MediaProduceJobTimeoutStatusEnum.UN.getStatus().equals(mediaProduceJobPO.getTimeoutStatus())) {
            if (IflyVideoQueryResponse.getStatus().equals(Const.ONE)) {
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_SUCCESS.getStatus());
            } else {
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_FAIL.getStatus());
            }
        }
    }

}
