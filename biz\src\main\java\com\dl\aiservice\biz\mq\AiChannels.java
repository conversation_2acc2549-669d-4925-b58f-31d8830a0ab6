package com.dl.aiservice.biz.mq;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;

/**
 * @describe: AiChannels
 * @author: zhousx
 * @date: 2023/5/16 13:58
 */
public interface AiChannels {

    /*****************消费者***************/
    //已下线 2024-03-13
    /*@Input("digitalmanconsumer")
    SubscribableChannel digitalmanconsumer();*/
    @Input("digitalprogressconsumer")
    SubscribableChannel digitalprogressconsumer();

    /**
     * 声音训练进度消息消费者
     *
     * @return
     */
    @Input("voicetrainprogressconsumer")
    SubscribableChannel voicetrainprogressconsumer();

    /*****************生产者***************/
    //已下线 2024-03-13
    /*@Output("digitalmanproducer")
    MessageChannel digitalmanproducer();*/
    @Output("digitalprogressproducer")
    MessageChannel digitalprogressproducer();

    /**
     * 声音训练进度消息生产者
     *
     * @return
     */
    @Output("voicetrainprogressproducer")
    MessageChannel voicetrainprogressproducer();
}
