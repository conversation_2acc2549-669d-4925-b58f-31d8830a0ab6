package com.dl.aiservice.biz.service.digital.dto.resp;

import com.dl.aiservice.biz.service.digital.dto.resp.guiji.GJRobotResponseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class TrainResponseDTO implements Serializable {


    private static final long serialVersionUID = 788186056690114510L;

    //**************************************硅基参数参数**********************************************


    /**
     * ID
     */
    private String id;
    /**
     * 标题
     */
    @ApiModelProperty(value = "硅基：标题")
    private String title;
    /**
     * 提交的待训练视频地址
     */
    @ApiModelProperty(value = "硅基：提交的待训练视频地址")
    private String videoUrl;
    /**
     * 训练状态 0或空-准备中 1训练中 2训练成功 3训练失败 4审核不通过
     */
    @ApiModelProperty(value = "硅基：训练状态 0或空-准备中 1训练中 2训练成功 3训练失败 4审核不通过")
    private Integer status;
    /**
     * 回调状态 0或空-初始态 1-回调成功 2-回调失败
     */
    @ApiModelProperty(value = "硅基：回调状态 0或空-初始态 1-回调成功 2-回调失败")
    private Integer callbackStatus;
    /**
     * 备注
     */
    @ApiModelProperty(value = "硅基：备注")
    private String comment;
    /**
     * 训练完成模特ID
     */
    @ApiModelProperty(value = "硅基：训练完成模特ID")
    private Integer robotId;
    /**
     * 训练完成场景ID
     */
    @ApiModelProperty(value = "硅基：训练完成场景ID")
    private Integer sceneId;
    /**
     * 模特封面地址
     */
    @ApiModelProperty(value = "硅基：模特封面地址")
    private String coverUrl;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "硅基：创建时间")
    private String createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "硅基：更新时间")
    private String updateTime;

    /**
     * 训练完成后的模特详情
     */
    @ApiModelProperty(value = "硅基：训练完成后的模特详情")
    private GJRobotResponseDTO robotResponseDTO;


    //**************************************腾讯云参数参数**********************************************


//    /**
//     * 制作状态：
//     * MAKING：进⾏中
//     * SUCCESS：制作成功
//     * FAIL：制作失败
//     */
//    private String status;
    /**
     * 当 Status：MAKING时，任务进⾏中所处的阶段：
     * EXAMINE：视频审核中
     * TRAIN：模型训练中
     * 当 Status：SUCCESS时，任务进⾏中所处的阶段：
     * END：完成
     */
    @ApiModelProperty(value = "腾讯云：中所处的阶段")
    private String stage;
    /**
     * 制作失败返回的失败原因，便于排查问题
     */
    @ApiModelProperty(value = "腾讯云： 制作失败返回的失败原因，便于排查问题")
    private String failMessage;

    /**
     * 处于某个Stage的额外信息，⽬前仅在Stage：END时，返回授权信息（待定）
     */
    @ApiModelProperty(value = "腾讯云：处于某个Stage的额外信息，⽬前仅在Stage：END时，返回授权信息（待定）")
    private String stageInfo;

    /**
     * 定制管线id，⽤于排查问题，业务⽆须关注
     */
    @ApiModelProperty(value = "腾讯云：定制管线id，⽤于排查问题，业务⽆须关注")
    private String stageId;
}

