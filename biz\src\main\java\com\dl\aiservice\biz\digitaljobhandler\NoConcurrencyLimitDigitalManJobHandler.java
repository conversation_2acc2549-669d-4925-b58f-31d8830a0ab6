package com.dl.aiservice.biz.digitaljobhandler;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 无并发限制的数字人任务处理器
 * <p>
 * 这里的"无并发限制"指的是我方不用做并发限制，数字人厂商内部有队列，直接都提交给他们即可
 * <p>
 * 该数字人任务处理器作为默认处理器
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-12 14:45
 */
@Component
public class NoConcurrencyLimitDigitalManJobHandler extends AbstractDigitalManJobHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(NoConcurrencyLimitDigitalManJobHandler.class);

    /**
     * 任务队列的key
     */
    private static final String NO_CURRENCY_LIMIT_DM_JOB_QUEUE_KEY = "No_Currency_Limit_DM_Job_Queue";

    @Autowired
    private MediaProduceJobManager mediaProduceJobManager;

    @Override
    public ServiceChannelEnum supportChannel() {
        return null;
    }

    @Override
    public String getQueueKey() {
        return NO_CURRENCY_LIMIT_DM_JOB_QUEUE_KEY;
    }

    @Override
    public void handleDmVideoCreate() {
        //1.从队列中获取数字人合成请求
        CreateRequestDTO dmRequestDTO = super.popFromRedis();
        if (Objects.isNull(dmRequestDTO)) {
            LOGGER.info("无并发限制的数字人任务队列中暂无合成请求。");
            return;
        }
        LOGGER.info("获取到数字人合成请求dmRequestDTO:{}", JSONUtil.toJsonStr(dmRequestDTO));

        //2.校验数字人任务状态，非待合成状态不处理
        MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.lambdaQuery()
                .eq(MediaProduceJobPO::getId, dmRequestDTO.getUpdateId()).one();
        if (!Objects.equals(mediaProduceJobPO.getStatus(), Const.TWO)) {
            LOGGER.warn("数字人合成任务非待合成状态，不处理。dmRequestDTO={}", JsonUtils.toJSON(dmRequestDTO));
            return;
        }

        //3.合成数字人视频
        super.doDigitalManVideoCreate(dmRequestDTO);
    }
}
