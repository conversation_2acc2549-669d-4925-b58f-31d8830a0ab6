package com.dl.aiservice.share.videoproduce.dto.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @describe: FECanvas
 * @author: zhousx
 * @date: 2023/2/11 11:23
 */
@Data
public class FECanvasDTO {
    @JsonProperty("Width")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double width = 253.125;

    @JsonProperty("Height")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double height = 450.0;
}
