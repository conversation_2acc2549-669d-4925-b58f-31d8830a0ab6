package com.dl.aiservice.share.voiceclone;

import com.dl.framework.core.controller.param.AbstractPageParam;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-06 16:33
 */
@Data
public class VoiceTrainResultPageQueryDTO extends AbstractPageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 训练名
     */
    private String name;

    /**
     * 厂商训练模型编号
     */
    private String extModelCode;

    /**
     * 厂商，3-深声科技（线上训练），6-火山引擎
     */
    private Integer channel;

    /**
     * 训练状态：1 训练中；0 训练完成；-1 训练失败
     */
    private Integer status;

}
