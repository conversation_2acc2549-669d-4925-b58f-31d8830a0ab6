package com.dl.aiservice.biz.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.aiservice.biz.common.po.BasePO;
import lombok.Data;

import java.util.Date;

/**
 * 数字资产-数字声音信息表
 *
 * @TableName da_virtual_voice
 */
@TableName(value = "da_virtual_voice")
@Data
public class DaVirtualVoicePO extends BasePO {
    private static final long serialVersionUID = 8420260569849929820L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 声音唯一标识
     */
    @TableField(value = "biz_id")
    private Long bizId;

    /**
     * 租户代码
     */
    @TableField(value = "tenant_code")
    private String tenantCode;

    /**
     * 渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    @TableField(value = "channel")
    private Integer channel;

    /**
     * 外部厂商声音唯一标识
     */
    @TableField(value = "voice_key")
    private String voiceKey;

    /**
     * 声音名称
     */
    @TableField(value = "voice_name")
    private String voiceName;

    /**
     * 性别：1 男 ；2 女
     */
    @TableField(value = "gender")
    private Integer gender;

    /**
     * 描述
     */
    @TableField(value = "voice_desc")
    private String voiceDesc;

    /**
     * 1 克隆音；2 合成音
     */
    @TableField(value = "voice_type")
    private Integer voiceType;

    /**
     * 默认：通用
     */
    @TableField(value = "voice_category")
    private String voiceCategory;

    /**
     * 是否启用 0：否，1：是
     */
    @TableField(value = "is_enabled")
    private Integer isEnabled;

    /**
     * 是否删除 0：否，1：是
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;

    /**
     * 试听链接
     */
    @TableField(value = "sample_link")
    private String sampleLink;

    /**
     * 生效日期
     */
    @TableField(value = "effect_dt")
    private Date effectDt;

    /**
     * 失效日期
     */
    @TableField(value = "expiry_dt")
    private Date expiryDt;

    /**
     * 最大声音试听链接
     */
    @TableField(value = "max_voice_link")
    private String maxVoiceLink;

    /**
     * 建议音量
     */
    @TableField(value = "volume")
    private String volume;

    /**
     * 建议语速
     */
    @TableField(value = "speed")
    private String speed;

    /**
     * 语音头像
     */
    @TableField(value = "head_img")
    private String headImg;

    /**
     * 音频时长(单位：毫秒)
     */
    @TableField(value = "duration")
    private Long duration;

    /**
     * 多试听链接,json字符串,多个以,分割
     */
    @TableField(value = "voice_links")
    private String voiceLinks;
}