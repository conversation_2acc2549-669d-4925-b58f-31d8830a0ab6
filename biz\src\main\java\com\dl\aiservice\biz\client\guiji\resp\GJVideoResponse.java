package com.dl.aiservice.biz.client.guiji.resp;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class GJVideoResponse implements Serializable {

    private static final long serialVersionUID = -8463215353527554349L;
    /**
     * 提交的任务ID
     */
    private Integer id;
    /**
     * 视频名称
     */
    private String videoName;
    /**
     * 视频格式
     */
    private String videoFormat;
    /**
     * -1. 编辑中 1. 排队中 2. 合成中 3：合成成功 4：合成失败 5. 归档 6. 任务取消 7. 任务失败
     */
    private Integer synthesisStatus;
    /**
     * 合成视频的URL
     */
    private String videoUrl;
    /**
     * 字幕文件URL, ‘srtFlag’为1时才有值
     */
    private String srtUrl;
    /**
     * 水平分辨率
     */
    private Integer horizontal;
    /**
     * 垂直分辨率
     */
    private Integer vertical;
    /**
     * 时长
     */
    private String duration;
    /**
     * 文件尺寸
     */
    private String videoSize;
    /**
     * 封面图地址
     */
    private String coverUrl;
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 失败原因
     */
    private String failReason;

}

