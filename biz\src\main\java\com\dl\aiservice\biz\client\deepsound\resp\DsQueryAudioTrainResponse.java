package com.dl.aiservice.biz.client.deepsound.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName DsAudioCheckResponse
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 15:47
 * @Version 1.0
 **/
@Data
public class DsQueryAudioTrainResponse extends DsBaseResponse {

    private static final long serialVersionUID = -255119982193376160L;

    /**
     * 训练阶段，取值说明如下： "INQUEUE"-排队中； "TRAINING"-训练中； "FINISHED"-任务结束
     */
    @JsonProperty("stage")
    private String stage;

    /**
     * 训练状态，取值说明如下： 0-成功； 1-任务处理中（表示 stage 处于“INQUEUE”和 “TRAINING”两个阶段时 的状态）;-1-失败。
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * 业务方对当前提交音色的唯 一编号
     */
    @JsonProperty("record_id")
    private String recordId;

    /**
     * 服务内部对当前提交音色的 唯一编号
     */
    @JsonProperty("business_id")
    private String businessId;

    /**
     * 音色编号，仅当 status=0 时，该字段不会空
     */
    @JsonProperty("voice_name")
    private String voiceName;

    /**
     * 预计排队剩余时长，单位： 秒 ； 当 stage=INQUEUE 时，该 值有意义；stage 为非 INQUEUE 状态 时，该值为 0
     */
    @JsonProperty("estimated_start_time")
    private Integer estimatedStartTime;

    /**
     * 预计训练剩余时长，单位： 秒 ； 当 stage=TRAINNING 时， 该值有意义，stage 为非 TRAINNING 状态时，该值 为 0; 需注意预计训练剩余时长中 不包含排队时间。
     */
    @JsonProperty("estimated_finish_time")
    private Integer estimatedFinishTime;

    /**
     * 训练服务内部错误代码，0 表示成功，其他表示失败
     */
    @JsonProperty("error_code")
    private Integer errorCode;

}
