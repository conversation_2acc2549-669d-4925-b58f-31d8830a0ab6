package com.dl.aiservice.biz.manager.train.bo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-28 17:24
 */
@Data
public class TrainResultBaseInfoSaveBO {

    /**
     * 训练名
     */
    private String name;

    @NotNull
    private Integer channel;

    /**
     * 训练类型：0 数字人训练；1 声音训练
     */
    @NotNull
    private Integer trainType;

    /**
     * 厂商训练模型编号
     */
    @NotNull
    private String extModelCode;

    private String tenantCode;

    private Integer status;

    /**
     * 已训练次数。训练成功+训练中
     */
    private Integer trainedNum;

}
