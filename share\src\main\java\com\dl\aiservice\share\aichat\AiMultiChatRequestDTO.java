package com.dl.aiservice.share.aichat;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-13 11:06
 */
@Getter
@Setter
public class AiMultiChatRequestDTO implements Serializable {
    private static final long serialVersionUID = -7450686937522124613L;

    /**
     * 聊天用户id
     */
    @NotNull(message = "userId不能为空")
    private Long userId;

    /**
     * 消息列表
     * 请调用方自行保证顺序
     */
    @NotEmpty(message = "消息列表不能为空")
    private List<AiMultiChatMessageDTO> messages;

    /**
     * ai模型
     * 支持上层业务指定
     * 非必填
     */
    private String model;

    /**
     * 响应的最大token数
     * 支持上层业务指定
     * 非必填
     */
    private Integer respMaxToken;

}
