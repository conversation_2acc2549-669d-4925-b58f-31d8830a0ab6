package com.dl.aiservice.biz.service.digital.guiji;

import com.dl.aiservice.biz.service.digital.BaseDigitalService;
import com.dl.aiservice.biz.service.digital.dto.req.VideoCreate3DRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.VideoCreate3DTssRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.FreeRobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.SpeakersResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.TrainResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.UserInfoResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.UserResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.VideoCreateResponseDTO;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.framework.common.model.ResultPageModel;

/**
 * @author: xuebin
 * @description 数字人针对硅基特有的接口
 * @Date: 2023/2/28 17:50
 */
public interface GJDigitalService extends BaseDigitalService {

    /**
     * 获取token接口
     */
    String getAccessToken(Integer channel);

    /**
     * 硅基：企业所有发音人列表
     *
     * @return 返回结果
     */
    SpeakersResponseDTO allSpeaker();


    /**
     * 硅基免费模特列表查询
     *
     * @return 返回结果
     */
    FreeRobotResponseDTO robotFreeList();

    /**
     * 查询合成视频作品列表
     *
     * @param pageRequestDTO 分页条件
     * @return 返回结果
     */
    ResultPageModel<ProgressResponseDTO> videoPageList(PageRequestDTO pageRequestDTO);

    /**
     * 查询用户信息
     *
     * @return 返回结果
     */
    UserInfoResponseDTO getUser();


    /**
     * 查询企业下所有用户列表
     *
     * @return 返回结果
     */
    ResultPageModel<UserResponseDTO> allUserList(PageRequestDTO pageRequestDTO);


    /**
     * 创建3D视频合成任务
     *
     * @return 返回结果
     */
    VideoCreateResponseDTO create3D(VideoCreate3DRequestDTO request);


    /**
     * 创建3D视频合成任务
     *
     * @return 返回结果
     */
    VideoCreateResponseDTO create3DTss(VideoCreate3DTssRequestDTO request);

    /**
     * 训练任务列表查询
     *
     * @return 返回结果
     */
    ResultPageModel<TrainResponseDTO> trainPageList(PageRequestDTO pageRequestDTO);

    /**
     * 将视频转存到腾讯云cos
     *
     * @param mediaJobId
     * @param videoUrl
     * @return
     */
    String transfertToCos(Long mediaJobId, String videoUrl);
}

