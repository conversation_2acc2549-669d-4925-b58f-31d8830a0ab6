package com.dl.aiservice.biz.service.digital.dto.resp;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class DigitalTrainCallbackDTO implements Serializable {

    private static final long serialVersionUID = 6660662421497170627L;

    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    private Integer channel;

    /**
     * 训练类型：0 数字人训练；1 声音训练
     */
    private Integer jobType;

    /**
     * 训练id 雪花算法
     */
    private Long trainJobId;

    /**
     * 第三方训练id
     */
    private String extJobId;

    /**
     * 第三方训练人模型编号
     */
    private String extModelCode;

    /**
     * 训练状态：1 训练中；0 训练完成；-1 训练失败
     */
    private Integer status;

    /**
     * 回调日志id
     */
    private Long callbackId;

}

