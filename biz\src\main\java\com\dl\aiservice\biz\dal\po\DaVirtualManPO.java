package com.dl.aiservice.biz.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.aiservice.biz.common.po.BasePO;
import lombok.Data;

import java.util.Date;

/**
 * 数字资产-仿真人信息表
 *
 * @TableName da_virtual_man
 */
@TableName(value = "da_virtual_man")
@Data
public class DaVirtualManPO extends BasePO {
    private static final long serialVersionUID = -8526551605764194638L;
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数字人唯一标识
     */
    @TableField(value = "biz_id")
    private Long bizId;

    /**
     * 租户代码
     */
    @Deprecated
    @TableField(value = "tenant_code")
    private String tenantCode;

    /**
     * 数字人形象代码
     */
    @TableField(value = "vm_code")
    private String vmCode;

    /**
     * 数字人名称
     */
    @TableField(value = "vm_name")
    private String vmName;

    /**
     * 仿真人自有声音代码
     */
    @TableField(value = "vm_voice_key")
    private String vmVoiceKey;

    /**
     * 关联合成音/克隆音的声音代码
     */
    @TableField(value = "voice_biz_id")
    private Long voiceBizId;

    /**
     * 性别：1 男; 2 女
     */
    @TableField(value = "gender")
    private Integer gender;

    /**
     * 数字人头像地址url
     */
    @TableField(value = "head_img")
    private String headImg;

    /**
     * 数智⼈类型：1 2d真⼈;2 3d真⼈
     */
    @TableField(value = "vm_type")
    private Integer vmType;

    /**
     * 生效日期
     */
    @TableField(value = "effect_dt")
    private Date effectDt;

    /**
     * 仿真人失效期
     */
    @TableField(value = "expiry_dt")
    private Date expiryDt;

    /**
     * 渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    @TableField(value = "channel")
    private Integer channel;

    /**
     * 是否启用 0：否，1：是
     */
    @TableField(value = "is_enabled")
    private Integer isEnabled;

    /**
     * 是否删除 0：否，1：是
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;

    /**
     * 是否支持语速调节，0 否；1 是
     */
    @TableField(value = "is_enable_speed")
    private Integer isEnableSpeed;

    /**
     * 默认语速 1.0；范围 0.5 ~ 1.5
     */
    @TableField(value = "default_speed")
    private Float defaultSpeed;
}