package com.dl.aiservice.biz.service.digital.dto.req.ivh;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class IvhLogoParamsDTO {

    /**
     * logo图⽚⽂件下载路径，⽀持jpg、png格式
     */
    @ApiModelProperty(value = "腾讯非必填：logo图⽚⽂件下载路径")
    public String logoFileUrl;
    /**
     * logo图⽚左上⻆的X坐标（坐标范围和视频分辨率相关）
     */
    @ApiModelProperty(value = "腾讯非必填： logo图⽚左上⻆的X坐标")
    public Integer positionX;
    /**
     * logo图⽚左上⻆的Y坐标（坐标范围和视频分辨率相关）
     */
    @ApiModelProperty(value = "腾讯非必填：logo图⽚左上⻆的Y坐标")
    public Integer positionY;
    /**
     * logo图⽚缩放⽐例（1.0为原始图⽚⼤⼩）
     */
    @ApiModelProperty(value = "腾讯非必填： logo图⽚缩放⽐例")
    public Double scale;
}