package com.dl.aiservice.share.voiceclone;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @ClassName TrainCallbackResponseDTO
 * @Description 声纹训练响应报文
 * <AUTHOR>
 * @Date 2023/3/16 17:52
 * @Version 1.0
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TrainCallbackResponseDTO {

    @ApiModelProperty(value = "训练失败时的响应码；0=成功")
    Integer code = 0;

    @ApiModelProperty("详细信息")
    String msg;

    @ApiModelProperty(value = "音色的音质")
    String quality;

    @ApiModelProperty("声纹训练结果 - 音色名")
    String voiceName;

    @ApiModelProperty("当前提交音色的唯一编号")
    String trainJobId;

    @ApiModelProperty("训练状态：1 训练中；0 训练完成；-1 训练失败")
    Integer status;

}
