package com.dl.aiservice.web.controller.digital;

import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.GetTimbreRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ivh.IvhTimbreActionResponseDTO;
import com.dl.aiservice.biz.service.digital.ivh.IvhDigitalService;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhListenTtsRequestDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhListenTtsResponseDTO;
import com.dl.aiservice.share.digitalman.ivh.IvhSmallSampleAnchorResponseDTO;
import com.dl.framework.common.model.ResultModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description : 腾讯云特有接口
 * @date :2022-08-19 13:52:25
 */
@Slf4j
@RestController
@RequestMapping("/digital/ivh")
public class IvhDigitalController {

    @Resource
    private IvhDigitalService ivhDigitalService;

    @PostMapping("/getTimbreAction")
    public ResultModel<IvhTimbreActionResponseDTO> getTimbreAction(
            @RequestBody GetTimbreRequestDTO getResourceRequestDTO) {
        return ResultModel.success(ivhDigitalService.getTimbreAction(getResourceRequestDTO));
    }

    @PostMapping("/listenTts")
    public ResultModel<IvhListenTtsResponseDTO> listenTts(@RequestBody IvhListenTtsRequestDTO getResourceRequestDTO) {
        return ResultModel.success(ivhDigitalService.listenTts(getResourceRequestDTO));
    }

    @PostMapping("/videoMake")
    public ResultModel<CreateResponseDTO> videoMake(@RequestBody CreateRequestDTO getResourceRequestDTO) {
        return ResultModel.success(ivhDigitalService.videoCreate(getResourceRequestDTO));
    }

    @PostMapping("/getProgress")
    public ResultModel<ProgressResponseDTO> getProgress(@RequestBody TaskRequestDTO taskRequestDTO) {
        return ResultModel.success(ivhDigitalService.getProgress(taskRequestDTO));
    }

    /**
     * 查询小样本数字人相关信息
     *
     * @param pageRequest
     * @return
     */
    @PostMapping("/smallsample/anchor/list")
    public ResultModel<IvhSmallSampleAnchorResponseDTO> smallSampleAnchorList(@RequestBody PageRequestDTO pageRequest) {
        return ivhDigitalService.smallSampleAnchorList(pageRequest);
    }

    /**
     * 查询客户服务资产，可以查询客户当前拥有的播报小时包，播报并发，交互并发，可定制形象数量，可定制声音复刻数量。
     *
     * @return
     */
    @PostMapping("/get/service/asset/list")
    public ResultModel<Object> getServiceAssetList() {
        return ivhDigitalService.getServiceAssetInfo();
    }
}
