package com.dl.aiservice.biz.client.deepsound;

import com.dl.aiservice.biz.client.deepsound.intercepter.DeepSoundStandardInterceptor;
import com.dl.aiservice.biz.client.deepsound.req.DsStandardTtsRequest;
import com.dl.aiservice.biz.client.deepsound.resp.DsStandardBaseResponse;
import com.dl.aiservice.biz.client.deepsound.resp.DsStandardTTSResponse;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

@BaseRequest(baseURL = "https://api.deepsound.cn/v1.0", interceptor = DeepSoundStandardInterceptor.class)
public interface DeepSoundStandardClient {


    String APP_ID = "teOE3lav";

    String APP_SECRET = "35efdc989dab90cd90999ce200e08c32";

    String SIGN_KEY = "MD5 ";


    String HEADER_APP_ID = "X-Deepsound-Appid";
    String HEADER_TIMESTAMP = "X-Deepsound-Timestamp";
    String HEADER_SIGN = "X-Deepsound-Sign";


    @Post(url = "/tts", contentType = "application/json; charset=utf-8", dataType = "json")
    DsStandardBaseResponse<DsStandardTTSResponse> tts(@JSONBody DsStandardTtsRequest request);
}
