<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.aiservice.biz.dal.mapper.DaVirtualManMapper">

    <resultMap id="BaseResultMap" type="com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManBO">
            <result property="id" column="id"/>
            <result property="bizId" column="biz_id"/>
            <result property="tenantCode" column="tenant_code"/>
            <result property="vmCode" column="vm_code"/>
            <result property="vmName" column="vm_name"/>
            <result property="gender" column="gender"/>
            <result property="headImg" column="head_img"/>
            <result property="vmType" column="vm_type"/>
            <result property="effectDt" column="effect_dt"/>
            <result property="expiryDt" column="expiry_dt"/>
            <result property="channel" column="channel"/>
            <result property="isEnabled" column="is_enabled"/>
            <result property="createDt" column="create_dt"/>
            <result property="modifyDt" column="modify_dt"/>
            <result property="isEnableSpeed" column="is_enable_speed"/>
            <result property="defaultSpeed" column="default_speed"/>
            <result property="vmVoiceKey" column="vm_voice_key"/>
            <result property="voiceBizId" column="voice_biz_id"/>
    </resultMap>

    <sql id="page_query">
        <if test="param.vmCode != null and param.vmCode != ''">
            AND dvm.vm_code = #{param.vmCode}
        </if>
        <if test="param.vmName != null and param.vmName != ''">
            AND dvm.vm_name like concat('%', #{param.vmName}, '%')
        </if>
        <if test="param.channel != null">
            AND dvm.channel = #{param.channel}
        </if>
    </sql>

    <!--<select id="pageVm" resultMap="BaseResultMap"
            parameterType="com.dl.aiservice.biz.dal.po.DaVirtualManQueryPO">
        SELECT dvm.id,dvm.biz_id,dvm.tenant_code,dvm.vm_code,dvm.vm_name,dvm.gender,dvm.head_img,dvm.vm_type,
        dvm.effect_dt,dvm.expiry_dt,dvm.channel,dvm.is_enabled,dvm.create_dt,dvm.modify_dt,dvm.is_enable_speed,
        dvm.default_speed,dvmtc.tenant_code auth_tenant_code,dvms.scene_name scene_name_list
        FROM da_virtual_man dvm
        LEFT JOIN (
            SELECT vm_code, GROUP_CONCAT(DISTINCT tenant_code) tenant_code FROM da_virtual_man
            WHERE is_deleted = 0
            GROUP BY vm_code ) dvmtc ON dvm.vm_code = dvmtc.vm_code
        LEFT JOIN (
            SELECT vm_biz_id, GROUP_CONCAT(DISTINCT scene_name) scene_name FROM da_virtual_man_scenes
            WHERE is_deleted = 0
            GROUP BY vm_biz_id ) dvms ON dvm.biz_id = dvms.vm_biz_id
        WHERE dvm.is_deleted = 0
        <include refid="page_query"/>
        ORDER BY dvm.channel, dvm.id DESC
        LIMIT #{param.offset}, #{param.pageSize}
    </select>-->

    <select id="pageVm" resultMap="BaseResultMap"
            parameterType="com.dl.aiservice.biz.dal.po.DaVirtualManQueryPO">
        SELECT dvm.id,dvm.biz_id,dvm.tenant_code,dvm.vm_code,dvm.vm_name,dvm.gender,dvm.head_img,dvm.vm_type,
        dvm.effect_dt,dvm.expiry_dt,dvm.channel,dvm.is_enabled,dvm.create_dt,dvm.modify_dt,dvm.is_enable_speed,
        dvm.default_speed,dvm.vm_voice_key,dvm.voice_biz_id
        FROM da_virtual_man dvm
        where
        dvm.is_deleted = 0
        <include refid="page_query"/>
        ORDER BY dvm.id DESC
        LIMIT #{param.offset}, #{param.pageSize}
    </select>

    <select id="pageVmCount" resultType="java.lang.Integer"
            parameterType="com.dl.aiservice.biz.dal.po.DaVirtualManQueryPO">
        SELECT count(1)
        FROM da_virtual_man dvm
        WHERE
        dvm.is_deleted = 0
        <include refid="page_query"/>
    </select>

</mapper>
