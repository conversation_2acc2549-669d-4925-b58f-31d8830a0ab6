package com.dl.aiservice.share.voiceclone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TTSResponseDTO implements Serializable {

    private static final long serialVersionUID = -6442816271939461816L;

    @ApiModelProperty(value = "tts 合成请求唯一id")
    private String sid;

    @ApiModelProperty(value = "媒体任务id")
    private Long mediaJobId;

    @ApiModelProperty(value = "合成后的音频下载/播放地址")
    private String audioUrl;

    @ApiModelProperty(value = "音频时长")
    private Double duration;

    @ApiModelProperty(value = "字幕")
    private List<TtsSubtitleDTO> subtitles;
}
