package com.dl.aiservice.share.voiceclone;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @describe: TTSVoiceTypeDTO
 * @author: zhousx
 * @date: 2023/5/9 15:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TTSGenericVoiceDTO {
    /**
     * 渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    private Integer channel;

    /**
     * 外部厂商声音唯一标识
     */
    private String voiceKey;

    /**
     * 声音名称
     */
    private String voiceName;

    /**
     * 性别：1 男 ；2 女
     */
    private Integer voiceGender;

    /**
     * 默认：通用
     */
    private String voiceType;

    /**
     * 描述
     */
    private String voiceDesc;

    /**
     * 试听链接
     */
    private String sampleLink;
}
