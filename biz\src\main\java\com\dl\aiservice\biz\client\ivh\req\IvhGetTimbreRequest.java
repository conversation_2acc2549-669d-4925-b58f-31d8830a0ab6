package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class IvhGetTimbreRequest implements Serializable {


    private static final long serialVersionUID = -1301231693693196145L;

    /**
     * virtualmanKey
     */
    @JsonProperty(value = "VirtualmanKey")
    private String virtualmanKey;

}
