package com.dl.aiservice.biz.client.bytedance.interceptor;

import cn.hutool.json.JSONUtil;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * @describe: ByteDanceDigitalInterceptor
 * @author: zhousx
 * @date: 2023/10/8 14:13
 */
@Slf4j
public class ByteDanceDigitalInterceptor implements Interceptor {
    @SneakyThrows
    @Override
    public boolean beforeExecute(ForestRequest request) {
        log.info("before execute:\nrequest: {}", request.getBody().nameValuesMapWithObject());
        return Boolean.TRUE;
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        throw BusinessServiceException.getInstance(ex.getMessage());
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("after execute:\nrequest: {}\nresponse: {}", JSONUtil.toJsonStr(request.getBody().nameValuesMapWithObject()), response.getContent());
    }
}
