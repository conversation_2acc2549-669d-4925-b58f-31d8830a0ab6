package com.dl.aiservice.biz.service.digital.aliyun.impl;

import cn.hutool.json.JSONUtil;
import com.aliyun.ice20201109.Client;
import com.aliyun.ice20201109.models.GetSmartHandleJobRequest;
import com.aliyun.ice20201109.models.GetSmartHandleJobResponse;
import com.aliyun.ice20201109.models.GetSmartHandleJobResponseBody;
import com.aliyun.ice20201109.models.ListSmartSysAvatarModelsRequest;
import com.aliyun.ice20201109.models.ListSmartSysAvatarModelsResponse;
import com.aliyun.ice20201109.models.ListSmartSysAvatarModelsResponseBody;
import com.aliyun.ice20201109.models.SubmitAudioProduceJobRequest;
import com.aliyun.ice20201109.models.SubmitAudioProduceJobResponse;
import com.aliyun.ice20201109.models.SubmitAvatarVideoJobRequest;
import com.aliyun.ice20201109.models.SubmitAvatarVideoJobResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.voiceclone.grammartrans.impl.aliyun.AliyunGrammarTransformer;
import com.dl.aiservice.biz.mq.producer.AiDigitalManProducer;
import com.dl.aiservice.biz.service.digital.AbstractDigitalService;
import com.dl.aiservice.biz.service.digital.aliyun.AliyunDigitalService;
import com.dl.aiservice.biz.service.digital.dto.req.CreateRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.CreateTrainingRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.ProgressRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.RobotDetailRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TaskRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.TrainRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.aliyun.AliyunTTSEditingRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.aliyun.AliyunTTSOutputRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.aliyun.AliyunVideoEditingRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.aliyun.AliyunVideoInputRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.req.aliyun.AliyunVideoOutputRequestDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.CreateTrainingResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.ProgressResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotDetailResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.RobotResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.TrainResponseDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.aliyun.AliyunVideoAiResultResponseDTO;
import com.dl.aiservice.biz.service.digital.enums.AliyunSynthesisStatusEnum;
import com.dl.aiservice.biz.service.digital.enums.DigitalComposeEnum;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AliyunDigitalServiceImpl extends AbstractDigitalService implements AliyunDigitalService {

    private Client client;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private AiDigitalManProducer aiDigitalManProducer;
    @Resource
    private AliyunGrammarTransformer aliyunGrammarTransformer;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private ChannelUtil channelUtil;
    private final String url = "https://dl-cs-test.oss-cn-shanghai.aliyuncs.com/";

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.ALIYUN_DIGIITAL);
    }

    /**
     * 使用AK&SK初始化账号Client
     */
    @PostConstruct
    private void createClient() throws Exception {
        Config config = new Config().setAccessKeyId("LTAI5tRxgdkjVFJLyTEoc3o2")
                .setAccessKeySecret("******************************");
        // 访问的域名
        config.endpoint = "ice.cn-shanghai.aliyuncs.com";
        this.client = new Client(config);
    }


    @Override
    public CreateResponseDTO videoCreate(CreateRequestDTO createRequestDTO) {
        SubmitAvatarVideoJobRequest request = new SubmitAvatarVideoJobRequest();
        Assert.notNull(createRequestDTO.getType(), "合成类型不能为空");
        Assert.isTrue(StringUtils.isNotEmpty(createRequestDTO.getSceneId()), "场景id不能为空");
        //构建请求参数
        this.buildRequest(createRequestDTO, request);
        try {
            SubmitAvatarVideoJobResponse resp = client.submitAvatarVideoJobWithOptions(request, new RuntimeOptions());
            log.info("AliyunDigitalServiceImpl.videoCreate: request={},response={}", JsonUtils.toJSON(request),
                    JsonUtils.toJSON(resp));
            saveLog(createRequestDTO, request, resp, null);
            CreateResponseDTO createResponseDTO = new CreateResponseDTO();
            createResponseDTO.setTaskId(resp.getBody().getJobId());
            createResponseDTO.setMediaJobId(createRequestDTO.getUpdateId());
            createResponseDTO.setWorksBizId(createRequestDTO.getWorksBizId());
            //发送消息异步轮训查询结果
            ProgressRequestDTO progressRequestDTO = new ProgressRequestDTO();
            BeanUtils.copyProperties(createResponseDTO, progressRequestDTO);
            progressRequestDTO.setCount(0);
            aiDigitalManProducer.getProgressAndCallBackDelayed(progressRequestDTO);
            return createResponseDTO;
        } catch (Exception e) {
            log.info("AliyunDigitalServiceImpl.videoCreate 异常，request={}", JsonUtils.toJSON(createRequestDTO));
            log.error("AliyunDigitalServiceImpl.videoCreate 异常", e);
            saveLog(createRequestDTO, request, null, e);
            throw BusinessServiceException.getInstance(e.getMessage());
        }

    }

    private void buildRequest(CreateRequestDTO createRequestDTO, SubmitAvatarVideoJobRequest request) {
        AliyunVideoInputRequestDTO inputConfig = new AliyunVideoInputRequestDTO();
        if (Objects.equals(createRequestDTO.getType(), DigitalComposeEnum.TEXT.getCode())) {
            Assert.isTrue(StringUtils.isNotEmpty(createRequestDTO.getText()), "合成的文本不能为空");
            inputConfig.setText(aliyunGrammarTransformer.grammarTransform(createRequestDTO.getText()));
        }
        if (Objects.equals(createRequestDTO.getType(), DigitalComposeEnum.VIDEO.getCode())) {
            Assert.isTrue(StringUtils.isNotEmpty(createRequestDTO.getAudioUrl()), "上传音频不能为空");
            inputConfig.setInputFile(createRequestDTO.getAudioUrl());
        }

        AliyunVideoEditingRequestDTO editingConfig = new AliyunVideoEditingRequestDTO();
        editingConfig.setAvatarId(createRequestDTO.getSceneId());
        editingConfig.setVoice(StringUtils.isNotBlank(createRequestDTO.getSpeakerId()) ? createRequestDTO.getSpeakerId() : "");
        editingConfig.setSpeechRate(Objects.nonNull(createRequestDTO.getSpeechRate()) ? Integer.valueOf(createRequestDTO.getSpeechRate()) : null);
        editingConfig.setVolume(Objects.nonNull(createRequestDTO.getVolume()) ? Integer.parseInt(createRequestDTO.getVolume()) : 100);
        editingConfig.setBackgroundUrl(createRequestDTO.getBackgroundUrl());

        AliyunVideoOutputRequestDTO outputConfig = new AliyunVideoOutputRequestDTO();
        outputConfig.setMediaURL(url + hostTimeIdg.generateId().longValue() + "." + (StringUtils.isNotBlank(createRequestDTO.getVideoFormat()) ? createRequestDTO.getVideoFormat() : "webm"));
        outputConfig.setWidth(Objects.isNull(createRequestDTO.getWidth()) ? 1080 : Integer.parseInt(createRequestDTO.getWidth()));
        outputConfig.setHeight(Objects.isNull(createRequestDTO.getHeight()) ? 1920 : Integer.parseInt(createRequestDTO.getHeight()));
        request.setOutputConfig(JsonUtils.toJSON(outputConfig));

        request.setEditingConfig(JsonUtils.toJSON(editingConfig));
        request.setInputConfig(JsonUtils.toJSON(inputConfig));
    }

    /**
     * 增加媒体生成记录表
     */
    private void saveLog(CreateRequestDTO paramDTO, SubmitAvatarVideoJobRequest request, SubmitAvatarVideoJobResponse response, Exception e) {
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setId(paramDTO.getUpdateId());
        job.setJobContent(JsonUtils.toJSON(request));
        if (Objects.nonNull(response)) {
            if (Objects.nonNull(response.getBody()) && Objects.nonNull(response.getBody().getJobId())) {
                job.setExtJobId(response.getBody().getJobId());
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(Const.ONE);
            } else {
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(-Const.ONE);
                job.setResponseDt(new Date());
            }
        } else {
            job.setStatus(-Const.ONE);
            job.setFailCode("");
            job.setResponseDt(new Date());
            job.setFailReason(e.getMessage());
        }
        mediaProduceJobManager.updateById(job);
    }

    @Override
    public ResultPageModel<RobotResponseDTO> robotPageList(PageRequestDTO pageRequestDTO) {
        ResultPageModel<RobotResponseDTO> respDto = new ResultPageModel<>();
        respDto.setPageSize(pageRequestDTO.getPageSize().longValue());
        respDto.setPageIndex(pageRequestDTO.getPageIndex().longValue());
        try {
            ListSmartSysAvatarModelsRequest listSmartSysAvatarModelsRequest = new ListSmartSysAvatarModelsRequest()
                    .setPageNo(1L)
                    .setPageSize(10L);
            ListSmartSysAvatarModelsResponse resp = client.listSmartSysAvatarModelsWithOptions(listSmartSysAvatarModelsRequest, new RuntimeOptions());
            log.info("AliyunDigitalServiceImpl.robotPageList: request={},response={}", JsonUtils.toJSON(pageRequestDTO), JsonUtils.toJSON(resp));

            respDto.setCode(resp.getStatusCode().toString());
            if (resp.getStatusCode().equals(200)) {
                List<ListSmartSysAvatarModelsResponseBody.ListSmartSysAvatarModelsResponseBodySmartSysAvatarModelList> smartSysAvatarModelList = resp.getBody().getSmartSysAvatarModelList();
                List<RobotResponseDTO> responseDTOS = smartSysAvatarModelList.stream().map(avatarModelt -> {
                    RobotResponseDTO robotResponseDTO = new RobotResponseDTO();
                    robotResponseDTO.setId(avatarModelt.getAvatarId());
                    robotResponseDTO.setCoverUrl(avatarModelt.getCoverUrl());
                    robotResponseDTO.setRobotName(avatarModelt.getAvatarName());
                    BeanUtils.copyProperties(avatarModelt, robotResponseDTO);
                    return robotResponseDTO;
                }).collect(Collectors.toList());
                respDto.setTotal(resp.getBody().getTotalCount().longValue());
                respDto.setDataResult(responseDTOS);
            }
            return respDto;
        } catch (Exception e) {
            log.info("AliyunDigitalServiceImpl.robotPageList 异常，request={}", JsonUtils.toJSON(pageRequestDTO));
            log.error("AliyunDigitalServiceImpl.robotPageList 异常", e);
            throw BusinessServiceException.getInstance(e.getMessage());
        }
    }


    @Override
    public ProgressResponseDTO getProgress(TaskRequestDTO taskRequestDTO) {
        ProgressResponseDTO respDto = new ProgressResponseDTO();
        GetSmartHandleJobRequest request = new GetSmartHandleJobRequest();
        request.setJobId(taskRequestDTO.getTaskId());
        try {
            GetSmartHandleJobResponse resp = client.getSmartHandleJobWithOptions(request, new RuntimeOptions());
            log.info("AliyunDigitalServiceImpl.getProgress: request={},response={}", JsonUtils.toJSON(request), JsonUtils.toJSON(resp));
            if (resp.getStatusCode().equals(200)) {
                GetSmartHandleJobResponseBody body = resp.getBody();
                AliyunVideoAiResultResponseDTO videoAiResult = JSONUtil.toBean(body.getJobResult().getAiResult(), AliyunVideoAiResultResponseDTO.class);
                respDto.setSynthesisStatus(AliyunSynthesisStatusEnum.getEnum(body.getState()).getCode());
                respDto.setVideoUrl(videoAiResult.getOutputVideoUrl());
                respDto.setDuration(videoAiResult.getOutputDuration());
            }
            return respDto;
        } catch (Exception e) {
            log.info("AliyunDigitalServiceImpl.getProgress 异常，request={}", JsonUtils.toJSON(request));
            log.error("AliyunDigitalServiceImpl.getProgress 异常", e);
            throw BusinessServiceException.getInstance(e.getMessage());
        }
    }

    @Override
    public ResultModel<TTSResponseDTO> listenTts(TTSProduceParamDTO request) {
        Assert.notNull(request, "入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getVoiceName()), "voiceName入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(request.getText()), "音频文本不能为空");
        String fileName = hostTimeIdg.generateId().toString();
        SubmitAudioProduceJobRequest submitAudioProduceJobRequest = buildTTSRequest(request, fileName);
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(hostTimeIdg.generateId().longValue());
        job.setTenantCode(channelUtil.getTenantCode());
        job.setWorksBizId(request.getWorksBizId());
        job.setChannel(channelUtil.getChannel());
        job.setJobType(Const.TWO);
        job.setJobContent(JsonUtils.toJSON(submitAudioProduceJobRequest));
        job.setStatus(Const.ONE);
        job.setRequestDt(new Date());
        mediaProduceJobManager.save(job);
        MediaProduceJobPO updateJob = new MediaProduceJobPO();
        updateJob.setId(job.getId());
        try {
            log.info("AliyunDigitalServiceImpl.tts合成，mediaJobId:{},,,request={}", job.getMediaJobId(),
                    JsonUtils.toJSON(submitAudioProduceJobRequest));
            SubmitAudioProduceJobResponse resp = client
                    .submitAudioProduceJobWithOptions(submitAudioProduceJobRequest, new RuntimeOptions());
            log.info("AliyunDigitalServiceImpl.tts合成，mediaJobId:{},,,response={}", job.getMediaJobId(),
                    JsonUtils.toJSON(resp));
            if (Objects.nonNull(resp) && resp.getStatusCode().equals(200) && Objects
                    .nonNull(resp.getBody().getJobId())) {
                updateJob.setExtJobId(resp.getBody().getJobId());
                mediaProduceJobManager.updateById(updateJob);
                log.info("AliyunDigitalServiceImpl.tts合成进度查询，mediaJobId:{},,,request={}", job.getMediaJobId(),
                        JsonUtils.toJSON(resp.getBody().getJobId()));
                GetSmartHandleJobResponse processResult = this.getProcessResult(resp.getBody().getJobId(), 0);
                if (Objects.nonNull(processResult) && processResult.getStatusCode().equals(200)) {
                    log.info("AliyunDigitalServiceImpl.tts合成进度查询，mediaJobId:{},,,response={}", job.getMediaJobId(),
                            JsonUtils.toJSON(processResult));
                    TTSResponseDTO ttsResponseDTO = new TTSResponseDTO();
                    ttsResponseDTO.setSid(resp.getBody().getJobId());
                    ttsResponseDTO.setMediaJobId(job.getMediaJobId());
                    ttsResponseDTO.setAudioUrl(
                            url + fileName + "." + (StringUtils.isNotBlank(request.getAudioFormat()) ?
                                    request.getAudioFormat() :
                                    "MP3"));
                    updateJob.setStatus(Const.ZERO);
                    updateJob.setMediaUrl(ttsResponseDTO.getAudioUrl());
                    updateJob.setExtJobId(resp.getBody().getJobId());
                    updateJob.setResponseDt(new Date());
                    mediaProduceJobManager.updateById(updateJob);
                    return ResultModel.success(ttsResponseDTO);
                }
            }
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            updateJob.setStatus(-Const.ONE);
            // 失败原因
            updateJob.setFailCode(resp.getStatusCode().toString());
            updateJob.setFailReason(resp.getStatusCode().toString());
            updateJob.setExtJobId(StringUtils.isNotBlank(resp.getBody().getJobId()) ? resp.getBody().getJobId() : "");
            updateJob.setResponseDt(new Date());
            mediaProduceJobManager.updateById(updateJob);

            return ResultModel.error("-1", "语音合成失败:" + resp.getStatusCode());
        } catch (Exception e) {
            log.error("AliyunDigitalServiceImpl.getProgress 异常，mediaJobId:{},,,request={}", job.getMediaJobId(),
                    JsonUtils.toJSON(submitAudioProduceJobRequest));
            log.error("AliyunDigitalServiceImpl.getProgress 异常", e);
            // 任务状态：1 合成中；0 合成完成；-1 合成失败
            updateJob.setStatus(-Const.ONE);
            // 失败原因
            updateJob.setFailReason(e.getMessage());
            updateJob.setResponseDt(new Date());
            mediaProduceJobManager.updateById(updateJob);
            return ResultModel.error("-1", "语音合成失败:" + e.getMessage());
        }
    }

    @NotNull
    private SubmitAudioProduceJobRequest buildTTSRequest(TTSProduceParamDTO request, String fileName) {
        SubmitAudioProduceJobRequest submitAudioProduceJobRequest = new SubmitAudioProduceJobRequest();
        AliyunTTSEditingRequestDTO editingConfig = new AliyunTTSEditingRequestDTO();
        editingConfig.setVoice(request.getVoiceName());

//        DaVirtualVoicePO daVirtualVoicePO = daVirtualVoiceManager.getOne(Wrappers.<DaVirtualVoicePO>lambdaQuery()
//                .eq(DaVirtualVoicePO::getVoiceKey, request.getVoiceName())
//                .eq(DaVirtualVoicePO::getChannel, ServiceChannelEnum.ALIYUN_DIGIITAL.getCode())
//                .eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO)
//                .eq(DaVirtualVoicePO::getIsEnabled, Const.ONE));
//        if (Objects.isNull(daVirtualVoicePO)) {
//            throw BusinessServiceException.getInstance("阿里云克隆音语音合成失败，找不到对应的语音");
//        }
        //因现在没有克隆音声音，所以先用自定义音
//        editingConfig.setVoice(request.getVoiceName());
//        if (daVirtualVoicePO.getVoiceType().equals(Const.ZERO)) {
//            //克隆音
//            editingConfig.setCustomizedVoice(request.getVoiceName());
//        } else {
//            editingConfig.setVoice(request.getVoiceName());
//        }

        editingConfig.setFormat(StringUtils.isNotBlank(request.getAudioFormat()) ? request.getAudioFormat() : "mp3");
        AliyunTTSOutputRequestDTO outputConfig = new AliyunTTSOutputRequestDTO();
        outputConfig.setBucket("dl-cs-test");
        outputConfig.setObject(fileName);

//        AliyunVideoInputRequestDTO inputConfig = new AliyunVideoInputRequestDTO();
//        inputConfig.setInputFile(request.getText());
        submitAudioProduceJobRequest.setOutputConfig(JsonUtils.toJSON(outputConfig));
        submitAudioProduceJobRequest.setEditingConfig(JsonUtils.toJSON(editingConfig));
        submitAudioProduceJobRequest.setInputConfig(aliyunGrammarTransformer.grammarTransform(request.getText()));
        return submitAudioProduceJobRequest;
    }

    private GetSmartHandleJobResponse getProcessResult(String taskId, int count) {

        long l = System.currentTimeMillis();
        try {
            Thread.sleep(1000);
            log.info("getProcessResult>>count={},l={},time={}", count, l, System.currentTimeMillis() - l);
        } catch (InterruptedException e) {
            log.error("睡眠异常！rep={}", taskId);
            throw BusinessServiceException.getInstance("科大讯飞合成任务异常，睡眠异常");
        }
        GetSmartHandleJobRequest request = new GetSmartHandleJobRequest();
        request.setJobId(taskId);
        GetSmartHandleJobResponse resp;
        try {
            resp = client.getSmartHandleJobWithOptions(request, new RuntimeOptions());
        } catch (Exception e) {
            log.error("阿里云查询合成任务结果失败！param={}", taskId);
            throw BusinessServiceException.getInstance(e.getMessage());
        }
        if (Objects.isNull(resp) || !resp.getStatusCode().equals(200)) {
            log.error("阿里云查询合成任务结果失败！param={},resp={}", taskId, JSONUtil.toJsonStr(resp));
            return resp;
        }

        if (resp.getBody().getState().equals(AliyunSynthesisStatusEnum.Finished.getDesc())) {
            return resp;
        } else {
            ++count;
            if (count > 60) {
                log.error("阿里云查询合成时间过久！taskId={},count={}", taskId, count);
                throw BusinessServiceException.getInstance("阿里云tts合成超时");
            }
            return getProcessResult(taskId, count);
        }
    }


    @Override
    public CreateTrainingResponseDTO createTraining(CreateTrainingRequestDTO request) {
        return null;
    }

    @Override
    public RobotDetailResponseDTO robotDetail(RobotDetailRequestDTO requestDTO) {
        return null;
    }

    @Override
    public TrainResponseDTO getTrain(TrainRequestDTO gjTrainRequestDTO) {
        return null;
    }

}




