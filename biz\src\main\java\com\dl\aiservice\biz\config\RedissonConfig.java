package com.dl.aiservice.biz.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName RedissonConfig
 * @Description
 * <AUTHOR>
 * @Date 2022/4/20 15:46
 * @Version 1.0
 **/
@Configuration
public class RedissonConfig {

    @Autowired
    private RedisProperties redisProperties;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        StringBuffer addressBuilder = new StringBuffer("redis://");
        addressBuilder.append(redisProperties.getHost()).append(":").append(redisProperties.getPort());
        config.useSingleServer().setAddress(addressBuilder.toString()).setPassword(redisProperties.getPassword())
                .setDatabase(redisProperties.getDatabase());
        return Redisson.create(config);
    }
}
