package com.dl.aiservice.share.voiceclone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class AudioCheckParamDTO implements Serializable {

    private static final long serialVersionUID = 6396820559007386813L;

    @ApiModelProperty(value = "可公网访问音频文件链接", required = true)
    @NotBlank(message = "音频文件url必填")
    private String url;

    @ApiModelProperty(value = "音频内容的对应文本，不超过35个中文字符", required = true)
    @NotBlank(message = "音频内容的对应文本不能为空")
    private String text;

    @ApiModelProperty(value = "待检音频的语种；目前支持 zh（中 文）和 en（英文），默认为 zh")
    private String language;
}
