package com.dl.aiservice.biz.service.digital.dto.req.ivh;

import com.dl.aiservice.biz.client.ivh.req.IvhSpeedRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhVideoRequest;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IvhVideoMakeRequestDTO implements Serializable {

    private static final long serialVersionUID = -1301231693693196145L;
    /**
     * 定义播报的⻆⾊、服装、姿态、分辨率等信息，参数为枚举值。
     */
    private String virtualmanKey;
    /**
     * 播报的⽂本内容，⽀持ssml标签，⽀持的标签类型参照附录2，标签写法参照示例，内容不能换⾏，符号需转义。2d播报上限1万字；3d播报上限500字。DriverType为空、或Text时，该字段必填
     */
    private String inputSsml;
    /**
     * 定义⾳频的详细参数
     */
    private IvhSpeedRequest speechParam;

    /**
     * 定义合成视频的详细参数，
     */
    private IvhVideoRequest videoParam;
    /**
     * 当⽤户增加回调url时，将把视频制作结果以固定格式发送post请求到该url地址，固定格式⻅附录三，需注意：1、限制CallbackUrl⻓度⼩于200**2、只发送⼀次请求，⽆论是哪种问题导致的请求失败，都不会再进⾏发送。
     ***/
    private String callbackUrl;
    /**
     * 驱动类型，默认Text1.Text：⽂本驱动，要求InputSsml字段必填2.OriginalVoice：原声⾳频驱动，要求InputAudioUrl字段必填3
     * .ModulatedVoice：变声⾳频驱动，可通过Speech.TimbreKey指定⾳⾊，未填写时使⽤主播默认⾳⾊
     */
    private String driverType;
    /**
     * 驱动数智⼈的⾳频url，当DriverType为OriginalVoice、ModulatedVoice时，该字段必填。⾳频格式要求（待确认）：1、时⻓不超过10分钟，不少于0.5秒2、⽀持格式：mp3、wav
     */
    private String inputAudioUrl;
    /**
     * 可传⼊含鉴权s3协议存储url，视频成品会上传⾄该url
     */
    private String videoStorageS3Url;
    /**
     * 可传⼊含鉴权s3协议存储url，字幕成品会上传⾄该url
     */
    private String subtitleStorageS3Url;

}
