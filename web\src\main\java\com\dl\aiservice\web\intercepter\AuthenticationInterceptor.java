package com.dl.aiservice.web.intercepter;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.dl.aiservice.biz.common.annotation.NotAuth;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.RsaUtil;
import com.dl.aiservice.biz.properties.AuthProperties;
import com.dl.aiservice.share.common.auth.AuthHeader;
import com.dl.aiservice.share.common.auth.AuthTokenDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.framework.core.interceptor.expdto.CertificateException;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
@Slf4j
public class AuthenticationInterceptor implements HandlerInterceptor {
    private static final String PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmye+fSZBa1kjlnp0QNFn5+I2lDI0l+xLZLmP/SHwEp4ZEbXcbCtDvvEtpQ2xFt0okJoorpreOxgWynVPn5p/ZqOAwKe/ysxT46BPjrg2sa/EmrRRHXz8ZC1DSLe1I+F4GmMM2V75GYQ2ia9Dri32tlwbZ6WBbg5GW/wKoJz3kLAStxSSsipJrW9zOy44/BfYXTHJBthkcaMga5feiCghHTmtaqYOfM2SNhW2SOQhHaa/KGdcEL+s9/WEuNNtrt0x8tXOPYALRJXdXErmwPwLrIDHqrtKGk4x0W2fgnoR4qa24T1p+z7fCCRo/9GvYap2xYVVCFiOOMXGVweFRldmcwIDAQAB";
    private final PathMatcher pathMatcher = new AntPathMatcher();
    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private ChannelUtil channelUtil;

    private boolean isIgnoreUrl(HttpServletRequest request) {
        String url = request.getRequestURI();
        List<String> urlPatterns = authProperties.getIgnoreUrls();
        if (Collections.isEmpty(urlPatterns)) {
            return false;
        }
        for (String urlPattern : urlPatterns) {
            if (pathMatcher.match(urlPattern, url)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        //判断是否忽略url
        if (isIgnoreUrl(request)) {
            return true;
        }
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        //2、判断 NotLogin，有则跳过认证
        if (method.isAnnotationPresent(NotAuth.class)) {
            NotAuth loginToken = method.getAnnotation(NotAuth.class);
            if (loginToken.required()) {
                return true;
            }
        }
        // 从 http 请求头中取出 token 和 租户编号
        String tenantCode = request.getHeader(AuthHeader.TENANT_CODE_HEADER_NAME);
        String token = request.getHeader(AuthHeader.TOKEN_HEADER_NAME);
        if (StringUtils.isBlank(token) || StringUtils.isBlank(tenantCode)) {
            throw BusinessServiceException.getInstance("无权访问");
        }
        AuthProperties.Tenant tenantAuthInfo = getTenantAuth(tenantCode);
        String publicKey = Objects.nonNull(tenantAuthInfo) ? tenantAuthInfo.getPublicKey() : PUBLIC_KEY;
        //解密并校验token
        String decryptStr;
        try {
            decryptStr = RsaUtil.decryptByPublicKey(publicKey, token);
        } catch (Exception e) {
            log.error("token解密失败,token:{}", token);
            throw BusinessServiceException.getInstance("token解密失败");
        }
        AuthTokenDTO authTokenDTO = JSONObject.parseObject(decryptStr, AuthTokenDTO.class);
        //判断token是否是今日生成的
        if (!DateUtil.format(new Date(), "yyyy-MM-dd")
                .equals(DateUtil.format(authTokenDTO.getTokenCreateDt(), "yyyy-MM-dd"))) {
            throw new CertificateException("token已失效，请重新登录");
        }
        if (Objects.isNull(ServiceChannelEnum.getByCode(Integer.valueOf(authTokenDTO.getChannel())))) {
            throw new CertificateException("渠道不存在，请重新登录");
        }
        //渠道参数注入
        channelUtil.init(authTokenDTO);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) {
        channelUtil.remove();
    }

    private AuthProperties.Tenant getTenantAuth(String tenantCode) {
        List<AuthProperties.Tenant> tenants = authProperties.getTenants();
        if(CollectionUtils.isNotEmpty(tenants)) {
            Optional<AuthProperties.Tenant> tenant = tenants.stream().filter(t -> t.getTenantCode().equals(tenantCode)).findFirst();
            if(tenant.isPresent()) {
                return tenant.get();
            }
        }
        return null;
    }

}
