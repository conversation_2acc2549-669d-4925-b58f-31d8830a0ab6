package com.dl.aiservice.biz.manager.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * 录制模式
 */
public enum VideoModeEnum {
    /**
     * 数字分身
     */
    IMITATION_HUMAN(1),
    /**
     * 轻拍摄(拍头尾)
     */
    HEAD_AND_TAIL(2),
    /**
     * 拍全程
     */
    MAKE_ALL(3),
    /**
     * 照片模式
     */
    IMAGE_ONLY(4);

    private int code;

    VideoModeEnum(int code) {
        this.code = code;
    }

    public static VideoModeEnum getEnum(Integer code) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.code, code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }
}
