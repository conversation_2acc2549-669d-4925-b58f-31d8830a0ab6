package com.dl.aiservice.share.digitalman.ivh;

import lombok.Data;

import java.util.List;

@Data
public class IvhSmallSampleVirtualManDTO {

    /**
     * 数智⼈virtualmanKey
     */
    private String virtualManKey;
    /**
     * 主播名称
     */
    private String anchorName;
    /**
     * 主播code
     */
    private String anchorCode;
    /**
     * 数智⼈头像图⽚url
     */
    private String headerImage;
    /**
     * 数智⼈⽀持的驱动类型 1. Text: ⽂本驱动 2. OriginalVoice: 原声⾳频驱动 3. ModulatedVoice: 变声⾳频驱动
     */
    private List<String> supportDriverTypes;

    /**
     * 数智⼈服装
     */
    private String clothesName;

    /**
     * 数智⼈姿态
     */
    private String poseName;

    /**
     * 数智⼈分辨率
     */
    private String resolution;

    /**
     * 数智⼈姿态图⽚url
     */
    private String poseImage;

    /**
     * 数智⼈服装图⽚url
     */
    private String clothesImage;

    /**
     * 失效时间戳："2023-04-01 00:00:00"
     */
    private String expireDate;

    /**
     * 声音信息
     */
    private List<IvhTimbreDetailDTO> timbres;
}