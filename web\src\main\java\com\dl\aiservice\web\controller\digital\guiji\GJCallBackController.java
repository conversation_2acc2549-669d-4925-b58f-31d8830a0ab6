package com.dl.aiservice.web.controller.digital.guiji;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.client.guiji.DigitalCallbackClient;
import com.dl.aiservice.biz.client.guiji.enums.GJCallBackEnum;
import com.dl.aiservice.biz.client.guiji.enums.GJCallBackStatusEnum;
import com.dl.aiservice.biz.common.util.RedisUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.enums.MediaProduceJobTimeoutStatusEnum;
import com.dl.aiservice.biz.manager.train.TrainJobManager;
import com.dl.aiservice.biz.service.digital.dto.req.guiji.GJCreateCallbackDTO;
import com.dl.aiservice.biz.service.digital.dto.req.guiji.GJTrainingCallbackDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalTrainCallbackDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.aiservice.biz.service.digital.guiji.GJDigitalService;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.web.controller.digital.guiji.vo.GJCallbackVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description : 账户
 * @date :2022-08-19 13:52:25
 */
@Slf4j
@RestController
@RequestMapping("/guiji")
public class GJCallBackController {

    @Resource
    private DigitalCallbackClient digitalCallbackClient;
    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private TrainJobManager trainJobManager;
    @Resource
    private GJDigitalService gjDigitalService;
    @Resource
    private RedisUtil redisUtil;

    private static final String GUIJI_VIDEO_CALLBACK_LOCK = "guiji_video_callback_lock:";

    private static final long LOCK_TIMEOUT = 5 * 60L;

    /**
     * 合成任务回调接口
     */
    @PostMapping("/callback")
    public void videoCreateCallBack(@RequestBody GJCallbackVO callBackVO) {
        log.info("硅基智能回调,回调类型：{}，回调参数：{}", callBackVO.getTaskType(), JSONUtil.toJsonStr(callBackVO));
        switch (GJCallBackEnum.getEnum(callBackVO.getTaskType())) {
        case VIDEO_TRAINING:
            this.handleVideoTraining(callBackVO);
            break;
        case VIDEO_SYNTHESIS:
            this.handleVideoSynthesis(callBackVO);
            break;
        default:
            throw new IllegalStateException("Unexpected value: " + GJCallBackEnum.getEnum(callBackVO.getTaskType()));
        }
    }

    private void handleVideoTraining(GJCallbackVO callBackVO) {
        GJTrainingCallbackDTO tranCallback = callBackVO.getData().toJavaObject(GJTrainingCallbackDTO.class);
        TrainJobPO trainJobPO = trainJobManager.getOne(Wrappers.<TrainJobPO>lambdaUpdate()
                .eq(TrainJobPO::getChannel, ServiceChannelEnum.GUI_JI.getCode())
                .eq(TrainJobPO::getExtJobId, tranCallback.getId()));
        if (Objects.isNull(trainJobPO)) {
            log.info("硅基智能回调训练,未找到对应合成任务，三方任务id:{},具体回调信息：{}", tranCallback.getId(), callBackVO.getData());
            return;
        }
        trainJobPO.setExtModelCode(tranCallback.getRobotId());
        trainJobPO.setStatus(GJCallBackStatusEnum.getEnum(tranCallback.getResult()).getCode());
        trainJobManager.updateById(trainJobPO);
        DigitalTrainCallbackDTO digitalTrainCallbackDTO = new DigitalTrainCallbackDTO();
        BeanUtils.copyProperties(trainJobPO, digitalTrainCallbackDTO);
        digitalCallbackClient
                .trainingCallback(trainJobPO.getCallbackUrl(), digitalTrainCallbackDTO, JSONUtil.toJsonStr(callBackVO));
    }

    private void handleVideoSynthesis(GJCallbackVO callBackVO) {
        GJCreateCallbackDTO callbackDTO = callBackVO.getData().toJavaObject(GJCreateCallbackDTO.class);
        MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.lambdaQuery().in(MediaProduceJobPO::getChannel,
                Lists.newArrayList(ServiceChannelEnum.GUI_JI.getCode(), ServiceChannelEnum.CJHX_GUIJI.getCode()))
                .eq(MediaProduceJobPO::getExtJobId, callbackDTO.getId()).one();
        if (Objects.isNull(mediaProduceJobPO)) {
            log.info("硅基智能回调,未找到对应合成任务，三方任务id:{},具体回调信息：{}", callbackDTO.getId(), callBackVO.getData());
            return;
        }
        String lockKey = GUIJI_VIDEO_CALLBACK_LOCK + mediaProduceJobPO.getMediaJobId();
        if (!redisUtil.tryLockAndSetTimeout(lockKey, LOCK_TIMEOUT)) {
            log.error("硅基数字人回调，当前已有请求正在处理，不可重复处理！mediaJobId:{},,,callBackVO:{}", mediaProduceJobPO.getMediaJobId(),
                    JSONUtil.toJsonStr(callBackVO));
            return;
        }

        //是否已超时
        Boolean hasTimeout = !MediaProduceJobTimeoutStatusEnum.UN.getStatus()
                .equals(mediaProduceJobPO.getTimeoutStatus());
        this.fillMediaProduceJobStatus(callbackDTO, mediaProduceJobPO, hasTimeout);
        mediaProduceJobPO.setCoverUrl(callbackDTO.getCoverUrl());
        if (Objects.nonNull(callbackDTO.getDuration())) {
            mediaProduceJobPO.setDuration(Double.valueOf(callbackDTO.getDuration()));
        }
        mediaProduceJobPO.setMediaName(callbackDTO.getVideoName());
        mediaProduceJobPO.setFailReason(callbackDTO.getFailReason());
        mediaProduceJobPO.setResponseDt(new Date());

        String videoUrl = callbackDTO.getVideoUrl();
        if (StringUtils.isNotBlank(callbackDTO.getVideoUrl())) {
            boolean isSuccess = false;
            try {
                //将视频转存到cos
                videoUrl = gjDigitalService
                        .transfertToCos(mediaProduceJobPO.getMediaJobId(), callbackDTO.getVideoUrl());
                //不一样则说明转存成功
                if (!callbackDTO.getVideoUrl().equals(videoUrl)) {
                    isSuccess = true;
                }
            } catch (Exception e) {
                log.error("硅基数字人视频转存到cos发生异常！mediaJobId:{},videoUrl:{},e:{}", mediaProduceJobPO.getMediaJobId(),
                        callbackDTO.getVideoUrl(), e);
            }

            //不成功的话再重试一次
            if (!isSuccess) {
                try {
                    //将视频转存到cos
                    videoUrl = gjDigitalService
                            .transfertToCos(mediaProduceJobPO.getMediaJobId(), callbackDTO.getVideoUrl());
                    //不一样则说明转存成功
                    if (!callbackDTO.getVideoUrl().equals(videoUrl)) {
                        isSuccess = true;
                        log.info("再次转存成功! mediaJobId:{}", mediaProduceJobPO.getMediaJobId());
                    }
                } catch (Exception e) {
                    log.error("硅基数字人视频再次转存到cos发生异常！用原始视频url作为兜底。mediaJobId:{},videoUrl:{},e:{}",
                            mediaProduceJobPO.getMediaJobId(), callbackDTO.getVideoUrl(), e);
                }
                if (!isSuccess) {
                    log.warn("再次转存失败! mediaJobId:{}", mediaProduceJobPO.getMediaJobId());
                }
            }

        }
        mediaProduceJobPO.setMediaUrl(videoUrl);
        mediaProduceJobManager.updateById(mediaProduceJobPO);

        //已超时则不回调业务
        if (hasTimeout) {
            return;
        }

        Long worksBizId = mediaProduceJobPO.getWorksBizId();
        DigitalVideoCallbackDTO digitalCallbackDTO = new DigitalVideoCallbackDTO();
        BeanUtils.copyProperties(mediaProduceJobPO, digitalCallbackDTO);
        digitalCallbackDTO.setMediaUrl(videoUrl);

        //回调业务
        gjDigitalService.callBackBiz(mediaProduceJobPO.getTenantCode(), worksBizId, mediaProduceJobPO.getCallbackUrl(),
                digitalCallbackDTO, JSONUtil.toJsonStr(callBackVO));
    }

    private void fillMediaProduceJobStatus(GJCreateCallbackDTO callbackDTO, MediaProduceJobPO mediaProduceJobPO,
            Boolean hasTimeout) {
        GJCallBackStatusEnum gjCallBackStatusEnum = GJCallBackStatusEnum.getEnum(callbackDTO.getResult());

        //先判断是否已经超时，若已超时，则修改timeout_status，不处理status
        if (hasTimeout) {
            if (GJCallBackStatusEnum.SUCCESS.equals(gjCallBackStatusEnum)) {
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_SUCCESS.getStatus());
                return;
            }
            if (GJCallBackStatusEnum.FAIL.equals(gjCallBackStatusEnum)) {
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_FAIL.getStatus());
                return;
            }
            return;
        }

        mediaProduceJobPO.setStatus(gjCallBackStatusEnum.getCode());
    }

}