package com.dl.aiservice.share.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * 克隆音渠道枚举类，属于ServiceChannelEnum的子集
 */
@Deprecated
public enum VoiceCloneChannelEnum {
    /**
     * 新华智云
     */
    XHZY(0, "XHZY"),
    /**
     * 硅基智能
     */
    GUI_JI(1, "GUI_JI"),
    /**
     * 腾讯云克隆音
     */
    IVH(2, "IVH"),
    /**
     * 深声科技
     */
    DEEP_SOUND(3, "DS"),
    /**
     * 阿里云
     */
    ALIYUN(4, "ALIYUN"),
    /**
     * 腾讯云TTS
     */
    TENCENT_CLOUD(5, "TENCENT_CLOUD"),
    /**
     * 火山引擎
     */
    VOLC_ENGINE(6, "VOLC_ENGINE");

    private Integer code;
    private String desc;

    VoiceCloneChannelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static VoiceCloneChannelEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> Objects.equals(code, e.getCode())).findAny().orElse(null);
    }
}
