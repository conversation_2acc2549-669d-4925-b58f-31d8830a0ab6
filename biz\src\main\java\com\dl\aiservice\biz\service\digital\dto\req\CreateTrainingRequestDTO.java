package com.dl.aiservice.biz.service.digital.dto.req;

import com.dl.aiservice.biz.common.po.UpdateBasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@NoArgsConstructor
@Data
public class CreateTrainingRequestDTO extends UpdateBasePO implements Serializable {
    private static final long serialVersionUID = -240723204685689772L;


    /**
     * 必填 数字人名称
     */
    @ApiModelProperty(value = "必填 数字人名称")
    private String name;

    /**
     * 必填 训练视频url，需公网可访问
     */
    @ApiModelProperty(value = "必填 训练视频url，需公网可访问")
    private String videoUrl;

    //**************************************硅基必填参数**********************************************


    /**
     *  训练任务ID，id为空表示提交新训练，id不为空表示更新训练
     */
    @ApiModelProperty(value = "硅基：训练任务ID")
    private Integer id;


    /**
     * 训练状态回调地址
     */
    @ApiModelProperty(value = "硅基：训练状态回调地址")
    private String callbackUrl;

    //**************************************腾讯云必填参数**********************************************

    /**
     * 必填
     * 请提供合规性视频url地址，要求：
     * url地址为通过4.1上传到指定路径的资源url地址后增加idcard路径，例
     * 如：域名/customer-pipline/{数字}/idcard/a.mp4/
     * ⼈物为⼩样本训练⼈物
     * ⼿持本⼈身份证
     * 说出要求话术："我是上传视频⾳频的本⼈，所提供的视频和⾳频经过本
     * ⼈授权，本⼈知悉并同意所提供材料⽤于数智⼈定制。"
     */
    @ApiModelProperty(value = "腾讯云必填：请提供合规性视频url地址")
    private String identityCosUrl;

    /**
     * 非必填 语训练出来的形象是带背景，默认否，即不带背景
     */
    @ApiModelProperty(value = "腾讯云非必填：语训练出来的形象是带背景，默认否，即不带背景")
    private Boolean isHaveBackground;


}