package com.dl.aiservice.biz.service.digital.enums;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;

public enum SynthesisStatusEnum {

    /**
     * -1. 编辑中 1. 排队中 2. 合成中 3：合成成功 4：合成失败 5. 归档 6. 任务取消 7. 任务失败
     */
    EDITING(-1, "编辑中"),
    LINE_UP(1, "排队中"),
    MAKING(2, "合成中"),
    SUCCESS(3, "合成成功"),
    FAIL(4, "合成失败"),
    FILE(5, "归档"),
    CANCEL(6, "任务取消"),
    TASK_FILE(7, "任务失败");


    private final Integer code;
    private final String desc;

    SynthesisStatusEnum(Integer errorCode, String errorDesc) {
        this.code = errorCode;
        this.desc = errorDesc;
    }

    public static SynthesisStatusEnum getEnum(String desc) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getDesc(), desc)).findFirst().orElseThrow(() -> BusinessServiceException.getInstance("枚举异常"));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
