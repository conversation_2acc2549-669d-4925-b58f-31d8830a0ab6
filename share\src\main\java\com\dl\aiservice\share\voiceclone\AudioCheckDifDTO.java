package com.dl.aiservice.share.voiceclone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName DsDiffInfo
 * @Description 机器识别内容与文本内容出入
 * <AUTHOR>
 * @Date 2023/2/8 15:55
 * @Version 1.0
 **/
@Data
public class AudioCheckDifDTO implements Serializable {

    private static final long serialVersionUID = 3593481160681924569L;

    @ApiModelProperty("错误在原文本位置，从0开始")
    private Integer pos;

    @ApiModelProperty("读错误的文本内容")
    private String text;

    @ApiModelProperty("枚举类型: 0 错读；1 多读；-1 漏读")
    private Integer type;
}
