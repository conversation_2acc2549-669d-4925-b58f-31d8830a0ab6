package com.dl.aiservice.biz.service.digital.dto.req.aliyun;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AliyunVideoInputRequestDTO {

    /**
     * 音频地址
     */
    @JsonProperty("InputFile")
    private String InputFile;
    /**
     * 文本
     */
    @JsonProperty("Text")
    private String Text;
    /**
     * MediaId
     */
    @JsonProperty("MediaId")
    private String MediaId;
}