package com.dl.aiservice.web.controller.resourceconfig;

import com.dl.aiservice.biz.dal.po.ResourcePriceConfigPO;
import com.dl.aiservice.biz.manager.ResourcePriceConfigManager;
import com.dl.aiservice.share.resourceconfig.dto.ResourcePriceConfigRequestDTO;
import com.dl.aiservice.share.resourceconfig.dto.ResourcePriceConfigResponseDTO;
import com.dl.framework.common.model.ResultModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @describe: ResourceConfigController
 * @author: zhousx
 * @date: 2023/3/16 14:27
 */
@Slf4j
@RestController
@RequestMapping("/resourceconfig")
public class ResourceConfigController {

    @Resource
    private ResourcePriceConfigManager resourcePriceConfigManager;

    @PostMapping("/getprice")
    public ResultModel<List<ResourcePriceConfigResponseDTO>> getPrice(
            @RequestBody @Validated ResourcePriceConfigRequestDTO request) {
        List<ResourcePriceConfigPO> configs = resourcePriceConfigManager.lambdaQuery()
                .eq(ResourcePriceConfigPO::getTenantCode, request.getTenantCode())
                .eq(Objects.nonNull(request.getStatus()), ResourcePriceConfigPO::getStatus, request.getStatus())
                .eq(Objects.nonNull(request.getResourceType()), ResourcePriceConfigPO::getResourceType,
                        request.getResourceType())
                .orderByAsc(request.isSortByOrder(), ResourcePriceConfigPO::getSortOrder)
                .list();
        if (CollectionUtils.isEmpty(configs)) {
            return ResultModel.success(Collections.EMPTY_LIST);
        }
        return ResultModel.success(configs.stream()
                .map(dto -> ResourcePriceConfigResponseDTO.builder()
                        .price(dto.getPrice())
                        .resourceType(dto.getResourceType())
                        .channel(dto.getChannel())
                        .tenantCode(dto.getTenantCode())
                        .order(dto.getSortOrder())
                        .build())
                .collect(Collectors.toList()));
    }
}
