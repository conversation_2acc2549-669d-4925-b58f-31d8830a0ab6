package com.dl.aiservice.biz.manager.train.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.dal.mapper.TrainResultMapper;
import com.dl.aiservice.biz.dal.po.TrainResultPO;
import com.dl.aiservice.biz.manager.train.TrainResultManager;
import com.dl.aiservice.biz.manager.train.bo.TrainResultBaseInfoSaveBO;
import com.dl.aiservice.biz.manager.train.bo.TrainResultPageBO;
import com.dl.aiservice.biz.manager.train.convert.TrainResultConvert;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-28 15:43
 */
@Component
public class TrainResultManagerImpl extends ServiceImpl<TrainResultMapper, TrainResultPO>
        implements TrainResultManager {
    @Resource
    private HostTimeIdg hostTimeIdg;

    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED)
    public TrainResultPO saveTrainResultBaseInfo(TrainResultBaseInfoSaveBO saveBO) {
        TrainResultPO existPO = this
                .getOne(Wrappers.lambdaQuery(TrainResultPO.class).eq(TrainResultPO::getChannel, saveBO.getChannel())
                        .eq(TrainResultPO::getTrainType, saveBO.getTrainType())
                        .eq(TrainResultPO::getExtModelCode, saveBO.getExtModelCode())
                        .eq(TrainResultPO::getIsDeleted, Const.ZERO));

        //查询入参名称对应的记录
        TrainResultPO existNamePO = this
                .getOne(Wrappers.lambdaQuery(TrainResultPO.class).eq(TrainResultPO::getTrainType, saveBO.getTrainType())
                        .eq(TrainResultPO::getName, saveBO.getName()).eq(TrainResultPO::getIsDeleted, Const.ZERO));

        //新增
        if (Objects.isNull(existPO)) {
            if (Objects.nonNull(existNamePO)) {
                throw BusinessServiceException.getInstance("当前名称已存在，请更换名称");
            }
            TrainResultPO insertPO = TrainResultConvert.cnvTrainResultBaseInfoSaveBO2PO(saveBO);
            insertPO.setBizId(hostTimeIdg.generateId().longValue());
            this.save(insertPO);
            return insertPO;
        }

        //修改
        if (StringUtils.isNotBlank(saveBO.getName())) {
            if (Objects.nonNull(existNamePO) && !existNamePO.getBizId().equals(existPO.getBizId())) {
                throw BusinessServiceException.getInstance("当前名称已存在，请更换名称");
            }
            existPO.setName(saveBO.getName());
        }
        if (Objects.nonNull(saveBO.getStatus())) {
            existPO.setStatus(saveBO.getStatus());
        }
        if (Objects.nonNull(saveBO.getTrainedNum())) {
            existPO.setTrainedNum(saveBO.getTrainedNum());
        }
        existPO.setModifyDt(new Date());
        this.updateById(existPO);

        return existPO;
    }

    @Override
    public IPage<TrainResultPO> page(TrainResultPageBO pageBO) {
        LambdaQueryWrapper<TrainResultPO> wrapper = Wrappers.lambdaQuery(TrainResultPO.class)
                .like(StringUtils.isNotBlank(pageBO.getName()), TrainResultPO::getName, pageBO.getName())
                .eq(StringUtils.isNotBlank(pageBO.getExtModelCode()), TrainResultPO::getExtModelCode,
                        pageBO.getExtModelCode())
                .eq(Objects.nonNull(pageBO.getChannel()), TrainResultPO::getChannel, pageBO.getChannel())
                .eq(Objects.nonNull(pageBO.getTrainType()), TrainResultPO::getTrainType, pageBO.getTrainType())
                .eq(Objects.nonNull(pageBO.getStatus()), TrainResultPO::getStatus, pageBO.getStatus())
                .eq(TrainResultPO::getIsDeleted, Const.ZERO).orderByDesc(TrainResultPO::getId);

        return this.page(new Page<>(pageBO.getPageIndex(), pageBO.getPageSize()), wrapper);
    }
}
