package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/7 17:02
 */
@Data
@NoArgsConstructor
public class IvhTask {


    /**
     * ⾳频制作的任务ID，携带TaskId访问<⾳视频制作进度查询接⼝>，可获得该⾳频的
     * 制作进度和下载地址，⾳频格式为mp3。
     */
    @JsonProperty(value = "TaskId")
    public String TaskId;


}