package com.dl.aiservice.biz.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.dl.aiservice.biz.common.po.BasePO;
import lombok.Data;

/**
 * 回调记录表
 * @TableName callback_log
 */
@TableName(value ="callback_log")
@Data
public class CallbackLogPO extends BasePO {
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 三方事件唯一id
     */
    @TableField(value = "ext_job_id")
    private String extJobId;

    /**
     * 回调类型：1 视频合成回调 2数字人训练 3声纹训练
     */
    @TableField(value = "callback_type")
    private Integer callbackType;

    /**
     * 渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    @TableField(value = "channel")
    private Integer channel;

    /**
     * 渠道回调响应原文，json串
     */
    @TableField(value = "ext_callback_resp_body")
    private String extCallbackRespBody;

    /**
     * 回调定力业务响应原文，json串
     */
    @TableField(value = "callback_resp_body")
    private String callbackRespBody;

    /**
     * 回调定力业务重试次数
     */
    @TableField(value = "retry_count")
    private Integer retryCount;

    /**
     * 回调定力业务状态 0回调中 1 成功 2失败 
     */
    @TableField(value = "status")
    private Integer status;

}