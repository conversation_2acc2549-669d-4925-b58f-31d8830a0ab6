package com.dl.aiservice.biz.service.digital.dto.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetResourceRequestDTO implements Serializable {


    private static final long serialVersionUID = -1301231693693196145L;
    /**
     * 为false时查询当前appkey对应资源，为true时查询当前⽤户所有资源
     */
    private Boolean getAllResource;
    /**
     * 主播code
     */
    private String anchorCode;

}
