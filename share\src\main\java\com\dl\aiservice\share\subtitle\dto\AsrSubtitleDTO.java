package com.dl.aiservice.share.subtitle.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @describe: AsrSubtitleDTO
 * @author: zhousx
 * @date: 2023/3/25 20:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AsrSubtitleDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一id
     * 调用方传入
     */
    private Integer uniqId;

    private Integer timePointStart;

    private Integer timePointEnd;

    @NotBlank
    private String subtitle;

    /**
     * 词组列表
     */
    private List<AsrSubtitleWordsDTO> words;
}
