package com.dl.aiservice.web.controller.voiceclone.convert;

import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.dal.po.TrainResultPO;
import com.dl.aiservice.share.voiceclone.VoiceTrainJobDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultDTO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-06 16:48
 */
public class VoiceCloneConvert {

    public static VoiceTrainResultDTO cnvTrainResultPO2DTO(TrainResultPO input) {
        VoiceTrainResultDTO result = new VoiceTrainResultDTO();
        result.setBizId(input.getBizId());
        result.setName(input.getName());
        result.setExtModelCode(input.getExtModelCode());
        result.setChannel(input.getChannel());
        result.setUserId(input.getUserId());
        result.setTenantCode(input.getTenantCode());
        result.setTrainType(input.getTrainType());
        result.setSampleLink(input.getSampleLink());
        result.setStatus(input.getStatus());
        result.setTrainedNum(input.getTrainedNum());
        result.setLatestFailReason(input.getLatestFailReason());
        return result;
    }

    public static VoiceTrainJobDTO cnvTrainJobPO2DTO(TrainJobPO input) {
        VoiceTrainJobDTO result = new VoiceTrainJobDTO();
        result.setTenantCode(input.getTenantCode());
        result.setChannel(input.getChannel());
        result.setJobType(input.getJobType());
        result.setGender(input.getGender());
        result.setTrainJobId(input.getTrainJobId());
        result.setExtJobId(input.getExtJobId());
        result.setExtModelCode(input.getExtModelCode());
        result.setStatus(input.getStatus());
        result.setTrainName(input.getTrainName());
        result.setFailCode(input.getFailCode());
        result.setFailReason(input.getFailReason());
        result.setSampleLink(input.getSampleLink());
        result.setSource(input.getSource());
        result.setCreateDt(input.getCreateDt());
        return result;
    }
}
