package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class IvhVirtualmanImageResources {


    /**
     * 申请的AppKey
     */
    @JsonProperty(value = "AppKey")
    private String appKey;

    /**
     * 该AppKey对应的数智⼈资源
     */
    @JsonProperty(value = "Virtualmans")
    private List<IvhVirtualmanImage> virtualmans;

}