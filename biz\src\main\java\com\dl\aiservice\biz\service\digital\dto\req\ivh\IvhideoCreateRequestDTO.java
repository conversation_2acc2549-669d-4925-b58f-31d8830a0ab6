package com.dl.aiservice.biz.service.digital.dto.req.ivh;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IvhideoCreateRequestDTO implements  Serializable {


    private static final long serialVersionUID = 5665417990096231528L;


//    /**
//     * 播报的⽂本内容，⽀持ssml标签，⽀持的标签类型参照附录2，标签写法参照示例，内容不能换⾏，符参数类型必须说明号需转义。2d播报上限1万字；3d播报上限500字。DriverType为空、或Text时，该字段必填
//     */
//    private String inputSsml;
    /**
     * 定义⾳频的详细参数
     */
    public IvhSpeedRequestDTO speechParam;
    /**
     * 定义合成视频的详细参数，
     */
    public IvhVideoAdvancedRequestDTO videoParam;


}
