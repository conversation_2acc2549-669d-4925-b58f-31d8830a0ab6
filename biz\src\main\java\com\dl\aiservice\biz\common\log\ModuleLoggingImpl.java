package com.dl.aiservice.biz.common.log;

import com.dl.aiservice.biz.filter.RepeatedlyRequestWrapper;
import com.dl.framework.common.context.RequestContext;
import com.dl.framework.common.logging.ModuleLogging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Component
public class ModuleLoggingImpl implements ModuleLogging {

    @Autowired
    private HttpServletRequest request;

    @Override
    public void writeExceptionByInfo(Exception ex) {
        // 打印日志
        log.info("接口:{}, 请求参数:{}", request.getRequestURI(), RepeatedlyRequestWrapper.getBodyString(request), ex);
    }

    @Override
    public void writeSimpleExceptionByInfo(Exception ex) {
        RequestContext requestContext = RequestContext.getCurrentContext();
        if (requestContext != null) {
            // 打印日志
            log.info("接口:{}, 请求参数:{}", request.getRequestURI(), RepeatedlyRequestWrapper.getBodyString(request));
        } else {
            log.info("接口:{}", request.getRequestURI(), ex);
        }
    }

    @Override
    public void writeExceptionByWarn(Exception ex) {
        RequestContext requestContext = RequestContext.getCurrentContext();
        if (requestContext != null) {
            // 打印日志
            log.warn("接口:{}, 请求参数:{}", request.getRequestURI(), RepeatedlyRequestWrapper.getBodyString(request), ex);
        } else {
            log.info("接口:{}", request.getRequestURI(), ex);
        }
    }

    @Override
    public void writeExceptionByError(Exception ex) {
        RequestContext requestContext = RequestContext.getCurrentContext();
        if (requestContext != null) {
            // 打印日志
            log.error("接口:{}, 请求参数:{}", request.getRequestURI(), RepeatedlyRequestWrapper.getBodyString(request), ex);
        }
    }

    @Override
    public void writeEmailLogByError(Exception ex) {
        log.error(ex.getMessage(), ex);
    }

}
