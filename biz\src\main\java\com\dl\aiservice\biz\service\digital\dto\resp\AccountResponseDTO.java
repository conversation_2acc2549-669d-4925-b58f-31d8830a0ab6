package com.dl.aiservice.biz.service.digital.dto.resp;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class AccountResponseDTO implements Serializable {

    private static final long serialVersionUID = -7678822472084440557L;
    /**
     * 账户余额
     */
    private Float balance;
    /**
     * 剩余时长（秒）
     */
    private String duration;
    /**
     * 消耗市场（秒）
     */
    private String durationCost;
    /**
     * 赠送余额
     */
    private Float giftBalance;
    /**
     * 模特私有市场汇总（秒）
     */
    private Integer privateDuration;
    /**
     * 总消费额度
     */
    private Float totalCost;
    /**
     * 通用时长汇总（秒）
     */
    private Integer universalDuration;
    /**
     * 音频合成剩余时长（单位：秒）
     */
    private Integer ttsDuration;
    /**
     * 剩余训练次数
     */
    private Integer trainTime;
    /**
     * 合同总金额
     */
    private Integer totalAmount;
    /**
     * Ai绘画（单位：次）
     */
    private Integer aiPanting;
    /**
     * Ai漫画（单位：次）
     */
    private Integer aiCartoon;

}

