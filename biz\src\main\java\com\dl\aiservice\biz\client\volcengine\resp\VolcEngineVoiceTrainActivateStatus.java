package com.dl.aiservice.biz.client.volcengine.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-26 16:44
 */
@Data
public class VolcEngineVoiceTrainActivateStatus {

    /**
     * unix epoch create time in millisecond
     */
    @JsonProperty(value = "CreateTime")
    private Long createTime;

    /**
     * http demo link
     */
    @JsonProperty(value = "DemoAudio")
    private String demoAudio;

    /**
     * volcengine Instance Number
     */
    @JsonProperty(value = "InstanceNO")
    private String instanceNO;

    /**
     * if this speakerID can be updated
     */
    @JsonProperty(value = "IsActivable")
    private Boolean isActivable;

    @JsonProperty(value = "SpeakerID")
    private String speakerID;

    /**
     * state of speakerID
     */
    @JsonProperty(value = "State")
    private String state;

    /**
     * version of speakerID
     */
    @JsonProperty(value = "Version")
    private String version;
    
}
