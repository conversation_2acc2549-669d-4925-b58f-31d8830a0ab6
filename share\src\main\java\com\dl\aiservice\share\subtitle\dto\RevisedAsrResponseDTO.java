package com.dl.aiservice.share.subtitle.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @describe: 校验后的ASR字幕结果
 * @author: zhousx
 * @date: 2023/3/25 19:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RevisedAsrResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String bizId;

    private List<RevisedAsrSubtitleDTO> revisedAsrSubtitles;
}
