package com.dl.aiservice.biz.manager;

import com.dl.aiservice.biz.dal.po.ResourcePriceConfigPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.aiservice.biz.manager.dto.ResourcePriceConfigDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【resource_price_config(资源单价配置)】的数据库操作Service
 * @createDate 2023-03-16 11:54:30
 */
public interface ResourcePriceConfigManager extends IService<ResourcePriceConfigPO> {
    /**
     * 功能描述: <br>
     *
     * @Param: [tenantCode]
     * @Return: java.util.List<com.dl.aiservice.biz.manager.dto.ResourcePriceConfigDTO>
     * @Author: zhousx
     * @Date: 2023/3/16 14:44
     */
    List<ResourcePriceConfigDTO> listPriceConfigsByTenant(String tenantCode, Integer status);
}
