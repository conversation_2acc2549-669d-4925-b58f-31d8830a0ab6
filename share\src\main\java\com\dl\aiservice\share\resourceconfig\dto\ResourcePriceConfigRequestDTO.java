package com.dl.aiservice.share.resourceconfig.dto;

import lombok.Data;
import lombok.Getter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @describe: ResourcePriceConfigRequestDTO
 * @author: zhousx
 * @date: 2023/3/16 15:20
 */
@Data
public class ResourcePriceConfigRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "租户编号必填")
    private String tenantCode;

    private Integer status;

    /**
     * 资源类型 1-视频合成，2-数字人，3-克隆音
     */
    private Integer resourceType;

    private boolean sortByOrder;
}
