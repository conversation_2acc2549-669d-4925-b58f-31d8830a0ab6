package com.dl.aiservice.share.digitalman;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @describe: DmSubtitleResponseDTO
 * @author: zhousx
 * @date: 2023/6/17 16:29
 */
@Data
public class DmSubtitleResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 数字人字幕
     */
    @ApiModelProperty(value = "数字人字幕")
    private List<DmSubtitleDTO> subtitles;
}
