package com.dl.aiservice.biz.client.guiji.support;

import com.dl.aiservice.biz.service.digital.guiji.GJDigitalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class GJDigitalManager {

    private final GJDigitalService gjDigitalService;

    @Autowired
    public GJDigitalManager(GJDigitalService gjDigitalService) {
        this.gjDigitalService = gjDigitalService;
    }

    /**
     * 硅基数字人服务
     *
     * @return GJDigitalService
     */
    public GJDigitalService gjDigitalService() {
        return gjDigitalService;
    }
}
