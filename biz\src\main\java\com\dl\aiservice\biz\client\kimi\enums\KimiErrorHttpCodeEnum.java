package com.dl.aiservice.biz.client.kimi.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 14:43
 */
public enum KimiErrorHttpCodeEnum {
    invalid_authentication_error(401, "鉴权失败"),
    invalid_request_error(400, "输入格式有误"),
    rate_limit_reached_error(429, "当前请求过多，请稍后再试"),
    exceeded_current_quota_error(429, "账号余额不足，请联系管理员充值");

    private Integer code;

    private String desc;

    KimiErrorHttpCodeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KimiErrorHttpCodeEnum parse(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (KimiErrorHttpCodeEnum codeEnum : KimiErrorHttpCodeEnum.values()) {
            if (codeEnum.getCode().equals(code)) {
                return codeEnum;
            }
        }
        return null;
    }
}
