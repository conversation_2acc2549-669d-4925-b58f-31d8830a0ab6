package com.dl.aiservice.biz.manager.digitalasset.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.dal.mapper.DaVirtualManScenesMapper;
import com.dl.aiservice.biz.dal.po.DaTenantAuthPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManSceneQueryPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManScenesPO;
import com.dl.aiservice.biz.manager.digitalasset.DaTenantAuthManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualManScenesManager;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManScenesBO;
import com.dl.aiservice.biz.manager.digitalasset.conf.DaOldNewBizIdConfig;
import com.dl.aiservice.biz.manager.digitalasset.enums.DaTenantAuthBizTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【da_virtual_man_scenes(数字资产-仿真人场景信息表)】的数据库操作Service实现
 * @createDate 2023-06-02 13:53:14
 */
@Service
public class DaVirtualManScenesManagerImpl extends ServiceImpl<DaVirtualManScenesMapper, DaVirtualManScenesPO>
        implements DaVirtualManScenesManager {

    @Resource
    private DaTenantAuthManager daTenantAuthManager;

    @Resource
    private DaOldNewBizIdConfig daOldNewBizIdConfig;

    @Override
    public List<DaVirtualManScenesBO> listVirtualManScene(String tenantCode, Long bizId, Integer enableFilter) {
        Assert.isTrue(StringUtils.isNotBlank(tenantCode), "租户编号必填");

        //优先判断该bizId是否有对应的新bizId，若有则查询新bizId的数据
        if (daOldNewBizIdConfig.getVmOldNewBizIdMap().containsKey(bizId)) {
            bizId = daOldNewBizIdConfig.getVmOldNewBizIdMap().get(bizId);
        }
        DaTenantAuthPO authVmPO = daTenantAuthManager
                .getOne(Wrappers.lambdaQuery(DaTenantAuthPO.class).eq(DaTenantAuthPO::getTenantCode, tenantCode)
                        .eq(DaTenantAuthPO::getBizType, DaTenantAuthBizTypeEnum.DIGITAL_MAN.getType())
                        .eq(DaTenantAuthPO::getBizId, bizId).eq(DaTenantAuthPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(authVmPO)) {
            return Collections.emptyList();
        }
        return this.baseMapper.listVirtualManScene(Collections.singletonList(bizId), enableFilter);
    }

    @Override
    public List<DaVirtualManScenesBO> listVirtualManSceneByBizIds(String tenantCode, List<Long> bizIdList, Integer enableFilter) {
        Assert.isTrue(StringUtils.isNotBlank(tenantCode), "租户编号必填");

        bizIdList = bizIdList.stream().map(bizId -> {
            //优先判断该bizId是否有对应的新bizId，若有则查询新bizId的数据
            if (daOldNewBizIdConfig.getVmOldNewBizIdMap().containsKey(bizId)) {
                return daOldNewBizIdConfig.getVmOldNewBizIdMap().get(bizId);
            }
            return bizId;
        }).collect(Collectors.toList());

        List<DaTenantAuthPO> authVmList = daTenantAuthManager
                .list(Wrappers.lambdaQuery(DaTenantAuthPO.class).eq(DaTenantAuthPO::getTenantCode, tenantCode)
                        .eq(DaTenantAuthPO::getBizType, DaTenantAuthBizTypeEnum.DIGITAL_MAN.getType())
                        .in(DaTenantAuthPO::getBizId, bizIdList).eq(DaTenantAuthPO::getIsDeleted, Const.ZERO));
        if (CollectionUtils.isEmpty(authVmList)) {
            return Collections.emptyList();
        }
        bizIdList = authVmList.stream().map(DaTenantAuthPO::getBizId).collect(Collectors.toList());
        return this.baseMapper.listVirtualManScene(bizIdList, enableFilter);
    }

    @Override
    public List<DaVirtualManScenesBO> listVirtualManSceneWithoutAuthCheck(Long bizId, Integer enableFilter) {
        Assert.notNull(bizId, "数字人bizId必填");

        //优先判断该bizId是否有对应的新bizId，若有则查询新bizId的数据
        if (daOldNewBizIdConfig.getVmOldNewBizIdMap().containsKey(bizId)) {
            bizId = daOldNewBizIdConfig.getVmOldNewBizIdMap().get(bizId);
        }

        return this.baseMapper.listVirtualManScene(Collections.singletonList(bizId), enableFilter);
    }

    @Override
    public IPage<DaVirtualManScenesBO> pageVirtualMan(DaVirtualManSceneQueryPO query) {
        IPage page = new Page<DaVirtualManScenesBO>(query.getPageIndex(), query.getPageSize());
        Integer totalCount = baseMapper.countVirtualManScene(query);
        if (Objects.isNull(totalCount) || totalCount < Const.ONE) {
            return page;
        }
        page.setTotal(totalCount);
        page.setRecords(baseMapper.pageVirtualManScene(query));
        return page;
    }

    @Override
    public List<DaVirtualManScenesBO> listVirtualManSceneBySceneIdsWithoutAuthCheck(List<String> sceneIdList,
            List<Integer> channelList, Integer enableFilter) {
        return baseMapper.listVirtualManSceneBySceneIds(sceneIdList, channelList, enableFilter);
    }
}




