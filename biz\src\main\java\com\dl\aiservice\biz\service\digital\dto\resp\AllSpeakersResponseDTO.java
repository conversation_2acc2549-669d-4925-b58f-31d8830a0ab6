package com.dl.aiservice.biz.service.digital.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AllSpeakersResponseDTO implements Serializable {
    private static final long serialVersionUID = -2251438404882119296L;
    /**
     * 发言人ID
     */
    private String id;
    /**
     * 发言人名
     */
    private String ttsName;
    /**
     * 介绍
     */
    private String ttsIntroduction;
    /**
     * 使用场景
     */
    private String ttsScenes;
    /**
     * 发言人标识
     */
    private String ttsSpeaker;
    /**
     * 特色
     */
    private String ttsFeatures;
    /**
     * 示例音频地址
     */
    private String ttsAudition;
    /**
     * 封面地址
     */
    private String ttsCover;
    /**
     * 支持的语言类型列表 cn-中文 en-英文
     */
    public List<String> languages;

}

