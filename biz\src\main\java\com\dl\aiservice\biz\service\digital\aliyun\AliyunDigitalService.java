package com.dl.aiservice.biz.service.digital.aliyun;

import com.dl.aiservice.biz.service.digital.BaseDigitalService;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.model.ResultModel;

/**
 * @author: xuebin
 * @description 数字人针对阿里云特有的接口
 */
public interface AliyunDigitalService extends BaseDigitalService {

    /**
     * tts试听接⼝
     *
     * @param request 请求参数
     * @return 返回参数
     */
    ResultModel<TTSResponseDTO> listenTts(TTSProduceParamDTO request);

}

