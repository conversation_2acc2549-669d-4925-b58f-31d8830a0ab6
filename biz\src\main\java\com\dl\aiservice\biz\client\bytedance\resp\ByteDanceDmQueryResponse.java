package com.dl.aiservice.biz.client.bytedance.resp;

import com.dl.aiservice.biz.common.constant.Const;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Objects;

/**
 * @describe: ByteDanceDmQueryResponse
 * @author: zhousx
 * @date: 2023/10/8 15:16
 */
@Data
public class ByteDanceDmQueryResponse {
    public boolean isSuccess() {
        return Objects.equals(Const.ZERO, code);
    }

    private Integer code;

    private String message;

    private String logid;

    private RespData data;

    @Data
    public static class RespData {
        @JsonProperty("task_id")
        private String taskId;

        @JsonProperty("task_status")
        private Integer taskStatus;

        @JsonProperty("video_url")
        private String videoUrl;

        @JsonProperty("failure_reason")
        private String failureReason;

        @JsonProperty("video_duration")
        private Long duration;
    }
}
