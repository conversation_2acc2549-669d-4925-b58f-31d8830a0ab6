package com.dl.aiservice.biz.common.util;

import com.dtflys.forest.logging.LogBodyMessage;
import com.dtflys.forest.logging.LogHeaderMessage;
import com.dtflys.forest.logging.RequestLogMessage;
import com.dtflys.forest.utils.StringUtils;

import java.util.List;

/**
 * @ClassName ForestLogUtil
 * @Description
 * <AUTHOR>
 * @Date 2023/3/14 20:22
 * @Version 1.0
 **/
public class ForestLogUtil {

    public static String requestLoggingContent(RequestLogMessage requestLogMessage) {
        StringBuilder builder = new StringBuilder();
        builder.append("Request: \n\t");
        builder.append(requestLogMessage.getRequestLine());
        String headers = requestLoggingHeaders(requestLogMessage);
        if (StringUtils.isNotEmpty(headers)) {
            builder.append("\n\tHeaders: \n");
            builder.append(headers);
        }
        String body = requestLoggingBody(requestLogMessage);
        if (StringUtils.isNotEmpty(body)) {
            builder.append("\n\tBody: ");
            builder.append(body);
        }
        return builder.toString();
    }

    protected static String requestLoggingHeaders(RequestLogMessage requestLogMessage) {
        StringBuilder builder = new StringBuilder();
        List<LogHeaderMessage> headers = requestLogMessage.getHeaders();
        if (headers == null) {
            return "";
        }
        for (int i = 0; i < headers.size(); i++) {
            LogHeaderMessage headerMessage = headers.get(i);
            String name = headerMessage.getName();
            String value = headerMessage.getValue();
            builder.append("\t\t" + name + ": " + value);
            if (i < headers.size() - 1) {
                builder.append("\n");
            }
        }
        return builder.toString();
    }

    protected static String requestLoggingBody(RequestLogMessage requestLogMessage) {
        LogBodyMessage logBodyMessage = requestLogMessage.getBody();
        if (logBodyMessage == null) {
            return "";
        }
        return logBodyMessage.getBodyString();
    }

}
