package com.dl.aiservice.web.controller.digitalasset;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.transaction.TransactionProxyManager;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.dal.po.DaTenantAuthPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManPO;
import com.dl.aiservice.biz.dal.po.DaVirtualVoicePO;
import com.dl.aiservice.biz.manager.digitalasset.DaTenantAuthManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualManManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualVoiceManager;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoiceListQueryPairBO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoicePageBO;
import com.dl.aiservice.biz.manager.digitalasset.enums.DaTenantAuthBizTypeEnum;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceAuthorizeDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceBaseInfoDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceChannelDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDetailDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceListQueryDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoicePageDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceUpdateDTO;
import com.dl.aiservice.web.controller.digitalasset.convert.DigitalAssetConvert;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/digital/voice")
public class DigitalVoiceController {
    private static final String DEFAULT_DL = "DEFAULT";

    @Resource
    private DaVirtualVoiceManager daVirtualVoiceManager;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private DaTenantAuthManager daTenantAuthManager;
    @Resource
    private DaVirtualManManager daVirtualManManager;
    @Resource
    private TransactionProxyManager transactionProxyManager;
    @Resource
    private ChannelUtil channelUtil;

    @PostMapping("/page")
    public ResultPageModel<DaVirtualVoiceDTO> voicePage(@RequestBody DaVirtualVoicePageDTO param) {
        DaVirtualVoicePageBO queryBO = new DaVirtualVoicePageBO();
        BeanUtils.copyProperties(param, queryBO);
        queryBO.setCurrent(param.getPageIndex());
        queryBO.setSize(param.getPageSize());
        IPage<DaVirtualVoicePO> pageResult = daVirtualVoiceManager.pageVoice(queryBO);
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new ResultPageModel<>();
        }

        //查询授权列表
        List<DaTenantAuthPO> tenantAuthPOList = daTenantAuthManager.list(Wrappers.lambdaQuery(DaTenantAuthPO.class)
                .in(DaTenantAuthPO::getBizId,
                        pageResult.getRecords().stream().map(DaVirtualVoicePO::getBizId).collect(Collectors.toList()))
                .eq(DaTenantAuthPO::getIsDeleted, Const.ZERO)
                .eq(DaTenantAuthPO::getBizType, DaTenantAuthBizTypeEnum.VOICE.getType()));

        //key-bizId
        Map<Long, List<DaTenantAuthPO>> tenantAuthMap = tenantAuthPOList.stream()
                .collect(Collectors.groupingBy(DaTenantAuthPO::getBizId));

        ResultPageModel<DaVirtualVoiceDTO> resultPage = new ResultPageModel<>();
        resultPage.setDataResult(pageResult.getRecords().stream().map(voice -> {
            DaVirtualVoiceDTO result = DigitalAssetConvert.cnvDaVirtualVoicePO2DTO(voice, channelUtil.getTenantCode());

            List<DaTenantAuthPO> tenantAuthPOS = tenantAuthMap.get(voice.getBizId());
            if (CollectionUtils.isNotEmpty(tenantAuthPOS)) {
                result.setTenantCodeList(
                        tenantAuthPOS.stream().map(DaTenantAuthPO::getTenantCode).collect(Collectors.toList()));
            }
            return result;
        }).collect(Collectors.toList()));
        resultPage.setPageIndex(pageResult.getCurrent());
        resultPage.setPageSize(pageResult.getSize());
        resultPage.setTotal(pageResult.getTotal());
        resultPage.setTotalPage(pageResult.getPages());
        return resultPage;
    }


    @PostMapping("/pageSearch")
    ResultPageModel<DaVirtualVoiceDTO> pageSearch(@RequestBody DaVirtualVoicePageDTO param) {
        DaVirtualVoicePageBO queryBO = new DaVirtualVoicePageBO();
        BeanUtils.copyProperties(param, queryBO);
        queryBO.setCurrent(param.getPageIndex());
        queryBO.setSize(param.getPageSize());
        queryBO.setIsEnabled(Const.ONE);
        IPage<DaVirtualVoicePO> pageResult = daVirtualVoiceManager.pageVoice(queryBO);
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new ResultPageModel<>();
        }

        ResultPageModel<DaVirtualVoiceDTO> resultPage = new ResultPageModel<>();
        resultPage.setDataResult(pageResult.getRecords().stream()
                .map(voice -> DigitalAssetConvert.cnvDaVirtualVoicePO2DTO(voice, channelUtil.getTenantCode()))
                .collect(Collectors.toList()));
        resultPage.setPageIndex(pageResult.getCurrent());
        resultPage.setPageSize(pageResult.getSize());
        resultPage.setTotal(pageResult.getTotal());
        resultPage.setTotalPage(pageResult.getPages());
        return resultPage;

    }

    @PostMapping("/detail")
    ResultModel<DaVirtualVoiceDTO> detail(@RequestBody DaVirtualVoiceDetailDTO param) {
        DaVirtualVoicePO daVirtualVoicePO = daVirtualVoiceManager.lambdaQuery()
                .eq(DaVirtualVoicePO::getBizId, param.getVoiceBizId()).one();

        if (Objects.isNull(daVirtualVoicePO)) {
            return ResultModel.success(null);
        }
        DaVirtualVoiceDTO result = DigitalAssetConvert
                .cnvDaVirtualVoicePO2DTO(daVirtualVoicePO, channelUtil.getTenantCode());
        return ResultModel.success(result);
    }

    @PostMapping("/saveOrUpdate")
    public ResultModel vmSaveOrUpdate(@RequestBody DaVirtualVoiceDTO param) {
        DaVirtualVoicePO existVoiceKey = daVirtualVoiceManager.lambdaQuery()
                .eq(DaVirtualVoicePO::getVoiceKey, param.getVoiceKey())
                .eq(DaVirtualVoicePO::getChannel, param.getChannel()).eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO)
                .one();

        if (Objects.nonNull(existVoiceKey)) {
            if (Objects.isNull(param.getBizId())) {
                throw BusinessServiceException.getInstance("声音key[" + param.getVoiceKey() + "]已存在");
            }
            if (!param.getBizId().equals(existVoiceKey.getBizId())) {
                throw BusinessServiceException.getInstance("声音key[" + param.getVoiceKey() + "]已存在");
            }
        }

        //校验voiceName唯一
        DaVirtualVoicePO existVoiceName = daVirtualVoiceManager.lambdaQuery()
                .eq(DaVirtualVoicePO::getVoiceName, param.getVoiceName())
                .eq(DaVirtualVoicePO::getChannel, param.getChannel()).eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO)
                .one();
        if (Objects.nonNull(existVoiceName)) {
            if (Objects.isNull(param.getBizId())) {
                throw BusinessServiceException.getInstance("名称已存在，请换一个命名");
            }
            if (!param.getBizId().equals(existVoiceName.getBizId())) {
                throw BusinessServiceException.getInstance("名称已存在，请换一个命名");
            }
        }

        DaVirtualVoicePO virtualVoicePO = DigitalAssetConvert.cnvDaVirtualVoiceDTO2PO(param);

        //修改
        if (Objects.nonNull(param.getBizId())) {
            DaVirtualVoicePO vmExist = daVirtualVoiceManager.lambdaQuery()
                    .eq(DaVirtualVoicePO::getBizId, param.getBizId()).eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO)
                    .one();
            Assert.notNull(vmExist, String.format("声音[%s]不存在", param.getBizId()));

            transactionProxyManager.process(() -> {
                daVirtualVoiceManager.update(virtualVoicePO, Wrappers.lambdaUpdate(DaVirtualVoicePO.class)
                        .eq(DaVirtualVoicePO::getChannel, vmExist.getChannel())
                        .eq(DaVirtualVoicePO::getBizId, vmExist.getBizId()));

                if (!Objects.equals(vmExist.getVoiceKey(), param.getVoiceKey())) {
                    // 更新操作,更新声音表中的voiceKey
                    virtualVoicePO.setBizId(null);
                    virtualVoicePO.setTenantCode(null);
                    daVirtualVoiceManager.update(virtualVoicePO, Wrappers.lambdaUpdate(DaVirtualVoicePO.class)
                            .eq(DaVirtualVoicePO::getChannel, vmExist.getChannel())
                            .eq(DaVirtualVoicePO::getVoiceKey, vmExist.getVoiceKey()));
                    // 更新数字人中关联的voiceKey
                    daVirtualManManager.update(Wrappers.lambdaUpdate(DaVirtualManPO.class)
                            .eq(DaVirtualManPO::getVoiceBizId, vmExist.getBizId())
                            .eq(DaVirtualManPO::getIsDeleted, Const.ZERO)
                            .set(DaVirtualManPO::getVmVoiceKey, param.getVoiceKey())
                            .set(DaVirtualManPO::getModifyDt, new Date()));
                }

                if (CollectionUtils.isNotEmpty(param.getTenantCodeList())) {
                    daVirtualVoiceManager.voiceAuth(param.getBizId(), param.getTenantCodeList());
                }
            });
            return ResultModel.success(null);
        }

        // 新增操作
        transactionProxyManager.process(() -> {
            virtualVoicePO.setBizId(hostTimeIdg.generateId().longValue());
            daVirtualVoiceManager.save(virtualVoicePO);

            if (CollectionUtils.isNotEmpty(param.getTenantCodeList())) {
                daVirtualVoiceManager.voiceAuth(virtualVoicePO.getBizId(), param.getTenantCodeList());
            }
        });

        return ResultModel.success(null);
    }

    @PostMapping("/authorize")
    public ResultModel voiceAuthorize(@RequestBody DaVirtualVoiceAuthorizeDTO param) {
        daVirtualVoiceManager.voiceAuth(param.getBizId(), param.getTenantCodeList());
        return ResultModel.success(null);
    }

    @PostMapping("/enable")
    ResultModel enable(@RequestBody DaVirtualVoiceUpdateDTO param) {
        DaVirtualVoicePO voicePO = daVirtualVoiceManager.lambdaQuery()
                .eq(DaVirtualVoicePO::getBizId, param.getBizId()).one();
        Assert.notNull(voicePO, "声音不存在");
        voicePO.setIsEnabled(param.getEnableState());

        if (param.getEnableState().equals(Const.ZERO)) {
            checkVoice(param, new StringBuilder("停用失败：关联"));
        }
        LambdaUpdateWrapper<DaVirtualVoicePO> update = Wrappers.<DaVirtualVoicePO>lambdaUpdate()
                .set(DaVirtualVoicePO::getIsEnabled, param.getEnableState())
                .eq(DaVirtualVoicePO::getChannel, voicePO.getChannel())
                .eq(DaVirtualVoicePO::getVoiceKey, voicePO.getVoiceKey());
        daVirtualVoiceManager.update(update);
        return ResultModel.success(null);
    }

    @PostMapping("/delete")
    ResultModel delete(@RequestBody DaVirtualVoiceUpdateDTO param) {
        DaVirtualVoicePO voicePO = daVirtualVoiceManager.lambdaQuery()
                .eq(DaVirtualVoicePO::getBizId, param.getBizId()).one();
        Assert.notNull(voicePO, "声音不存在");
        checkVoice(param, new StringBuilder("删除失败：关联"));
        LambdaUpdateWrapper<DaVirtualVoicePO> update = Wrappers.<DaVirtualVoicePO>lambdaUpdate()
                .set(DaVirtualVoicePO::getIsDeleted, Const.ONE)
                .eq(DaVirtualVoicePO::getChannel, voicePO.getChannel())
                .eq(DaVirtualVoicePO::getVoiceKey, voicePO.getVoiceKey());
        daVirtualVoiceManager.update(update);
        return ResultModel.success(null);
    }

    private void checkVoice(DaVirtualVoiceUpdateDTO param, StringBuilder s) {
        List<DaVirtualManPO> virtualManList = daVirtualManManager.lambdaQuery()
                .select(DaVirtualManPO::getVmName)
                .eq(DaVirtualManPO::getIsDeleted, Const.ZERO)
                .eq(DaVirtualManPO::getVoiceBizId, param.getBizId())
                .list().stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(virtualManList)) {
            for (DaVirtualManPO virtualManPO : virtualManList) {
                s.append("数字人").append(virtualManPO.getVmName()).append("、");
            }
            s = new StringBuilder(s.substring(0, s.length() - 1));
            s.append("，请先取消数字人关联");
            throw BusinessServiceException.getInstance(s.toString());
        }
    }

    @GetMapping("/channel")
    ResultModel<List<DaVirtualVoiceChannelDTO>> channel() {
        return ResultModel.success(daVirtualVoiceManager.getVoiceChannel());
    }

    @PostMapping("/listquery")
    public ResultModel<List<DaVirtualVoiceDTO>> listquery(@RequestBody DaVirtualVoiceRequestDTO param) {
        List<DaVirtualVoicePO> voicePOList = daVirtualVoiceManager.lambdaQuery()
                .eq(DaVirtualVoicePO::getIsDeleted, Const.ZERO).eq(DaVirtualVoicePO::getIsEnabled, Const.ONE)
                .in(CollectionUtils.isNotEmpty(param.getChannels()), DaVirtualVoicePO::getChannel, param.getChannels())
                .eq(Objects.nonNull(param.getVoiceType()), DaVirtualVoicePO::getVoiceType, param.getVoiceType())
                .eq(Objects.nonNull(param.getVoiceBizId()), DaVirtualVoicePO::getBizId, param.getVoiceBizId())
                .eq(StringUtils.isNotBlank(param.getVoiceKey()), DaVirtualVoicePO::getVoiceKey, param.getVoiceKey())
                .list();
        if (CollectionUtils.isEmpty(voicePOList)) {
            return ResultModel.success(Collections.emptyList());
        }
        return ResultModel.success(voicePOList.stream()
                .map(voicePO -> DigitalAssetConvert.cnvDaVirtualVoicePO2DTO(voicePO, channelUtil.getTenantCode()))
                .collect(Collectors.toList()));
    }

    /**
     * 根据厂商+声音编码列表查询声音列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/listbychannelandvoicekeylist")
    public ResultModel<List<DaVirtualVoiceBaseInfoDTO>> listByChannelAndVoiceKeyList(
            @RequestBody @Validated DaVirtualVoiceListQueryDTO queryDTO) {

        List<DaVirtualVoiceListQueryPairBO> pairBOList = queryDTO.getPairList().stream().map(pairDTO -> {
            DaVirtualVoiceListQueryPairBO bo = new DaVirtualVoiceListQueryPairBO();
            bo.setChannel(pairDTO.getChannel());
            bo.setVoiceKey(pairDTO.getVoiceKey());
            return bo;
        }).collect(Collectors.toList());

        List<DaVirtualVoicePO> voicePOList = daVirtualVoiceManager.listByChannelAndVoiceKeyList(pairBOList);
        return ResultModel.success(voicePOList.stream()
                .map(voicePO -> DigitalAssetConvert.cnvDaVirtualVoicePO2BaseDTO(voicePO, channelUtil.getTenantCode()))
                .collect(Collectors.toList()));
    }

}
