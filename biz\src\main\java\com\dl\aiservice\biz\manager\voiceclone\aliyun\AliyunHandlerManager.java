package com.dl.aiservice.biz.manager.voiceclone.aliyun;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.OutputFormatEnum;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizer;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerListener;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.enums.SymbolE;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.MediaUtil;
import com.dl.aiservice.biz.common.util.RedisUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.manager.subtitle.SubtitleManager;
import com.dl.aiservice.biz.manager.subtitle.aliyun.AliyunDslConfig;
import com.dl.aiservice.biz.manager.voiceclone.VoiceCloneHandlerManager;
import com.dl.aiservice.biz.manager.voiceclone.aliyun.enums.AliyunTtsErrorStatusEnum;
import com.dl.aiservice.biz.manager.voiceclone.grammartrans.impl.aliyun.AliyunGrammarTransformer;
import com.dl.aiservice.share.enums.MediaProduceJobStatusEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.HtmlUtils;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.dl.aiservice.biz.manager.subtitle.aliyun.AliyunDslConst.ACCESS_TOKEN_REDIS_KEY;

/**
 * @describe: AliyunHandlerManager
 * @author: zhousx
 * @date: 2023/5/6 17:30
 */
@Component
@Slf4j
public class AliyunHandlerManager implements VoiceCloneHandlerManager {
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private CosFileUploadManager cosFileUploadManager;
    @Autowired
    private MediaProduceJobManager mediaProduceJobManager;
    @Autowired
    private ChannelUtil channelUtil;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private SubtitleManager subtitleManager;
    @Autowired
    private AliyunDslConfig aliyunDslConfig;
    @Resource
    private AliyunGrammarTransformer aliyunGrammarTransformer;

    private static final String STATUS_TEXT_418 = "418";

    private static final String STATUS_TEXT_424 = "424";

    private static final String STATUS_TEXT_413 = "413";

    private static final String STATUS_TEXT_JSON_ILLEGAL = "Request json illegal,failed to parse request";

    private static final String STATUS_TEXT_TOO_LONG = "SSML text length should be less than 300";

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.ALIYUN);
    }

    @Override
    public ResultModel envCheck(String url) {
        return null;
    }

    @Override
    public ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request) {
        return null;
    }

    @Override
    public ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId) {
        return null;
    }

    @Override
    public ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO param) {
        Assert.notNull(param, "入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getVoiceName()), "voiceName入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getText()), "音频文本不能为空");

        TTSResponseDTO ttsResponseDTO = new TTSResponseDTO();
        SpeechSynthesizer synthesizer = null;
        MediaProduceJobPO job = new MediaProduceJobPO();
        Long mediaJobId = hostTimeIdg.generateId().longValue();
        ttsResponseDTO.setMediaJobId(mediaJobId);
        try {
            OutputFormatEnum format = formatCodec(param.getAudioEncode());
            //创建实例，建立连接。
            String filePath = mediaJobId + SymbolE.DOT.getValue() + format.getName();
            synthesizer = new SpeechSynthesizer(getClient(),
                    getSynthesizerListener(filePath, job, cosFileUploadManager, mediaProduceJobManager, subtitleManager,
                            ttsResponseDTO, param));
            synthesizer.setAppKey(aliyunDslConfig.getAppKey());
            //设置返回音频的编码格式
            synthesizer.setFormat(format);
            //设置返回音频的采样率
            synthesizer.setSampleRate(SampleRateEnum.SAMPLE_RATE_16K);
            //发音人
            synthesizer.setVoice(param.getVoiceName());
            //设置用于语音合成的文本
            synthesizer.setText(aliyunGrammarTransformer.grammarTransform(param.getText()));
            // 是否开启字幕功能（返回相应文本的时间戳），默认不开启，需要注意并非所有发音人都支持该参数。
            synthesizer.addCustomedParam("enable_subtitle", true);

            synthesizer.setVolume(formatVolume(param.getVolume()));

            if (StringUtils.isNotBlank(param.getSpeed())) {
                //语速，范围是-500~500，默认是0
                synthesizer.setSpeechRate(mapRangeDouble(Double.valueOf(param.getSpeed()), 0.5d, 1.5, -500, 500));
            }

            // 初始化请求数据
            job.setMediaJobId(mediaJobId);
            job.setTenantCode(channelUtil.getTenantCode());
            job.setWorksBizId(param.getWorksBizId());
            job.setVideoTaskJobId(param.getVideoTaskJobId());
            job.setChannel(ServiceChannelEnum.ALIYUN.getCode());
            job.setJobType(Const.TWO);
            job.setJobContent(JsonUtils.toJSON(synthesizer));
            job.setStatus(Const.ONE);
            job.setRequestDt(new Date());
            mediaProduceJobManager.save(job);

            synthesizer.start();
            //等待语音合成结束
            synthesizer.waitForComplete();

            //处理失败情况
            if (MediaProduceJobStatusEnum.FAIL.getStatus().equals(job.getStatus())) {
                return ResultModel.error("-1", "语音合成失败:" + job.getFailReason());
            }

            return ResultModel.success(ttsResponseDTO);
        } catch (BusinessServiceException e) {
            return ResultModel.error(e.getErrCode(), "语音合成失败:" + e.getMessage());
        } catch (Exception e) {
            job.setStatus(-Const.ONE);
            job.setFailReason(e.getMessage());
            job.setResponseDt(new Date());
            mediaProduceJobManager.updateById(job);
            log.error(e.getMessage(), e);
            return ResultModel.error("-1", "语音合成失败:" + e.getMessage());
        } finally {
            //关闭连接
            if (null != synthesizer) {
                synthesizer.close();
            }
        }
    }

    private static SpeechSynthesizerListener getSynthesizerListener(String filePath, MediaProduceJobPO job, CosFileUploadManager cosFileUploadManager, MediaProduceJobManager mediaProduceJobManager, SubtitleManager subtitleManager, TTSResponseDTO ttsResponseDTO, TTSProduceParamDTO param) throws Exception {
        Assert.isTrue(StringUtils.isNotBlank(filePath), "文件名不可为空。");
        SpeechSynthesizerListener listener = new SpeechSynthesizerListener() {
            File audioFile = new File(filePath);
            FileOutputStream fout = new FileOutputStream(audioFile);
            boolean firstRecvBinary = true;

            //语音合成结束
            @Override
            public void onComplete(SpeechSynthesizerResponse resp) {
                String audioUrl = cosFileUploadManager.uploadFile(audioFile, null, null);
                Double duration = MediaUtil.getAudioDuration(audioFile);
                FileUtil.del(audioFile.getAbsolutePath());

                job.setExtJobId(resp.getTaskId());
                job.setStatus(Const.ZERO);
                job.setMediaUrl(audioUrl);
                job.setDuration(duration);
                job.setResponseDt(new Date());
                mediaProduceJobManager.updateById(job);

                ttsResponseDTO.setAudioUrl(audioUrl);
                ttsResponseDTO.setDuration(duration);
            }

            //语音合成的语音二进制数据
            @Override
            public void onMessage(ByteBuffer message) {
                try {
                    if (firstRecvBinary) {
                        //计算首包语音流的延迟，收到第一包语音流时，即可以进行语音播放，以提升响应速度（特别是实时交互场景下）。
                        firstRecvBinary = false;
                    }
                    byte[] bytesArray = new byte[message.remaining()];
                    message.get(bytesArray, 0, bytesArray.length);
                    fout.write(bytesArray);
                } catch (IOException e) {
                    job.setStatus(-Const.ONE);
                    job.setFailReason(e.getMessage());
                    job.setResponseDt(new Date());
                    mediaProduceJobManager.updateById(job);
                    log.error(e.getMessage(), e);
                    throw BusinessServiceException.getInstance("-1", "处理语音流发生异常！" + e.getMessage());
                }
            }

            @Override
            public void onMetaInfo(SpeechSynthesizerResponse response) {
                /*Integer maxLength = param.getMaxLength();
                // 调用onMetaInfo时表示返回字级别时间戳
                JSONArray subtitles = (JSONArray) response.getObject("subtitles");
                //List<Map> wordList = subtitles.toJavaList(Map.class);
                log.info("阿里云TTS返回的字幕:subtitles:{}", JSONUtil.toJsonStr(subtitles));*/
                // {"begin_index":145,"phoneme":"null","end_time":27269,"end_index":146,"begin_time":26969,"text":"买"}
                /*int lastEndTime = 0;
                int beginTime = 0;
                List<AsrSubtitleDTO> subtitleList = new ArrayList<>();
                AsrSubtitleDTO subtitle = new AsrSubtitleDTO();
                StringBuilder sb = new StringBuilder();
                for (Map word : wordList) {
                    int wordBeginTime = Integer.valueOf(word.get("begin_time").toString());
                    int wordEndTime = Integer.valueOf(word.get("end_time").toString());
                    String text = word.get("text").toString();
                    if (lastEndTime == wordBeginTime) {
                        if (Objects.isNull(maxLength) || sb.length() < maxLength) {
                            sb.append(text);
                        } else {
                            subtitle.setSubtitle(sb.toString());
                            subtitle.setTimePointStart(beginTime);
                            subtitle.setTimePointEnd(lastEndTime);
                            subtitleList.add(subtitle);

                            subtitle = new AsrSubtitleDTO();
                            sb = new StringBuilder();
                            sb.append(text);
                            beginTime = wordBeginTime;
                        }
                    } else {
                        subtitle.setSubtitle(sb.toString());
                        subtitle.setTimePointStart(beginTime);
                        subtitle.setTimePointEnd(lastEndTime);
                        subtitleList.add(subtitle);

                        subtitle = new AsrSubtitleDTO();
                        sb = new StringBuilder();
                        sb.append(text);
                        beginTime = wordBeginTime;
                    }
                    lastEndTime = wordEndTime;
                }

                if (sb.length() > 0) {
                    subtitle.setSubtitle(sb.toString());
                    subtitle.setTimePointStart(beginTime);
                    subtitle.setTimePointEnd(lastEndTime);
                    subtitleList.add(subtitle);
                }
                ReviseAsrRequestDTO reviseAsrRequestDTO = new ReviseAsrRequestDTO();
                reviseAsrRequestDTO.setOriginalScript(HtmlUtils.deleteHtml(param.getText()));
                reviseAsrRequestDTO.setSubtitles(subtitleList);
                RevisedAsrResponseDTO revisedAsrResponseDTO = subtitleManager.asrRevise(reviseAsrRequestDTO);
                mediaProduceJobManager.lambdaUpdate().eq(MediaProduceJobPO::getId, job.getId())
                        .set(CollectionUtils.isNotEmpty(subtitleList), MediaProduceJobPO::getSubtitleDetail,
                                JSONUtil.toJsonStr(subtitleList)).set(MediaProduceJobPO::getModifyDt, new Date())
                        .update();
                ttsResponseDTO.setSubtitles(
                        revisedAsrResponseDTO.getRevisedAsrSubtitles().stream().map(revisedAsrSubtitleDTO -> {
                            TtsSubtitleDTO ttsSubtitleDTO = new TtsSubtitleDTO();
                            ttsSubtitleDTO.setText(trimSymbol(revisedAsrSubtitleDTO.getRevisedSubtitle()));
                            ttsSubtitleDTO.setBeginTime(Long.valueOf(revisedAsrSubtitleDTO.getTimePointStart()));
                            ttsSubtitleDTO.setEndTime(Long.valueOf(revisedAsrSubtitleDTO.getTimePointEnd()));
                            return ttsSubtitleDTO;
                        }).collect(Collectors.toList()));*/
            }

            @Override
            public void onFail(SpeechSynthesizerResponse response) {
                job.setStatus(-Const.ONE);
                job.setFailCode(response.getStatus() + "");
                String failReason = extractFailReason(response);
                job.setFailReason(failReason);
                job.setResponseDt(new Date());
                mediaProduceJobManager.updateById(job);
                log.error("阿里云语音合成失败!response:{}", JSONUtil.toJsonStr(response));
                throw BusinessServiceException.getInstance("-1", "阿里云语音合成失败！" + JSONUtil.toJsonStr(response));
            }
        };

        return listener;
    }

    private static String extractFailReason(SpeechSynthesizerResponse response) {
        AliyunTtsErrorStatusEnum errorStatusEnum = AliyunTtsErrorStatusEnum.parse(response.getStatus());

        if (Objects.isNull(errorStatusEnum)) {
            return JSONUtil.toJsonStr(response);
        }
        switch (errorStatusEnum) {
        case STATUS_41020001:
            String statusText = response.getStatusText();
            if (StringUtils.isBlank(statusText)) {
                return JSONUtil.toJsonStr(response);
            }

            String detailFailReason = "";
            if (statusText.contains(STATUS_TEXT_418)) {
                detailFailReason = "传递了不支持的发音人名称。";
            } else if (statusText.contains(STATUS_TEXT_424)) {
                detailFailReason = "传递的背景音乐或拼接录音不符合格式，请参考文档说明设置正确的背景音。";
            } else if (statusText.contains(STATUS_TEXT_413)) {
                detailFailReason = "使用的SSML格式错误。";
            } else if (statusText.contains(STATUS_TEXT_JSON_ILLEGAL)) {
                detailFailReason = "传递的JSON格式非法。";
            } else if (statusText.contains(STATUS_TEXT_TOO_LONG)) {
                detailFailReason = "传递的合成文本过长，建议使用长文本语音合成接口";
            }
            return errorStatusEnum.getReason() + "：" + detailFailReason;
        default:
            return errorStatusEnum.getReason();
        }

    }

    private OutputFormatEnum formatCodec(String audioEncode) {
        if (StringUtils.isNotBlank(audioEncode) && audioEncode.contains("wav")) {
            return OutputFormatEnum.WAV;
        }
        if (StringUtils.isNotBlank(audioEncode) && audioEncode.contains("mp3")) {
            return OutputFormatEnum.MP3;
        }
        if (StringUtils.isNotBlank(audioEncode) && audioEncode.contains("pcm")) {
            return OutputFormatEnum.PCM;
        }
        return OutputFormatEnum.MP3;
    }

    private Integer formatVolume(String vol) {
        if(StringUtils.isNotBlank(vol)) {
            Float volume = Float.parseFloat(vol) * 100;
            return volume.intValue();
        }
        return 50;
    }

    private NlsClient getClient() {
        String token = redisUtil.get(ACCESS_TOKEN_REDIS_KEY);
        Assert.isTrue(StringUtils.isNotBlank(token), "阿里云智能合成服务token为空");
        return new NlsClient(token);
    }

    private static String trimSymbol(String orignal) {
        if (StringUtils.isBlank(orignal)) {
            return orignal;
        }
        return orignal.replaceAll("【", "").replaceAll("】", "").replaceAll("。", "").replaceAll(",", "")
                .replaceAll("，", "").replaceAll("!", "").replaceAll("！", "").replaceAll("、", "").replaceAll("[?]", "")
                .replaceAll("？", "");
    }

    /**
     * 根据数据映射
     *
     * @param value
     * @param inputMin
     * @param inputMax
     * @param outputMin
     * @param outputMax
     * @return
     */
    public static Integer mapRangeDouble(Double value, Double inputMin, Double inputMax, double outputMin,
            double outputMax) {
        double inputRange = (double) (inputMax - inputMin);
        double outputRange = outputMax - outputMin;

        // 计算映射后的值
        double mappedValue = ((value - inputMin) / inputRange) * outputRange + outputMin;
        return BigDecimal.valueOf(mappedValue).setScale(1, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().intValue();
    }

    public static void main(String[] args) {
        String s = HtmlUtils.deleteHtml("aa </break>");
        System.out.println(s);
    }
}
