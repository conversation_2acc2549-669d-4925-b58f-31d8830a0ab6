package com.dl.aiservice.biz.register;

import com.dl.aiservice.biz.manager.voiceclone.VoiceCloneHandlerManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class VoiceCloneRegister implements BeanFactoryPostProcessor {

    private ConfigurableListableBeanFactory beanFactory;

    // 声纹克隆接口
    private static final Map<ServiceChannelEnum, VoiceCloneHandlerManager> VOICE_CLONE_API = new ConcurrentHashMap<>();

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    void init() {
        String[] voiceCloneApi = this.beanFactory.getBeanNamesForType(VoiceCloneHandlerManager.class);
        if (voiceCloneApi.length != 0) {
            for (String sendApi : voiceCloneApi) {
                register((VoiceCloneHandlerManager) beanFactory.getBean(sendApi));
            }
        }
    }

    private void register(VoiceCloneHandlerManager voiceCloneManager) {
        if (Objects.nonNull(voiceCloneManager)) {
            Assert.notEmpty(voiceCloneManager.getEnums(),
                    "enum() cannot be empty beanName:" + voiceCloneManager.getClass().getName());
            for (ServiceChannelEnum channelEnum : voiceCloneManager.getEnums()) {
                VOICE_CLONE_API.put(channelEnum, voiceCloneManager);
            }
        }
    }

    VoiceCloneHandlerManager get(ServiceChannelEnum typeEnum) {
        return VOICE_CLONE_API.get(typeEnum);
    }
}
