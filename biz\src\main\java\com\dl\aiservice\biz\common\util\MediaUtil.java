package com.dl.aiservice.biz.common.util;

import lombok.extern.slf4j.Slf4j;
import org.jaudiotagger.audio.AudioFile;
import org.jaudiotagger.audio.AudioFileIO;
import org.jaudiotagger.audio.AudioHeader;
import org.jaudiotagger.audio.mp3.MP3AudioHeader;
import org.jaudiotagger.audio.mp3.MP3File;

import javax.sound.sampled.UnsupportedAudioFileException;
import java.io.File;

/**
 * @describe: 媒体文件工具类
 * @author: zhousx
 * @date: 2023/5/8 11:40
 */
@Slf4j
public class MediaUtil {

    /**
     * 加了synchronized关键字，否则会有并发问题导致时长计算不准
     *
     * @param audioFile
     * @return
     */
    public static synchronized Double getAudioDuration(File audioFile) {
        double duration = 0.0;
        try {
            MP3File f = (MP3File) AudioFileIO.read(audioFile);
            MP3AudioHeader audioHeader = (MP3AudioHeader) f.getAudioHeader();
            duration = audioHeader.getPreciseTrackLength();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return duration;
    }

    public static synchronized Double getWavAudioDuration(File audioFile) {
        double duration = 0.0;
        try {
            // 使用 AudioFileIO.read() 方法读取音频文件，这里不需要强制转换为 MP3File
            AudioFile f = AudioFileIO.read(audioFile);

            // 对于 WAV 文件，您应该使用相应的 AudioHeader 子类，例如 WAVAudioHeader（如果 jaudiotagger 提供了这样的类）
            // 但是，jaudiotagger 通常使用通用的 AudioHeader 接口，因此您可能不需要显式转换
            AudioHeader audioHeader = f.getAudioHeader();

            // 获取音频文件的持续时间
            // 注意：getTrackLength() 方法可能返回的是近似值，而 getPreciseTrackLength() 通常用于 MP3 文件中的精确长度
            // 对于 WAV 文件，您可能需要使用 getTrackLength() 或查找其他方法来获取精确长度（如果可用）
            duration = audioHeader.getTrackLength(); // 或者，如果有 getPreciseTrackLength() 方法且适用于 WAV，则使用它
        } catch (Exception e) {
            // 记录错误或根据需要处理异常
            log.error(e.getMessage(), e);
        }
        return duration;
    }
}
