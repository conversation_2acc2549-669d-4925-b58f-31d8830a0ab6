package com.dl.aiservice.biz.client.deepsound.resp;

import com.dl.aiservice.biz.client.guiji.enums.GjErrCodeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class DsStandardBaseResponse<T> implements Serializable {

    private static final long serialVersionUID = 1961425384297568194L;

    private Integer code;

    private String message;

    private T data;

    private String tip;

    public boolean isSuccess() {
        return GjErrCodeEnum.ERROR_CODE_0.getErrorCode().equals(getCode());
    }
}
