package com.dl.aiservice.biz.service.digital.enums;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

public enum IvhSynthesisStatusEnum {

    /**
     * 制作状态"COMMIT"：已提交需要排队"MAKING"：制作中"SUCCESS"：制作成功"FAIL"：制作失败
     */
    COMMIT(1, "COMMIT"),
    MAKING(2, "MAKING"),
    SUCCESS(3, "SUCCESS"),
    FAIL(4, "FAIL");


    private final Integer code;
    private final String desc;

    IvhSynthesisStatusEnum(Integer errorCode, String errorDesc) {
        this.code = errorCode;
        this.desc = errorDesc;
    }

    public static IvhSynthesisStatusEnum getEnum(String desc) {
        Optional<IvhSynthesisStatusEnum> first =
                Arrays.stream(values()).filter(value -> Objects.equals(value.getDesc(), desc)).findFirst();
        return first.orElseThrow(() -> BusinessServiceException.getInstance("枚举异常"));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
