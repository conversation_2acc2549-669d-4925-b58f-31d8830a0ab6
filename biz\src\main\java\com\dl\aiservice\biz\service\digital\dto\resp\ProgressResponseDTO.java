package com.dl.aiservice.biz.service.digital.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
@ApiModel("进度查询DTO")
public class ProgressResponseDTO implements Serializable {

    private static final long serialVersionUID = 6325209005692463919L;

    /**
     * 上层业务唯一id
     */
    @ApiModelProperty(value = "通用必填：上层业务唯一id", required = true)
    private Long worksBizId;

    /**
     * -1. 编辑中 1. 排队中 2. 合成中 3：合成成功 4：合成失败 5. 归档 6. 任务取消 7. 任务失败  SynthesisStatusEnum
     */
    @ApiModelProperty(value = "-1. 编辑中 1. 排队中 2. 合成中 3：合成成功 4：合成失败 5. 归档 6. 任务取消 7. 任务失败  SynthesisStatusEnum")
    private Integer synthesisStatus;
    /**
     * 合成视频的URL
     */
    @ApiModelProperty(value = "合成视频的URL")
    private String videoUrl;
    /**
     * 字幕文件URL, ‘srtFlag’为1时才有值
     */
    @ApiModelProperty(value = "字幕文件URL, ‘srtFlag’为1时才有值")
    private String srtUrl;


    //**************************************硅基参数参数**********************************************


    /**
     * 提交的任务ID
     */
    @ApiModelProperty(value = "硅基：提交的任务ID")
    private Integer id;
    /**
     * 视频名称
     */
    @ApiModelProperty(value = "硅基：视频名称")
    private String videoName;
    /**
     * 视频格式
     */
    @ApiModelProperty(value = "硅基：视频格式")
    private String videoFormat;
    /**
     * 水平分辨率
     */
    @ApiModelProperty(value = "硅基：水平分辨率")
    private Integer horizontal;
    /**
     * 垂直分辨率
     */
    @ApiModelProperty(value = "硅基：垂直分辨率")
    private Integer vertical;
    /**
     * 时长
     */
    @ApiModelProperty(value = "硅基：时长")
    private String duration;
    /**
     * 文件尺寸
     */
    @ApiModelProperty(value = "硅基：文件尺寸")
    private String videoSize;
    /**
     * 封面图地址
     */
    @ApiModelProperty(value = "硅基：封面图地址")
    private String coverUrl;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "硅基：用户ID")
    private Integer userId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "硅基：创建时间")
    private String createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "硅基：更新时间")
    private String updateTime;


    //**************************************腾讯云参数**********************************************

    /**
     * 制作进度，-1~100，-1代表⽣成失败，100代表⽣成成功（预留字段，⽬前不具备参考意义）
     */
    @ApiModelProperty(value = "腾讯：制作进度，-1~100，-1代表⽣成失败，100代表⽣成成功（预留字段，⽬前不具备参考意义）")
    private Integer progress;
    /**
     * ⾳视频结果地址
     */
//    private String mediaUrl;
    /**
     * 当制作视频时，返回视频对应的SRT字幕地址
     */
//    private String subtitlesUrl;
    /**
     * 制作状态"COMMIT"：已提交需要排队"MAKING"：制作中"SUCCESS"：制作成功"FAIL"：制作失败
     */
//    private String status;
    /**
     * Status为"COMMIT"状态时在该任务之前排队的任务数量
     */
    @ApiModelProperty(value = "腾讯：Status为\"COMMIT\"状态时在该任务之前排队的任务数量")
    private Integer arrayCount;
    /**
     * 制作失败返回的失败原因，便于排查问题
     */
    @ApiModelProperty(value = "腾讯：制作失败返回的失败原因，便于排查问题")
    private String failMessage;

}

