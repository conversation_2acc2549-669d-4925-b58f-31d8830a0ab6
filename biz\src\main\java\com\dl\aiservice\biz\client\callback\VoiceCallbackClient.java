package com.dl.aiservice.biz.client.callback;

import com.dl.aiservice.share.voiceclone.TrainCallbackResponseDTO;
import com.dl.framework.common.model.ResultModel;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;

/**
 * @ClassName VoiceCallbackClient
 * @Description
 * <AUTHOR>
 * @Date 2023/3/16 17:43
 * @Version 1.0
 **/
@BaseRequest
public interface VoiceCallbackClient {

    @Post("{callbackUrl}")
    ResultModel callback(@Var("callbackUrl") String callbackUrl, @JSONBody TrainCallbackResponseDTO resp);
}
