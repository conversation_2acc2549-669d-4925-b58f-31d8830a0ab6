package com.dl.aiservice.biz.manager.digitalasset.bo;

import lombok.Data;

import java.util.Date;

@Data
public class DaVirtualVoiceBO {

    private Long bizId;

    /**
     * 渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    private Integer channel;

    /**
     * 外部厂商声音唯一标识
     */
    private String voiceKey;

    /**
     * 性别：1 男 ；2 女
     */
    private Integer gender;

    /**
     * 1 克隆音；2 合成音
     */
    private Integer voiceType;

    /**
     * 生效日期
     */
    private Date effectDt;

    /**
     * 失效日期
     */
    private Date expiryDt;

    private String speed;

    private String pitch;

    private String volume;

    /**
     * 情感neutral(中性)、sad(悲伤)、happy(高兴)、angry(生气)、fear(恐惧)、news(新闻)、story(故事)、radio(广播)、poetry(诗歌)、call(客服)
     */
    private String emotionCategory;

    /**
     * 控制合成音频情感程度，取值范围为[50,200],默认为100；只有EmotionCategory不为空时生效
     */
    private Long emotionIntensity;
}