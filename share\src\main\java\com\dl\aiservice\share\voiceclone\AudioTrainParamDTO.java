package com.dl.aiservice.share.voiceclone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AudioTrainParamDTO implements Serializable {
    private static final long serialVersionUID = -8450722761284975366L;

    /**
     * 录音人名称，如“韩梅梅”
     */
    @ApiModelProperty(value = "录音人名称")
    private String speaker;

    /**
     * 性别，限制值：1-男性，2-女性
     */
    @ApiModelProperty(value = "性别，限制值：1-男性，2-女性", required = true)
    @NotNull(message = "gender必填")
    private Integer gender;

    @ApiModelProperty(value = "第三方训练人模型编号")
    private String extModelCode;

    /**
     * 录音文本及其录音链接（20 句）
     */
    @NotEmpty(message = "录音文本及其录音链接不能为空")
    @ApiModelProperty(value = "录音文本及其录音链接（20 句）", required = true)
    private List<AudioTrainSourceDTO> sources;

    /**
     * 声音模型训练完成回调接口 URL，将训 练结果信息推送到指定的 URL，必须外 网可访问。
     * 在通知失败的情况下，会重 试回调三次，时间间隔为 15 秒、30 秒、 60 秒。
     * 如果未设置，可调用训练状态查询接口 进行轮询查询
     */
    @ApiModelProperty(value = "声音模型训练完成回调接口 URL")
    private String notifyUrl;

    /**
     * 提交音频的语种；目前支持 zh（中文） 和 en（英文），默认为 zh
     */
    @ApiModelProperty(value = "提交音频的语种；目前支持zh（中文）和en（英文），默认为 zh")
    private String language;

    /**
     * 训练来源，1-A端，2-D端
     */
    @ApiModelProperty(value = "训练来源，1-A端，2-D端")
    private Integer source;
}
