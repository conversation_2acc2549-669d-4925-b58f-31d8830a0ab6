package com.dl.aiservice.biz.register;

import com.dl.aiservice.biz.manager.videoproduce.VideoProduceHandleManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@AllArgsConstructor
public class VideoProduceHelper {

    private final VideoProduceRegister videoProduceRegister;

    @PostConstruct
    public void init() {
        videoProduceRegister.init();
    }

    public VideoProduceHandleManager get(ServiceChannelEnum typeEnum) {
        return videoProduceRegister.get(typeEnum);
    }
}
