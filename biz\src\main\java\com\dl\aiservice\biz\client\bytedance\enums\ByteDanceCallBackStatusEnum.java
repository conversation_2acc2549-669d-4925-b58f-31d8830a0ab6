package com.dl.aiservice.biz.client.bytedance.enums;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;

public enum ByteDanceCallBackStatusEnum {

    /**
     * 视频合成任务回调
     */
    SUCCESS(1, "SUCCESS"),

    /**
     * 视频训练任务回调
     */
    FAIL(2, "FAIL");


    private final Integer code;
    private final String desc;

    ByteDanceCallBackStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ByteDanceCallBackStatusEnum getEnum(Integer code) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getCode(), code)).findFirst().orElseThrow(() -> BusinessServiceException.getInstance("枚举异常"));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
