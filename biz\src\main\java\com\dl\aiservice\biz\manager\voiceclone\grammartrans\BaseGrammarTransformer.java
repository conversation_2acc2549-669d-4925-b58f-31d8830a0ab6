package com.dl.aiservice.biz.manager.voiceclone.grammartrans;

import com.dl.aiservice.biz.common.enums.SymbolE;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;

/**
 * 语法转换器 基类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-10 14:59
 */
public abstract class BaseGrammarTransformer implements GrammerTransformerInterface {
    private static final Logger LOGGER = LoggerFactory.getLogger(BaseGrammarTransformer.class);

    /**
     * 语法转换
     *
     * @param originalText
     * @return
     */
    @Override
    public String grammarTransform(String originalText) {
        //判断是否包含ssml脚本，若不包含，则直接返回。
        if (!judgeContainSsml(originalText)) {
            return originalText;
        }

        //防止头尾不存在xml标签导致解析失败，此处在头尾加一个speak标签
        String speakText = "<speak>" + originalText + "</speak>";
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(speakText)));

            Element root = document.getDocumentElement();

            //进行语法转换
            LOGGER.info("开始进行脚本语法转换:originalText:{},,,speakText:{}", originalText, speakText);
            String result = doTransform(root);
            LOGGER.info("脚本语法转换完成:originalText:{},,,result:{}", originalText, result);
            return result;
        } catch (Exception e) {
            LOGGER.error("脚本语法转换发生异常!,originalText:{},,,e:{}", originalText, e);
            throw BusinessServiceException.getInstance("脚本语法转换发生异常");
        }
    }

    /**
     * 进行语法转换
     *
     * @return
     */
    protected String doTransform(Element root) {
        return SymbolE.BLANK.getValue();
    }

}
