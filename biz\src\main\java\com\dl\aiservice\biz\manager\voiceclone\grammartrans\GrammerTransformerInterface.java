package com.dl.aiservice.biz.manager.voiceclone.grammartrans;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-07 09:43
 */
public interface GrammerTransformerInterface {

    /**
     * 语法转换
     *
     * @param originalText
     * @return
     */
    String grammarTransform(String originalText);

    /**
     * 判断是否包含xml标签
     *
     * @param originalText
     * @return
     */
    default boolean judgeContainSsml(String originalText) {
        if (StringUtils.isBlank(originalText)) {
            return false;
        }
        String regex = "<(\\/?[a-zA-Z][a-zA-Z0-9\\-]*)(\\s*[^<>]*?)?(\\s*\\/?)?>";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(originalText);
        return matcher.find();
    }

}
