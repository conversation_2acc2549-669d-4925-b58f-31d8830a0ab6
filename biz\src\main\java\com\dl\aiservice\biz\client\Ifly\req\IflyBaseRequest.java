package com.dl.aiservice.biz.client.Ifly.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class IflyBaseRequest<T> implements Serializable {


    private static final long serialVersionUID = -6023833530442878791L;
    @JsonProperty(value = "base")
    private IflyRequestHeader base;

    @JsonProperty(value = "param")
    private T param;

}
