package com.dl.aiservice.share.digitalasset;

import com.dl.aiservice.share.digitalman.DigitalManSceneBaseInfoDTO;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName DaVirtualManScenesBO
 * @Description
 * <AUTHOR>
 * @Date 2023/6/5 9:07
 * @Version 1.0
 **/
@Data
public class DaVirtualManScenesDTO extends DigitalManSceneBaseInfoDTO {

    private static final long serialVersionUID = 486408756907067944L;
    /**
     * 数字人场景主键ID
     */
    private Long id;

    /**
     * 数字人唯一标识
     */
    private Long bizId;

    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 数字人形象代码
     */
    private String vmCode;

    /**
     * 数字人名称
     */
    private String vmName;

    /**
     * 仿真人自有声音代码
     */
    private String vmVoiceKey;

    /**
     * 关联合成音/克隆音的声音代码
     */
    private Long voiceBizId;

    /**
     * 性别：1 男; 2 女
     */
    private Integer gender;

    /**
     * 数字人头像地址url
     */
    private String headImg;


    /**
     * 数字人手机号
     */
    private String phone;

    /**
     * 数字人邮箱地址
     */
    private String email;

    /**
     * 数字人岗位名称
     */
    private String positionName;

    /**
     * 数智⼈类型：1 2d真⼈;2 3d真⼈
     */
    private Integer vmType;

    /**
     * 生效日期
     */
    private Date effectDt;

    /**
     * 仿真人失效期
     */
    private Date expiryDt;

    /**
     * 渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    private Integer channel;

}
