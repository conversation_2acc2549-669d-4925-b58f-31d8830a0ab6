package com.dl.aiservice.biz.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.aiservice.biz.common.annotation.BaseDao;
import com.dl.aiservice.biz.dal.po.DaVirtualManPO;
import com.dl.aiservice.biz.dal.po.DaVirtualManQueryPO;
import com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualManBO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【da_virtual_man(数字资产-仿真人信息表)】的数据库操作Mapper
 * @createDate 2023-06-02 13:53:14
 * @Entity com.dl.aiservice.biz.dal.po.DaVirtualMan
 */
@BaseDao
public interface DaVirtualManMapper extends BaseMapper<DaVirtualManPO> {

    /**
     * 分页查询数据
     *
     * @param param
     * @return
     */
    List<DaVirtualManBO> pageVm(@Param("param") DaVirtualManQueryPO param);

    /**
     * 根据条件统计总数
     *
     * @param param
     * @return
     */
    Integer pageVmCount(@Param("param") DaVirtualManQueryPO param);
}




