package com.dl.aiservice.share.digitalman;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DigitalManVideoGenResultDTO implements Serializable {

    private static final long serialVersionUID = 9015644465304536352L;

    /**
     * 媒体id 雪花算法
     */
    @ApiModelProperty(value = "媒体id 雪花算法")
    private Long mediaJobId;

    /**
     * 渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    @ApiModelProperty(value = "渠道：1 硅基 2 腾讯云 3 深声科技 4 阿里云")
    private Integer channel;

    /**
     * 任务状态：1 合成中；0 合成完成；-1 合成失败
     */
    @ApiModelProperty(value = "任务状态：1 合成中；0 合成完成；-1 合成失败")
    private Integer status;

    /**
     * 合成后url
     */
    @ApiModelProperty(value = "合成后url")
    private String mediaUrl;

    @ApiModelProperty(value ="合成失败原因")
    private String failReason;

    /**
     * 时长
     */
    @ApiModelProperty(value = "视频时长")
    private Double duration;
}

