package com.dl.aiservice.biz.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class AiConfig {

    @Value("${wealth.aiservice.callbackPrefix}")
    private String callbackPrefix;

    @Value("${wealth.aiservice.callbackHost}")
    private String callbackHost;

    @Value("${digtal.ivh.train.appkey}")
    private String ivhTrainKey;

    @Value("${digtal.ivh.train.secret}")
    private String ivhTrainSecret;

    @Value("${digtal.ivh.video.appkey}")
    private String ivhVideoKey;

    @Value("${digtal.ivh.video.secret}")
    private String ivhVideoSecret;

    @Value("${digtal.ifly.appid}")
    private String iflyAppid;

    @Value("${digtal.ifly.apiSecret}")
    private String iflyApiSecret;

    @Value("${digtal.fujia.ivh.video.appkey}")
    private String fujiaIvhVideoKey;

    @Value("${digtal.fujia.ivh.video.secret}")
    private String fujiaIvhVideoSecret;

    @Value("${digtal.dlsy.ivh.video.appkey}")
    private String dlsyIvhVideoKey;

    @Value("${digtal.dlsy.ivh.video.secret}")
    private String dlsyIvhVideoSecret;

    public String getIvhTrainKey() {
        return ivhTrainKey;
    }

    public String getIvhTrainSecret() {
        return ivhTrainSecret;
    }

    public String getIvhVideoKey() {
        return ivhVideoKey;
    }

    public String getIvhVideoSecret() {
        return ivhVideoSecret;
    }

    public String getCallbackPrefix() {
        return callbackPrefix;
    }

    public String getCallbackHost() {
        return callbackHost;
    }

    public String getIflyApiSecret() {
        return iflyApiSecret;
    }

    public String getIflyAppid() {
        return iflyAppid;
    }

    public String getFujiaIvhVideoKey() {
        return fujiaIvhVideoKey;
    }

    public String getFujiaIvhVideoSecret() {
        return fujiaIvhVideoSecret;
    }

    public String getDlsyIvhVideoKey() {
        return dlsyIvhVideoKey;
    }

     public String getDlsyIvhVideoSecret() {
        return dlsyIvhVideoSecret;
    }
}
