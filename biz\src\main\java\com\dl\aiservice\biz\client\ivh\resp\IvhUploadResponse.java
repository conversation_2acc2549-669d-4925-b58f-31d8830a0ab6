package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IvhUploadResponse implements Serializable {


    private static final long serialVersionUID = -1301231693693196145L;

    /**
     * 临时密钥令牌
     */
    @JsonProperty(value = "Token")
    private String token;
    /**
     * 临时证书密钥ID
     */
    @JsonProperty(value = "TmpSecretId")
    private String tmpSecretId;
    /**
     * 临时证书密钥Key
     */
    @JsonProperty(value = "TmpSecretKey")
    private String tmpSecretKey;

}
