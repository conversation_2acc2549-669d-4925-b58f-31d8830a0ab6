package com.dl.aiservice.biz.manager.voiceclone.volcengine;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.client.volcengine.VolcEngineVoiceClient;
import com.dl.aiservice.biz.client.volcengine.config.VolcEngineVoiceConfig;
import com.dl.aiservice.biz.client.volcengine.enums.VolcEngineTtsErrorCodeEnum;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineTtsReq;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineTtsReq.App;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineTtsReq.Audio;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineTtsReq.Request;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineTtsReq.User;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineVoiceTrainAudio;
import com.dl.aiservice.biz.client.volcengine.req.VolcEngineVoiceTrainReq;
import com.dl.aiservice.biz.client.volcengine.resp.VolcEngineTtsResp;
import com.dl.aiservice.biz.client.volcengine.resp.VolcEngineTtsSubtitle;
import com.dl.aiservice.biz.client.volcengine.resp.VolcEngineTtsSubtitleWord;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ChannelUtil;
import com.dl.aiservice.biz.common.util.DownloadUtil;
import com.dl.aiservice.biz.common.util.MediaUtil;
import com.dl.aiservice.biz.dal.po.DaVirtualVoicePO;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.dal.po.TrainResultPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.cos.CosFileUploadManager;
import com.dl.aiservice.biz.manager.digitalasset.DaVirtualVoiceManager;
import com.dl.aiservice.biz.manager.subtitle.SubtitleManager;
import com.dl.aiservice.biz.manager.subtitle.consts.SubtitleConst;
import com.dl.aiservice.biz.manager.train.TrainJobManager;
import com.dl.aiservice.biz.manager.train.TrainResultManager;
import com.dl.aiservice.biz.manager.train.bo.TrainJobAddBO;
import com.dl.aiservice.biz.manager.train.bo.TrainResultBaseInfoSaveBO;
import com.dl.aiservice.biz.manager.train.enums.TrainStatusEnum;
import com.dl.aiservice.biz.manager.train.enums.TrainTypeEnum;
import com.dl.aiservice.biz.manager.voiceclone.VoiceCloneHandlerManager;
import com.dl.aiservice.biz.manager.voiceclone.enums.VoiceTypeEnum;
import com.dl.aiservice.biz.manager.voiceclone.grammartrans.impl.volcengine.VolcEngineGrammarTransformer;
import com.dl.aiservice.biz.mq.dto.VoiceTrainProgressDTO;
import com.dl.aiservice.biz.mq.enums.DelayLevelEnum;
import com.dl.aiservice.biz.mq.producer.AiVoiceTrainProducer;
import com.dl.aiservice.share.digitalman.ivh.SentencesDTO;
import com.dl.aiservice.share.digitalman.ivh.WordsDTO;
import com.dl.aiservice.share.enums.MediaProduceJobStatusEnum;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.AudioCheckResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainDetailResponseDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.aiservice.share.voiceclone.TtsSubtitleDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * @describe: VolcEngineHandlerManager
 * @author: zhousx
 * @date: 2023/5/10 10:48
 */
@Component
@Slf4j
public class VolcEngineHandlerManager implements VoiceCloneHandlerManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(VolcEngineHandlerManager.class);

    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private CosFileUploadManager cosFileUploadManager;
    @Autowired
    private MediaProduceJobManager mediaProduceJobManager;
    @Autowired
    private ChannelUtil channelUtil;
    @Resource
    private VolcEngineVoiceClient volcEngineVoiceClient;
    @Resource
    private DaVirtualVoiceManager daVirtualVoiceManager;
    @Resource
    private VolcEngineVoiceConfig volcEngineVoiceConfig;
    @Resource
    private TrainResultManager trainResultManager;
    @Resource
    private TrainJobManager trainJobManager;
    @Resource
    private AiVoiceTrainProducer aiVoiceTrainProducer;
    @Resource
    private VolcEngineGrammarTransformer volcEngineGrammarTransformer;
    @Resource
    private SubtitleManager subtitleManager;

    @Value("${dl.fileTempPath}")
    public String localPathPrefix;

    private static final String TTS_CLUSTER = "volcano_tts";

    private static final String MEGA_TTS_CLUSTER = "volcano_mega";

    private static final Integer MAX_TRAIN_TIMES = 10;

    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.VOLC_ENGINE);
    }

    @Override
    public ResultModel envCheck(String url) {
        return null;
    }

    @Override
    public ResultModel<AudioCheckResponseDTO> audioCheck(String url, String text, String language) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public ResultModel<AudioTrainResponseDTO> audioTrain(AudioTrainParamDTO request) {
        //1.校验是否可以训练
        Integer trainedNum = this.checkCanTrainAndReturnTrainedNum(request);

        //2.保存train_result
        TrainResultBaseInfoSaveBO trainResultBaseInfoSaveBO = this.buildTrainResultBaseInfoSaveBO(request, trainedNum);
        TrainResultPO trainResultPO = trainResultManager.saveTrainResultBaseInfo(trainResultBaseInfoSaveBO);

        //3.创建train_job
        TrainJobAddBO trainJobAddBO = buidTrainJobAddBO(request);
        TrainJobPO trainJobPO = trainJobManager.addJob(trainJobAddBO);

        //4.下载文件
        String audioUrl = request.getSources().get(0).getLink();
        String savePath;
        String audioFormat;
        File file = null;
        try {
            String decodeUrl = URLDecoder.decode(audioUrl, "UTF-8");
            String obejetKey = DownloadUtil.getCosKeyFromUrlString(decodeUrl);
            //要对obejctKey做下encode，不然一些字符无法识别。比如（）
            String encodeUrl = DownloadUtil.encodeCosKeyFromUrlString(decodeUrl);
            // 从路径中提取最后一个点（.）后面的部分作为文件格式
            audioFormat = obejetKey.substring(obejetKey.lastIndexOf('.') + 1);
            savePath = localPathPrefix + UUID.randomUUID() + "-" + obejetKey;
            //文件下载
            file = DownloadUtil.downloadFile(encodeUrl, savePath);
        } catch (Exception e) {
            LOGGER.error("处理文件下载出现异常! audioUrl:{}, e:{}", audioUrl, e);
            FileUtils.deleteQuietly(file);
            throw BusinessServiceException.getInstance("文件下载出现异常");
        }

        //5.使用Base64进行编码
        String base64Encoded;
        try {
            // 读取文件的字节
            byte[] fileContent = Files.readAllBytes(Paths.get(savePath));

            // 使用Base64编码字节数组
            byte[] encodedBytes = Base64.getEncoder().encode(fileContent);

            // 将Base64编码的字节数组转换为字符串，并显式使用UTF-8编码
            base64Encoded = new String(encodedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            LOGGER.error("音频文件base64编码失败！audioUrl:{},e:", audioUrl, e);
            throw BusinessServiceException.getInstance("音频文件base64编码失败");
        } finally {
            FileUtils.deleteQuietly(file);
        }

        //6.调用火山云训练接口
        VolcEngineVoiceTrainReq voiceTrainReq = new VolcEngineVoiceTrainReq();
        voiceTrainReq.setAppId(volcEngineVoiceConfig.getAppId());
        voiceTrainReq.setSpeakerId(request.getExtModelCode());
        VolcEngineVoiceTrainAudio audio = new VolcEngineVoiceTrainAudio();
        audio.setAudioBytes(base64Encoded);
        audio.setAudioFormat(audioFormat);
        voiceTrainReq.setAudios(Lists.newArrayList(audio));
        try {
            volcEngineVoiceClient.voiceTrain(trainResultPO.getBizId(), trainJobPO.getTrainJobId(), voiceTrainReq);
        } catch (BusinessServiceException e) {
            LOGGER.error("调用火山云声音训练失败。speakerId:{},,,e.message:{}", request.getExtModelCode(), e.getMessage());
            return ResultModel.error(e.getErrCode(), "火山云声音训练失败。" + e.getMessage());
        } catch (Exception e) {
            LOGGER.error("调用火山云声音训练失败。speakerId:{},,,e:{}", request.getExtModelCode(), e);
            return ResultModel.error("-1", "火山云声音训练失败。" + e.getMessage());
        }

        //7.发送mq 在消费者中查询训练进度
        VoiceTrainProgressDTO progressDTO = new VoiceTrainProgressDTO();
        progressDTO.setTrainResultBizId(trainResultPO.getBizId());
        progressDTO.setTrainJobId(trainJobPO.getTrainJobId());
        progressDTO.setChannel(ServiceChannelEnum.VOLC_ENGINE.getCode());
        progressDTO.setTrainType(TrainTypeEnum.TRAIN_VOICE.getType());
        progressDTO.setExtModelCode(trainResultPO.getExtModelCode());
        progressDTO.setCount(0);
        aiVoiceTrainProducer.sendVoiceTrainProgressMQ(progressDTO, DelayLevelEnum.FIVE_SECEND);

        AudioTrainResponseDTO responseDTO = new AudioTrainResponseDTO();
        responseDTO.setTrainJobId(String.valueOf(trainJobPO.getTrainJobId()));
        return ResultModel.success(responseDTO);
    }

    @Override
    public ResultModel<AudioTrainDetailResponseDTO> queryAudioTrain(String recordId) {
        
        return null;
    }

    @Override
    public ResultModel<TTSResponseDTO> ttsProduce(TTSProduceParamDTO param) {
        Assert.notNull(param, "入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getVoiceName()), "voiceName入参不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getText()), "音频文本不能为空");

        //声音类型，优先用入参中传入的。若没有则根据voiceName从表里查询
        Integer voiceType;
        if (Objects.nonNull(param.getVoiceType())) {
            voiceType = param.getVoiceType();
        } else {
            DaVirtualVoicePO voicePO = daVirtualVoiceManager
                    .infoByChannelVoiceKey(ServiceChannelEnum.VOLC_ENGINE.getCode(), param.getVoiceName());
            Assert.notNull(voicePO, "声音信息不存在!");
            voiceType = voicePO.getVoiceType();
        }

        VolcEngineTtsReq req = new VolcEngineTtsReq();
        String reqId = String.valueOf(hostTimeIdg.generateId().longValue());
        String codec = formatCodec(param.getAudioEncode());
        
        User user = req.getUser();
        user.setUid(reqId);
        Request request = req.getRequest();
        request.setReqid(reqId);
        request.setWith_timestamp(1);

        App app = req.getApp();
        if (VoiceTypeEnum.CLONE_VOICE.getType().equals(voiceType)) {
            app.setCluster(MEGA_TTS_CLUSTER);
            request.setSplit_sentence(1);
        } else {
            app.setCluster(TTS_CLUSTER);
        }
        app.setAppid(volcEngineVoiceConfig.getAppId());
        app.setToken(volcEngineVoiceConfig.getAccessToken());

        //ssml标签处理
        String transformedText = volcEngineGrammarTransformer.grammarTransform(param.getText());
        if (param.getText().equals(transformedText)) {
            request.setText_type("plain");
        } else {
            request.setText_type("ssml");
        }
        request.setText(transformedText);
        Audio audio = req.getAudio();
        audio.setEncoding(codec);
        audio.setVoice_type(param.getVoiceName());

        //[0.2,3]，默认为1，通常保留一位小数即可
        audio.setSpeed_ratio(Objects.nonNull(param.getSpeed()) ? Float.parseFloat(param.getSpeed()) : 1);
        //[0.1, 3]，默认为2，通常保留一位小数即可
        audio.setVolume_ratio(Objects.nonNull(param.getVolume()) ? Float.parseFloat(param.getVolume()) * 2 : 2);

        // 初始化请求数据
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setMediaJobId(Long.valueOf(reqId));
        job.setTenantCode(channelUtil.getTenantCode());
        job.setWorksBizId(param.getWorksBizId());
        job.setVideoTaskJobId(param.getVideoTaskJobId());
        job.setChannel(ServiceChannelEnum.VOLC_ENGINE.getCode());
        job.setJobType(Const.TWO);
        job.setJobContent(JsonUtils.toJSON(req));
        job.setRequestDt(new Date());
        job.setStatus(Const.ONE);
        mediaProduceJobManager.save(job);

        try {
            //调用火山云tts接口
            VolcEngineTtsResp resp = volcEngineVoiceClient.tts(req);
            if (Objects.isNull(resp)) {
                LOGGER.error("火山云tts合成失败,resp is null!req:{}", JSONUtil.toJsonStr(req));
                throw BusinessServiceException.getInstance("火山云tts合成失败,响应为空");
            }

            VolcEngineTtsErrorCodeEnum errorCodeEnum = VolcEngineTtsErrorCodeEnum.parse(resp.getCode());
            //合成失败处理
            if (!VolcEngineTtsErrorCodeEnum.CODE_3000.getCode().equals(resp.getCode())) {
                LOGGER.error("火山云tts合成失败!req:{},,,resp:{}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(resp));
                throw BusinessServiceException
                        .getInstance(String.valueOf(errorCodeEnum.getCode()), errorCodeEnum.getDesc());
            }

            job.setResponseDt(new Date());
            File audioFile = getAudioFile(reqId, resp.getData(), codec);
            String audioUrl = cosFileUploadManager.uploadFile(audioFile, null, null);
            //火山云返回的时长字段比实际音频时长短。因此需要读取时长，以音频实际时长为准。
            Double duration = MediaUtil.getAudioDuration(audioFile);
            LOGGER.info("火山云音频链接：audioUrl:{},,,实际时长:{}s,,,火山云接口返回时长:{}ms", audioUrl, duration,
                    resp.getAddition().getDuration());

            FileUtil.del(audioFile.getAbsolutePath());

            MediaProduceJobPO updateJob = new MediaProduceJobPO();
            updateJob.setId(job.getId());
            updateJob.setExtJobId(reqId);
            updateJob.setStatus(Const.ZERO);
            updateJob.setMediaUrl(audioUrl);
            updateJob.setDuration(duration);
            mediaProduceJobManager.updateById(updateJob);

            TTSResponseDTO ttsResponseDTO = new TTSResponseDTO();
            ttsResponseDTO.setSid(reqId);
            ttsResponseDTO.setMediaJobId(job.getMediaJobId());
            ttsResponseDTO.setAudioUrl(audioUrl);
            ttsResponseDTO.setDuration(duration);
            if (Const.ONE.equals(param.getNeedSubtitle())) {
                LOGGER.info("火山云接口返回的字幕信息。mediaJobId:{},,,,resp.getAddition().getFrontend():{}", job.getMediaJobId(),
                        resp.getAddition().getFrontend());
                List<SentencesDTO> ttsResultSentnecesList = buildTtsResultSentnecesList(job.getMediaJobId(),
                        resp.getAddition().getFrontend());
                List<TtsSubtitleDTO> subtitleDTOList = subtitleManager
                        .genSubtitlesBySegmentor(param.getText(), param.getMaxLength(), ttsResultSentnecesList);
                ttsResponseDTO.setSubtitles(subtitleDTOList);
            }
            return ResultModel.success(ttsResponseDTO);
        } catch (BusinessServiceException e) {
            MediaProduceJobPO updateJob = new MediaProduceJobPO();
            updateJob.setId(job.getId());
            // 任务状态：1 合成中；0 合成完成；-1 合成失败com.dl.aiservice.share.enums.MediaProduceJobStatusEnum
            updateJob.setStatus(MediaProduceJobStatusEnum.FAIL.getStatus());
            // 失败原因
            updateJob.setFailReason(e.getMessage());
            updateJob.setFailCode(e.getErrCode());
            updateJob.setResponseDt(new Date());
            mediaProduceJobManager.updateById(updateJob);
            log.error("火山云tts合成失败，errorMessage:{}", e.getMessage(), e);
            return ResultModel.error("-1", "语音合成失败:" + e.getMessage());
        } catch (Exception e) {
            MediaProduceJobPO updateJob = new MediaProduceJobPO();
            updateJob.setId(job.getId());
            // 任务状态：1 合成中；0 合成完成；-1 合成失败 com.dl.aiservice.share.enums.MediaProduceJobStatusEnum
            updateJob.setStatus(MediaProduceJobStatusEnum.FAIL.getStatus());
            // 失败原因
            updateJob.setFailReason(e.getMessage());
            updateJob.setResponseDt(new Date());
            mediaProduceJobManager.updateById(updateJob);
            log.error("火山云tts合成失败，errorMessage:{}", e.getMessage(), e);
            return ResultModel.error("-1", "语音合成失败:" + e.getMessage());
        }
    }

    private static String formatCodec(String audioEncode) {
        if (StringUtils.isNotBlank(audioEncode) && audioEncode.contains("mp3")) {
            return "mp3";
        }
        if (StringUtils.isNotBlank(audioEncode) && audioEncode.contains("wav")) {
            return "wav";
        }
        if (StringUtils.isNotBlank(audioEncode) && audioEncode.contains("pcm")) {
            return "pcm";
        }
        return "mp3";
    }

    private static File getAudioFile(String sessionId, String audioData, String format) throws Exception {
        // 解码 Base64 数据
        byte[] data = Base64.getDecoder().decode(audioData);
        File file = new File(sessionId + "." + format); // 文件路径
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data); // 将字节数组写入文件
        fos.close();
        return file;
    }

    /**
     * 校验是否可以训练并返回已训练次数
     * <p>
     * 1.校验是否有正在训练中的任务
     * 2.校验已训练成功的任务数量是否达到最大值:10次 (训练失败的任务不占用次数)
     *
     * @param request
     */
    private Integer checkCanTrainAndReturnTrainedNum(AudioTrainParamDTO request) {
        //查询训练成功和训练中的任务
        List<TrainJobPO> existTrainJobs = trainJobManager.list(Wrappers.lambdaQuery(TrainJobPO.class)
                .eq(TrainJobPO::getChannel, ServiceChannelEnum.VOLC_ENGINE.getCode())
                .eq(TrainJobPO::getJobType, TrainTypeEnum.TRAIN_VOICE.getType())
                .eq(TrainJobPO::getExtModelCode, request.getExtModelCode()).in(TrainJobPO::getStatus,
                        Lists.newArrayList(TrainStatusEnum.MAKING.getCode(), TrainStatusEnum.SUCCESS.getCode())));
        if (CollectionUtils.isEmpty(existTrainJobs)) {
            return 0;
        }
        //校验是否有正在训练中的任务
        Optional<TrainJobPO> makingOpt = existTrainJobs.stream()
                .filter(job -> TrainStatusEnum.MAKING.getCode().equals(job.getStatus())).findAny();
        if (makingOpt.isPresent()) {
            throw BusinessServiceException.getInstance("当前已有一个训练中的任务，请勿再次提交");
        }

        //校验已训练成功的任务数量是否达到最大值:10次
        if (existTrainJobs.size() >= MAX_TRAIN_TIMES) {
            throw BusinessServiceException.getInstance("已达到训练次数上限");
        }

        return existTrainJobs.size();
    }

    private TrainResultBaseInfoSaveBO buildTrainResultBaseInfoSaveBO(AudioTrainParamDTO request, Integer trainedNum) {
        TrainResultBaseInfoSaveBO trainResultBaseInfoSaveBO = new TrainResultBaseInfoSaveBO();
        trainResultBaseInfoSaveBO.setChannel(ServiceChannelEnum.VOLC_ENGINE.getCode());
        trainResultBaseInfoSaveBO.setTrainType(TrainTypeEnum.TRAIN_VOICE.getType());
        trainResultBaseInfoSaveBO.setName(request.getSpeaker());
        trainResultBaseInfoSaveBO.setExtModelCode(request.getExtModelCode());
        trainResultBaseInfoSaveBO.setStatus(TrainStatusEnum.MAKING.getCode());
        trainResultBaseInfoSaveBO.setTenantCode(channelUtil.getTenantCode());
        trainResultBaseInfoSaveBO.setTrainedNum(trainedNum + 1);
        return trainResultBaseInfoSaveBO;
    }

    private TrainJobAddBO buidTrainJobAddBO(AudioTrainParamDTO request) {
        TrainJobAddBO trainJobAddBO = new TrainJobAddBO();
        trainJobAddBO.setTenantCode(channelUtil.getTenantCode());
        trainJobAddBO.setChannel(ServiceChannelEnum.VOLC_ENGINE.getCode());
        trainJobAddBO.setJobType(TrainTypeEnum.TRAIN_VOICE.getType());
        trainJobAddBO.setGender(request.getGender());
        trainJobAddBO.setExtJobId(request.getExtModelCode());
        trainJobAddBO.setExtModelCode(request.getExtModelCode());
        trainJobAddBO.setStatus(TrainStatusEnum.MAKING.getCode());
        trainJobAddBO.setCallbackUrl(request.getNotifyUrl());
        trainJobAddBO.setTrainName(request.getSpeaker());
        trainJobAddBO.setSource(request.getSource());
        return trainJobAddBO;
    }

    public static List<SentencesDTO> buildTtsResultSentnecesList(Long mediaJobId, String frontend) {
        if (StringUtils.isBlank(frontend)) {
            return Collections.emptyList();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        LOGGER.info("mediaJobId:{},,,火山云返回的字幕:{}", mediaJobId, frontend);
        VolcEngineTtsSubtitle volcEngineTtsSubtitle = null;
        try {
            volcEngineTtsSubtitle = objectMapper.readValue(frontend, VolcEngineTtsSubtitle.class);
        } catch (Exception e) {
            LOGGER.error("解析火山云字幕发生异常! mediaJobId:{},,,frontend:{},,,e:{}", mediaJobId, frontend, e);
            return Collections.emptyList();
        }

        List<SentencesDTO> resultList = new ArrayList<>();
        SentencesDTO result = new SentencesDTO();
        resultList.add(result);
        List<WordsDTO> words = new ArrayList<>();
        StringBuilder sentence = new StringBuilder();

        StringBuilder wordSbf = new StringBuilder();
        for (int i = 0; i < volcEngineTtsSubtitle.getWords().size(); i++) {
            VolcEngineTtsSubtitleWord input = volcEngineTtsSubtitle.getWords().get(i);

            String word = input.getWord();
            //移除标点符号
            String rmPuncWord = word.replaceAll(SubtitleConst.PUNCTUATION_REGEX, "");
            //移除标点符号后，若是纯英文，且非首个，则前面拼个空格
            if (i != 0 && rmPuncWord.matches(SubtitleConst.ENGLISH_CHAR)) {
                wordSbf.append(" ").append(input.getWord());
            } else {
                wordSbf.append(input.getWord());
            }
            sentence.append(wordSbf.toString());
            WordsDTO wordsDTO = new WordsDTO();
            wordsDTO.setWord(wordSbf.toString());
            wordsDTO.setStartTime(input.getStartTime());
            wordsDTO.setEndTime(input.getEndTime());
            words.add(wordsDTO);

            //重置
            wordSbf = new StringBuilder();
        }
        /*volcEngineTtsSubtitle.getWords().stream().forEach(input -> {
            sentence.append(input.getWord());
            WordsDTO wordsDTO = new WordsDTO();
            wordsDTO.setWord(input.getWord());
            wordsDTO.setStartTime(input.getStartTime());
            wordsDTO.setEndTime(input.getEndTime());
            words.add(wordsDTO);
        });*/
        result.setSentence(sentence.toString());
        result.setWords(words);
        return resultList;
    }
}
