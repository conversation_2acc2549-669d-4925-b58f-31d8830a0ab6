package com.dl.aiservice.biz.client.deepsound.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName DsAudioTrainSource
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 18:17
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DsTtsInput implements Serializable {

    private static final long serialVersionUID = -4213730735414879102L;

    /**
     * 需要合成的文 本串 待合成的文本，需要为 UTF-8 编 码，再使用 url_safe base64 编码
     */
    @JsonProperty("text")
    private String text;

    /**
     * 文本类型 固定为 plain
     */
    @JsonProperty(value = "text-type", defaultValue = "plain")
    private String textType = "plain";

    /**
     * 文本编码 固定为 utf-8
     */
    @JsonProperty(value = "text-encode", defaultValue = "utf-8")
    private String textEncode = "utf-8";

    /**
     * 数字的读法
     * 可选以下值，默认 ordinal_first；
     * ordinal_first # 优先数值读法；
     * digits_first # 优先数字串读法；
     * all_ordinal # 完全数值发音；
     * all_digits # 完全数字串读；
     * military # 军事读法。
     */
    @JsonProperty(value = "number-read", defaultValue = "ordinal_first")
    private String numberRead = "ordinal_first";
}