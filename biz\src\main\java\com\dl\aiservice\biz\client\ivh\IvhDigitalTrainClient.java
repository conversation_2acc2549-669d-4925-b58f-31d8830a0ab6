package com.dl.aiservice.biz.client.ivh;

import com.dl.aiservice.biz.client.ivh.intercepter.IvhDigitalMethodInterceptor;
import com.dl.aiservice.biz.client.ivh.intercepter.IvhDigitalTrainInterceptor;
import com.dl.aiservice.biz.client.ivh.req.IvhBaseRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhTrainingCreateRequest;
import com.dl.aiservice.biz.client.ivh.resp.IvhBaseResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhTask;
import com.dl.aiservice.biz.client.ivh.resp.IvhTrainingCreateResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhUploadTrainResponse;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

@BaseRequest(baseURL = "https://gw.tvs.qq.com/v2/ivh/assetmanager/customservice", interceptor = IvhDigitalTrainInterceptor.class)
public interface IvhDigitalTrainClient {


    String APP_KEY = "appkey";
    String TIMESTAMP = "timestamp";
    String SIGNATURE = "signature";

    String TRAIN_PATH = "/make";

    /**
     * 训练 定制接⼝
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/make", interceptor = IvhDigitalMethodInterceptor.class)
    IvhBaseResponse<IvhTask> trainingMake(@JSONBody IvhBaseRequest<IvhTrainingCreateRequest> request, Long id);

    /**
     * 训练进度查询接⼝
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/getprogress")
    IvhBaseResponse<IvhTrainingCreateResponse> getTrainingProgress(@JSONBody IvhBaseRequest<IvhTask> request);


    /**
     * 上传素材⽂件到云端存储
     *
     * @return 返回参数
     */
    @Post(url = "/getuploadcredentials")
    IvhBaseResponse<IvhUploadTrainResponse> getuploadcredentials(@JSONBody IvhBaseRequest<IvhTask> request);
}
