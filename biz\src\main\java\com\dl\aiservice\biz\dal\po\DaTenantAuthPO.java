package com.dl.aiservice.biz.dal.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.aiservice.biz.common.po.BasePO;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-11 10:39
 */
@Data
@TableName("da_tenant_auth")
public class DaTenantAuthPO extends BasePO {
    private static final long serialVersionUID = -841525770463789304L;

    @TableId
    private Long id;

    private String tenantCode;

    private Long bizId;

    private Integer bizType;

    private Integer isDeleted;

    private Date deleteDt;
}
