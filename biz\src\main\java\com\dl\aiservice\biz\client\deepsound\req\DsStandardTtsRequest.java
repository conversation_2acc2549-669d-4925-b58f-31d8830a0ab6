package com.dl.aiservice.biz.client.deepsound.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DsStandardTtsRequest implements Serializable {

    private static final long serialVersionUID = -8450722761284975366L;

    /**
     * 必填 发音人
     */
    @JsonProperty("voice_name")
    private String voiceName;

    /**
     * 待合成文本，UTF-8编码，长度限制8192字符，支持自定义text的发音方式，包括拼音、韵律、停顿、数字四种
     */
    @JsonProperty("text")
    private String text;

    /**
     * 待合成情感文本，长度限制8192字符，支持情感标签发音方式
     * 'emotion_text': [
     * {"text": "农夫看到一条奄奄一息的蛇，", "emotion_type": "plain"},
     * {"text": "“哎呀！怎么有条死蛇！”", "emotion_type": "amazing"},
     * {"text": "他打算把蛇扔到外面去。蛇突然说：", "emotion_type": "plain"},
     * {"text": "“别把我扔出去，救救我！", "emotion_type": "fear"},
     * {"text": "我就快死了，不会咬人的。”", "emotion_type": "sad"},
     * {"text": "农夫把它放进怀里，蛇温暖后很快苏醒，咬了农夫一口，", "emotion_type": "plain"},
     * {"text": "“哈哈，我活过来了！”", "emotion_type": "happy"},
     * {"text": "农夫后悔道：", "emotion_type": "plain"}
     * ]
     */
    @JsonProperty("emotion_text")
    private List<DsStandardTtsEmotion> emotionText;

    /**
     * 是否返回时间戳或字幕，默认0（0否， 1是）
     */
    @JsonProperty("with_caption")
    private Integer withCaption;

    /**
     * 返回时间戳或字幕格式: tl普通时间戳格式 tlx音素级时间戳格式 srt字幕格式
     */
    @JsonProperty("caption_type")
    private String captionType;

    /**
     * 字幕每行最大长度，默认为12
     */
    @JsonProperty("caption_length")
    private Integer captionLength;

    /**
     * 默认MP3，可选:MP3/WAV
     */
    @JsonProperty("format")
    private String format;

    /**
     * 输出格式，默认为json( json: 返回音频url, base64_raw: 返回bas64 encode后的二进制字符串)
     */
    @JsonProperty("output")
    private String output;

    /**
     * 音频采样率，默认44100，可选:8000/16000/24000/44100
     */
    @JsonProperty("sample_rate")
    private Integer sampleRate;
    /**
     * 速度，默认1.0，可选区间:0.0-2.0
     */
    @JsonProperty("speed")
    private Double speed;
    /**
     * 音调，默认1.0，可选区间：0.0.-2.0
     */
    @JsonProperty("pitch")
    private Double pitch;


}
