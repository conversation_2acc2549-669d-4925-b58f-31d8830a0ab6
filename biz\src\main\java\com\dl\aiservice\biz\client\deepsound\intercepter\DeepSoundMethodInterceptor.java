package com.dl.aiservice.biz.client.deepsound.intercepter;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.client.deepsound.DeepSoundClient;
import com.dl.aiservice.biz.client.deepsound.enums.DsAudioCheckErrCodeEnum;
import com.dl.aiservice.biz.client.deepsound.req.DsAudioTrainRequest;
import com.dl.aiservice.biz.client.deepsound.resp.DsAudioTrainResponse;
import com.dl.aiservice.biz.client.deepsound.resp.DsTtsResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.biz.common.util.ForestLogUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.train.TrainJobManager;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestHeader;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * @ClassName DeepSoundMethodInterceptor
 * @Description 方法级别的拦截器
 * <AUTHOR>
 * @Date 2023/3/14 17:23
 * @Version 1.0
 **/
@Slf4j
public class DeepSoundMethodInterceptor implements Interceptor {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeepSoundMethodInterceptor.class);

    private static final String DUR_KEY = "dur";
    private static final String RESP_CODE = "code";

    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        Interceptor.super.onSuccess(data, request, response);
        ttsProcess(data, request, response);
        trainProcess(data, request, response);
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        Interceptor.super.onError(ex, request, response);
        trainProcess(ex, request, response);
        ttsProcess(ex, request, response);
        JSONObject responseCnt = JSONUtil.parseObj(response.getContent());
        String code = DsAudioCheckErrCodeEnum.UNKNOWN.getErrorCode().toString();
        if (Objects.nonNull(responseCnt)) {
            code = responseCnt.getStr(RESP_CODE);
        }
        throw BusinessServiceException.getInstance(code, ex.getMessage());
    }

    private void ttsProcess(Object data, ForestRequest request, ForestResponse response) {
        String url = request.getMethod().getMetaRequest().getUrl();
        if (!StringUtils.equals(DeepSoundClient.TTS_PATH, url)) {
            return;
        }
        MediaProduceJobManager manager = ApplicationContextUtils.getContext().getBean(MediaProduceJobManager.class);
        HostTimeIdg hostTimeIdg = ApplicationContextUtils.getContext().getBean(HostTimeIdg.class);
        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setRequestDt(new Date());
        job.setResponseDt(new Date());
        job.setMediaJobId(hostTimeIdg.generateId().longValue());
        job.setTenantCode(request.getHeader(DeepSoundClient.HEADER_TENANT_CODE).getValue());
        String worksBizId = request.getHeader(DeepSoundClient.HEADER_MEDIA_BIZ_ID).getValue();
        if (NumberUtils.isNumber(worksBizId)) {
            job.setWorksBizId(Long.valueOf(worksBizId));
        }
        String tJobId = request.getHeader(DeepSoundClient.HEADER_TASK_JOB_ID).getValue();
        if (NumberUtils.isNumber(tJobId)) {
            job.setVideoTaskJobId(Long.valueOf(tJobId));
        }
        job.setChannel(ServiceChannelEnum.DEEP_SOUND.getCode());
        job.setJobType(Const.TWO);
        job.setJobContent(ForestLogUtil.requestLoggingContent(request.getRequestLogMessage()));
        job.setStatus(Const.ONE);
        if (data instanceof DsTtsResponse) {
            DsTtsResponse resp = (DsTtsResponse) data;
            job.setExtJobId(resp.getSid());
            if (resp.isSuccess()) {
                job.setStatus(Const.ZERO);
                job.setMediaUrl(resp.getAudioUrl());
                // [{"end":10,"dur":1936}] end 表示这段音频对应的文本结束位置；duration 表示这段音频的时长，单位为毫秒。
                ForestHeader timelineHeader = response.getHeader(DeepSoundClient.HEADER_RSP_TIME_LINE);
                if (Objects.nonNull(timelineHeader) && StringUtils.isNotBlank(timelineHeader.getValue())) {
                    JSONArray timelineArr = JSONUtil.parseArray(timelineHeader.getValue());
                    BigDecimal dur = timelineArr.stream()
                            .map(x -> BigDecimal.valueOf(((JSONObject) x).getInt(DUR_KEY)))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    job.setDuration(dur.divide(new BigDecimal(1000)).doubleValue());
                }
            } else {
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(-Const.ONE);
                // 三方错误码
                job.setFailCode(resp.getErrCode().toString());
                // 失败原因
                job.setFailReason(response.getContent());
            }
        } else {
            job.setStatus(-Const.ONE);
            try {
                JSONObject responseCnt = JSONUtil.parseObj(response.getContent());
                job.setFailCode(responseCnt.getStr(RESP_CODE));
                job.setFailReason(response.getContent());
            } catch (Exception e) {
                //失败原因  最大截取255个字符
                job.setFailReason(StringUtils.substring(JSONUtil.toJsonStr(response), 0,
                        Math.min(StringUtils.length(JSONUtil.toJsonStr(response)), 255)));
            }
        }
        manager.save(job);
    }

    private void trainProcess(Object data, ForestRequest request, ForestResponse response) {
        if (!trainCheck(request)) {
            return;
        }
        TrainJobManager manager = ApplicationContextUtils.getContext().getBean(TrainJobManager.class);
        TrainJobPO job = new TrainJobPO();
        job.setJobType(Const.ONE);
        job.setTrainJobId(Long.valueOf(((DsAudioTrainRequest) request.getArgument(Const.ZERO)).getRecordId()));
        job.setJobContent(ForestLogUtil.requestLoggingContent(request.getRequestLogMessage()));
        if (data instanceof DsAudioTrainResponse) {
            DsAudioTrainResponse resp = (DsAudioTrainResponse) data;
            if (resp.isSuccess()) {
                job.setExtJobId(resp.getData().getBusinessId());
            } else {
                // 任务状态：1 合成中；0 合成完成；-1 合成失败
                job.setStatus(-Const.ONE);
                // 三方错误码
                job.setFailCode(resp.getCode().toString());
                // 失败原因
                job.setFailReason(response.getContent());
            }
        } else {
            job.setStatus(-Const.ONE);
            try {
                JSONObject responseCnt = JSONUtil.parseObj(response.getContent());
                job.setFailCode(responseCnt.getStr(RESP_CODE));
                job.setFailReason(response.getContent());
            } catch (Exception e) {
                //失败原因  最大截取255个字符
                job.setFailReason(StringUtils.substring(JSONUtil.toJsonStr(response), 0,
                        Math.min(StringUtils.length(JSONUtil.toJsonStr(response)), 255)));
            }
        }
        manager.lambdaUpdate().eq(TrainJobPO::getTrainJobId, job.getTrainJobId()).update(job);
    }

    private boolean trainCheck(ForestRequest request) {
        String url = request.getMethod().getMetaRequest().getUrl();
        String type = request.getMethod().getMetaRequest().getType();
        if (!StringUtils.equals(DeepSoundClient.AUDIO_TRAIN_PATH, url) || !StringUtils.equals(
                DeepSoundClient.REQUEST_TYPE, type)) {
            return Boolean.FALSE;
        }
        Object argument = request.getArgument(Const.ZERO);
        if (!(argument instanceof DsAudioTrainRequest)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
