package com.dl.aiservice.biz.config;

import com.dl.aiservice.biz.client.guiji.support.GJContextHolder;
import com.dl.aiservice.biz.client.guiji.support.GJDigitalManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GJClientConfig {

    @Bean
    public GJContextHolder weChatContextHolder(GJDigitalManager gjDigitalManager) {
        GJContextHolder.setGJManager(gjDigitalManager);
        return null;
    }
}
