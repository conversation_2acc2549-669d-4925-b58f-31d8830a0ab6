package com.dl.aiservice.biz.client.aliyun;

import com.dl.aiservice.biz.client.aliyun.interceptor.AliyunAsrInterceptor;
import com.dl.aiservice.biz.client.aliyun.resp.AliyunFlashAsrResp;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-31 11:39
 */
@BaseRequest(baseURL = "https://nls-gateway.cn-shanghai.aliyuncs.com", interceptor = AliyunAsrInterceptor.class)
public interface AliyunAsrClient {

    /**
     * 录音文件识别（极速版）——使用音频文件链接
     * https://help.aliyun.com/document_detail/187161.html?spm=a2c4g.406172.0.0.2fea4f4fUbpnkP#section-fzv-lps-0dk
     *
     * @param format       音频编码格式。支持格式：WAV、OPUS、AAC、MP3。
     * @param audioAddress 使用音频文件链接时必填，存放录音文件的地址
     * @return
     */
    @Post("/stream/v1/FlashRecognizer")
    AliyunFlashAsrResp flashRecognizer(@Query("appkey") String appKey, @Query("token") String token,
            @Query("format") String format, @Query("audio_address") String audioAddress,
            @Query("sentence_max_length") Integer sentenceMaxLength);

    /**
     * 录音文件识别（极速版）——上传音频文件
     * https://help.aliyun.com/document_detail/187161.html?spm=a2c4g.406172.0.0.2fea4f4fUbpnkP#section-fzv-lps-0dk
     *
     * @param enableInverseTextNormalization ITN（逆文本inverse text normalization）中文数字转换阿拉伯数字。
     *                                       设置为True时，中文数字将转为阿拉伯数字输出，默认值：False。
     * @param enableWordLevelResult          是否返回词级别信息。取值：true或false。默认：false（不开启）。
     * @param sentenceMaxLength              每句最多展示字数，取值范围：[4，50]。默认不启用该功能。启用后如不填写字数，则按照长句断句。
     *                                       该参数可用于字幕生成场景，控制单行字幕最大字数。
     * @param format                         音频编码格式。支持格式：WAV、OPUS、AAC、MP3。
     * @param enableTimestampAlignment       是否启用时间戳校准功能，取值：true或false，默认：false（不开启）。
     * @param body                           音频文件的字节数组
     * @return
     */
    @Post(url = "/stream/v1/FlashRecognizer", contentType = "application/octet-stream")
    AliyunFlashAsrResp flashRecognizerFile(@Query("appkey") String appKey, @Query("token") String token,
            @Query("format") String format,
            @Query("enable_inverse_text_normalization") boolean enableInverseTextNormalization,
            @Query("enable_word_level_result") boolean enableWordLevelResult,
            @Query("sentence_max_length") Integer sentenceMaxLength,
            @Query("enable_timestamp_alignment") boolean enableTimestampAlignment, @Body byte[] body);

}
