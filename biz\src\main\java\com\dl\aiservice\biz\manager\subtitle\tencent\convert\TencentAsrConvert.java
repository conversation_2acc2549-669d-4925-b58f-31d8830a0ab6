package com.dl.aiservice.biz.manager.subtitle.tencent.convert;

import com.dl.aiservice.share.subtitle.dto.AsrSubtitleDTO;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleWordsDTO;
import com.tencent.asr.model.FlashRecognitionResponse;
import com.tencent.asr.model.FlashRecognitionResponse.FlashRecognitionResult;
import com.tencent.asr.model.FlashRecognitionResponse.FlashRecognitionSentence;
import com.tencent.asr.model.FlashRecognitionResponse.FlashWordData;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-08-29 17:49
 */
public class TencentAsrConvert {

    public static List<AsrSubtitleDTO> buildAsrSubtitleDTOList(FlashRecognitionResponse resp) {
        List<FlashRecognitionResult> flashRecognitionResultList = resp.getFlashResult();
        List<AsrSubtitleDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(flashRecognitionResultList)) {
            return resultList;
        }

        //我们只有单声道，故取第0个
        FlashRecognitionResult flashRecognitionResult = flashRecognitionResultList.get(0);
        //句子列表
        List<FlashRecognitionSentence> sentenceList = flashRecognitionResult.getSentenceList();

        //找出最后一个句子
        FlashRecognitionSentence lastSentences = sentenceList.stream()
                .max(Comparator.comparing(FlashRecognitionSentence::getEndTime)).get();

        resultList = sentenceList.stream().map(input -> {
            AsrSubtitleDTO result = new AsrSubtitleDTO();
            result.setSubtitle(input.getText());
            result.setTimePointStart(input.getStartTime().intValue());
            //最后一个句子的结束时间取整个音频的时长
            if (input.getStartTime().equals(lastSentences.getStartTime())) {
                result.setTimePointEnd(resp.getAudioDuration().intValue());
            } else {
                result.setTimePointEnd(input.getEndTime().intValue());
            }

            result.setWords(buildAsrSubtitleWordsDTOList(input.getWordList(), input.getStartTime().intValue()));
            return result;
        }).collect(Collectors.toList());
        return resultList;
    }

    public static List<AsrSubtitleWordsDTO> buildAsrSubtitleWordsDTOList(List<FlashWordData> inputWordsList,
            Integer currentSentenceStartTime) {
        if (CollectionUtils.isEmpty(inputWordsList)) {
            return Collections.emptyList();
        }

        List<AsrSubtitleWordsDTO> resultWordList = new ArrayList<>();
        //去除wordsList中的标点符号
        for (FlashWordData wordData : inputWordsList) {
            //若是标点符号，则跳过
            if (judgeIsPunctuation(wordData.getWord())) {
                continue;
            }
            AsrSubtitleWordsDTO result = new AsrSubtitleWordsDTO();
            result.setWord(wordData.getWord());
            result.setTimePointStart(currentSentenceStartTime + wordData.getStartTime().intValue());
            result.setTimePointEnd(currentSentenceStartTime + wordData.getEndTime().intValue());
            resultWordList.add(result);
        }

        return resultWordList;
    }

    public static void main(String[] args) {

        System.out.println(judgeIsPunctuation(",") ? 1 : 0);
        System.out.println(judgeIsPunctuation(".") ? 1 : 0);
        System.out.println(judgeIsPunctuation("。") ? 1 : 0);
        System.out.println(judgeIsPunctuation("，") ? 1 : 0);
        System.out.println(judgeIsPunctuation("？") ? 1 : 0);
        System.out.println(judgeIsPunctuation("！") ? 1 : 0);
        System.out.println(judgeIsPunctuation("!") ? 1 : 0);
        System.out.println(judgeIsPunctuation("、") ? 1 : 0);
    }

    public static boolean judgeIsPunctuation(String str) {
        boolean isPunctuation = true;
        for (char c : str.toCharArray()) {
            if (!Character.isWhitespace(c) && !Character.isSpaceChar(c)
                    && Character.getType(c) != Character.CONNECTOR_PUNCTUATION
                    && Character.getType(c) != Character.DASH_PUNCTUATION
                    && Character.getType(c) != Character.END_PUNCTUATION
                    && Character.getType(c) != Character.FINAL_QUOTE_PUNCTUATION
                    && Character.getType(c) != Character.INITIAL_QUOTE_PUNCTUATION
                    && Character.getType(c) != Character.OTHER_PUNCTUATION
                    && Character.getType(c) != Character.START_PUNCTUATION) {
                isPunctuation = false;
                break;
            }
        }
        return isPunctuation;
    }
}
