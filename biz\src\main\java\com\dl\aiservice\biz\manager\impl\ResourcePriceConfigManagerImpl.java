package com.dl.aiservice.biz.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.aiservice.biz.dal.mapper.ResourcePriceConfigMapper;
import com.dl.aiservice.biz.dal.po.ResourcePriceConfigPO;
import com.dl.aiservice.biz.manager.ResourcePriceConfigManager;
import com.dl.aiservice.biz.manager.dto.ResourcePriceConfigDTO;
import io.jsonwebtoken.lang.Assert;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【resource_price_config(资源单价配置)】的数据库操作Service实现
 * @createDate 2023-03-16 11:54:30
 */
@Service
public class ResourcePriceConfigManagerImpl extends ServiceImpl<ResourcePriceConfigMapper, ResourcePriceConfigPO>
        implements ResourcePriceConfigManager {

    @Override
    public List<ResourcePriceConfigDTO> listPriceConfigsByTenant(String tenantCode, Integer status) {
        Assert.isTrue(StringUtils.isNotBlank(tenantCode), "租户编号不可为空。");
        List<ResourcePriceConfigPO> resourcePriceConfigPOS =
                lambdaQuery().eq(ResourcePriceConfigPO::getTenantCode, tenantCode)
                        .eq(Objects.nonNull(status), ResourcePriceConfigPO::getStatus, status)
                        .list();
        if (CollectionUtils.isEmpty(resourcePriceConfigPOS)) {
            return Collections.EMPTY_LIST;
        }
        return resourcePriceConfigPOS.stream()
                .map(po -> ResourcePriceConfigDTO.builder()
                        .resourceType(po.getResourceType())
                        .channel(po.getChannel())
                        .tenantCode(po.getTenantCode())
                        .price(po.getPrice())
                        .order(po.getSortOrder())
                        .build())
                .collect(Collectors.toList());
    }
}




