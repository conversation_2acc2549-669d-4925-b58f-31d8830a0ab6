package com.dl.aiservice.biz.client.guiji;

import com.dl.aiservice.biz.client.guiji.intercepter.DigitalCallbackTrainInterceptor;
import com.dl.aiservice.biz.client.guiji.intercepter.DigitalCallbackVideoInterceptor;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalTrainCallbackDTO;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.aiservice.share.digitalman.DigitalManCallbackDTO;
import com.dl.framework.common.model.ResultModel;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

public interface DigitalCallbackClient {

    @Post(value = "{0}", interceptor = DigitalCallbackTrainInterceptor.class)
    ResultModel trainingCallback(String myURL, @JSONBody DigitalTrainCallbackDTO trainCallbackDTO,
            String extCallbackRespBody);

    @Post(value = "{0}", interceptor = DigitalCallbackVideoInterceptor.class)
    ResultModel createCallback(String myURL, @JSONBody DigitalVideoCallbackDTO videoCallbackDTO,
            String extCallbackRespBody);

    @Post(value = "{0}", interceptor = DigitalCallbackVideoInterceptor.class)
    ResultModel createCallback(String myURL, DigitalVideoCallbackDTO videoCallbackDTO, String extCallbackRespBody,
            @JSONBody DigitalManCallbackDTO digitalManCallbackDTO);

}
