package com.dl.aiservice.biz.client.ivh.intercepter;

import com.dl.aiservice.biz.client.ivh.IvhDigitalClient;
import com.dl.aiservice.biz.client.ivh.IvhDigitalTrainClient;
import com.dl.aiservice.biz.client.ivh.enums.IvhErrCodeEnum;
import com.dl.aiservice.biz.client.ivh.resp.IvhBaseResponse;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.common.util.ApplicationContextUtils;
import com.dl.aiservice.biz.common.util.ForestLogUtil;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.dal.po.TrainJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.train.TrainJobManager;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName DigitalMethodInterceptor
 * @Description 方法级别的拦截器
 * <AUTHOR>
 * @Date 2023/3/22 17:23
 * @Version 1.0
 **/
@Slf4j
public class IvhDigitalMethodInterceptor implements Interceptor {

    @Override
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        Interceptor.super.onSuccess(data, request, response);
        videoProcess(data, request, response);
        trainProcess(data, request, response);
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        Interceptor.super.onError(ex, request, response);
        videoProcess(ex, request, response);
        trainProcess(ex, request, response);
        throw BusinessServiceException.getInstance(IvhErrCodeEnum.UNKNOWN.getErrorCode().toString(), ex.getMessage());
    }

    private void videoProcess(Object data, ForestRequest request, ForestResponse response) {
        String url = request.getMethod().getMetaRequest().getUrl();
        if (!StringUtils.equals(IvhDigitalClient.VIDEO_PATH, url)) {
            return;
        }

        MediaProduceJobManager mediaProduceJobManager =
                ApplicationContextUtils.getContext().getBean(MediaProduceJobManager.class);

        MediaProduceJobPO job = new MediaProduceJobPO();
        job.setId((Long) request.getArgument(Const.ONE));
        job.setJobContent(ForestLogUtil.requestLoggingContent(request.getRequestLogMessage()));
        if (data instanceof IvhBaseResponse) {
            IvhBaseResponse resp = (IvhBaseResponse) data;
            if (!resp.isSuccess()) {
                job.setStatus(-Const.ONE);
                // 三方错误码
                job.setFailCode(String.valueOf(resp.getHeader().getCode()));
                // 失败原因  最大截取255个字符
                job.setFailReason(StringUtils.substring(resp.getHeader().getMessage(), 0,
                        Math.min(StringUtils.length(resp.getHeader().getMessage()), 255)));
            }
            mediaProduceJobManager.updateById(job);
            IvhErrCodeEnum errCodeEnum = IvhErrCodeEnum.errorCode(resp.getHeader().getCode());
            if (errCodeEnum != IvhErrCodeEnum.ERROR_CODE_0) {
                throw BusinessServiceException.getInstance(errCodeEnum.getErrorCode().toString(),
                        errCodeEnum.getErrorDesc());
            }
        }

        if (data instanceof ForestRuntimeException) {
            ForestRuntimeException resp = (ForestRuntimeException) data;
            job.setStatus(-Const.ONE);
            // 三方错误码
            job.setFailCode(IvhErrCodeEnum.UNKNOWN.getErrorCode().toString());
            // 失败原因  最大截取255个字符
            job.setFailReason(
                    StringUtils.substring(resp.getMessage(), 0, Math.min(StringUtils.length(resp.getMessage()), 255)));
            mediaProduceJobManager.updateById(job);
        }
    }

    private void trainProcess(Object data, ForestRequest request, ForestResponse response) {
        String url = request.getMethod().getMetaRequest().getUrl();
        if (!StringUtils.equals(IvhDigitalTrainClient.TRAIN_PATH, url)) {
            return;
        }
        TrainJobManager trainJobManager = ApplicationContextUtils.getContext().getBean(TrainJobManager.class);
        TrainJobPO job = new TrainJobPO();
        job.setId((Long) request.getArgument(Const.ONE));
        job.setJobContent(ForestLogUtil.requestLoggingContent(request.getRequestLogMessage()));
        if (data instanceof IvhBaseResponse) {
            IvhBaseResponse resp = (IvhBaseResponse) data;
            if (!resp.isSuccess()) {
                job.setStatus(-Const.ONE);
                // 三方错误码
                job.setFailCode(String.valueOf(resp.getHeader().getCode()));
                // 失败原因
                job.setFailReason(response.getContent());
            }
            trainJobManager.updateById(job);
            IvhErrCodeEnum errCodeEnum = IvhErrCodeEnum.errorCode(resp.getHeader().getCode());
            if (errCodeEnum != IvhErrCodeEnum.ERROR_CODE_0) {
                throw BusinessServiceException.getInstance(errCodeEnum.getErrorCode().toString(),
                        errCodeEnum.getErrorDesc());
            }
        }

        if (data instanceof ForestRuntimeException) {
            ForestRuntimeException resp = (ForestRuntimeException) data;
            job.setStatus(-Const.ONE);
            // 三方错误码
            job.setFailCode(IvhErrCodeEnum.UNKNOWN.getErrorCode().toString());
            // 失败原因
            job.setFailReason(resp.getMessage());
            trainJobManager.updateById(job);
        }
    }
}
