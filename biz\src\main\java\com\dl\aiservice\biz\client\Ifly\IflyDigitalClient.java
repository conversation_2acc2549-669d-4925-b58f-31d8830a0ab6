package com.dl.aiservice.biz.client.Ifly;

import com.dl.aiservice.biz.client.Ifly.intercepter.IflyDigitalInterceptor;
import com.dl.aiservice.biz.client.Ifly.req.IflyBaseRequest;
import com.dl.aiservice.biz.client.Ifly.req.IflyTtsCreateRequest;
import com.dl.aiservice.biz.client.Ifly.req.IflyVideoCreateRequest;
import com.dl.aiservice.biz.client.Ifly.req.IflyVideoQueryRequest;
import com.dl.aiservice.biz.client.Ifly.resp.IflyBaseResponse;
import com.dl.aiservice.biz.client.Ifly.resp.IflyTtsCreateResponse;
import com.dl.aiservice.biz.client.Ifly.resp.IflyVideoCreateResponse;
import com.dl.aiservice.biz.client.Ifly.resp.IflyVideoQueryResponse;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Post;

@BaseRequest(baseURL = "https://newserver.xfyousheng.com", interceptor = IflyDigitalInterceptor.class)
public interface IflyDigitalClient {


    /**
     * 文本试听接口
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/api/v1/tts/audio")
    IflyBaseResponse<IflyTtsCreateResponse> ttsAudio(IflyBaseRequest<IflyTtsCreateRequest> request);


    /**
     * 文本试听接口
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/api/v1/vir/add")
    IflyBaseResponse<IflyVideoCreateResponse> videoCreate(IflyBaseRequest<IflyVideoCreateRequest> request);


    /**
     * 文本试听接口
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/api/v1/vir/get")
    IflyBaseResponse<IflyVideoQueryResponse> videoQuery(IflyBaseRequest<IflyVideoQueryRequest> request);


}
