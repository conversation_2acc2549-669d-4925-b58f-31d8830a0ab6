package com.dl.aiservice.biz.manager.train.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-28 16:50
 */
public enum TrainTypeEnum {

    //训练类型：0 数字人训练；1 声音训练
    TRAIN_DIGITAL_MAN(0, "数字人训练"),
    TRAIN_VOICE(1, "声音训练");

    private Integer type;

    private String desc;

    TrainTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
