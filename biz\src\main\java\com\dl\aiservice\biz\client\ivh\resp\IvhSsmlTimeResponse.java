package com.dl.aiservice.biz.client.ivh.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/7 17:02
 */
@Data
@NoArgsConstructor
public class IvhSsmlTimeResponse {

    /**
     * 返回会将ssml拆分成多个句⼦再给出每个句⼦中每个字的时间戳
     */
    @JsonProperty(value = "Sentences")
    public List<IvhSentences> sentences;

}