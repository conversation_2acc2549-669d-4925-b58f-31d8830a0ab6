package com.dl.aiservice.biz.client.ivh;

import com.dl.aiservice.biz.client.ivh.intercepter.IvhDigitalInterceptor;
import com.dl.aiservice.biz.client.ivh.intercepter.IvhDigitalMethodInterceptor;
import com.dl.aiservice.biz.client.ivh.req.IvhBaseRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetAnchorRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetResourceRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetSsmlTimeRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhGetTimbreRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhListenTtsRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhVideoMakeAdvancedRequest;
import com.dl.aiservice.biz.client.ivh.req.IvhVideoMakeRequest;
import com.dl.aiservice.biz.client.ivh.resp.IvhActionResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhBaseResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhGetAnchorImageResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhGetAnchorResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhProgressResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhSsmlTimeResponse;
import com.dl.aiservice.biz.client.ivh.resp.IvhTask;
import com.dl.aiservice.biz.client.ivh.resp.IvhTimbreResponse;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

@BaseRequest(baseURL = "https://gw.tvs.qq.com/v2/ivh/videomaker/broadcastservice",
        interceptor = IvhDigitalInterceptor.class)
public interface IvhDigitalClient {

    String APP_KEY = "appkey";
    String TIMESTAMP = "timestamp";
    String SIGNATURE = "signature";

    String VIDEO_PATH = "/videomakeadvanced";

    String TTS_PATH = "/tts";

    /**
     * 4.1 tts试听接⼝
     * 通过Appkey和Accesstoken查询客户权限下拥有的前三个****数智⼈对应的形象、服装、姿态、分辨率以及动作。
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/tts")
    IvhBaseResponse<IvhTask> listenTts(@JSONBody IvhBaseRequest<IvhListenTtsRequest> request);


    /**
     * 4.2 视频制作接⼝-基础版
     * 通使⽤ssml⽂本和数智⼈进⾏视频制作，通过4.4<⾳视频制作进度查询接⼝>接⼝最终返回成品视频和字幕⽂件。不⽀持
     * 定义主播位置等未剪辑⾼级参数，如需使⽤需要切换到4.3 <进阶版>。
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/videomake")
    IvhBaseResponse<IvhTask> videoMake(@JSONBody IvhBaseRequest<IvhVideoMakeRequest> request);


    /**
     * 4.3 视频制作接⼝-进阶版
     * 使⽤ssml⽂本和数智⼈进⾏视频制作，通过4.4<⾳视频制作进度查询接⼝>接⼝最终返回成品视频和字幕⽂件。在原有
     * 接⼝基础上、进阶版接⼝新增了部分资源参数，扩展了微剪辑能⼒，⽀持能⼒如下表：
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/videomakeadvanced", interceptor = IvhDigitalMethodInterceptor.class)
    IvhBaseResponse<IvhTask> videoMakeAdvanced(@JSONBody IvhBaseRequest<IvhVideoMakeAdvancedRequest> request, Long id);

    /**
     * 4.4 ⾳视频制作进度查询接⼝
     * 通过TaskId来查询任务的制作进度和结果，当返回值⾥的 progress 字段值为100时，即可通过 MediaUrl 获取最终⾳视
     * 频的下载地址，如果是视频制作也会返回SubtitlesUrl 字段获取最终SRT字幕地址。未上传⾃定义存储url的⾳视频资源
     * 只保留7天。
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/getprogress")
    IvhBaseResponse<IvhProgressResponse> getProgress(@JSONBody IvhBaseRequest<IvhTask> request);


    /**
     * 4.6 客户资源查询主播接⼝
     * 通过Appkey和Accesstoken查询客户权限下拥有的所有主播，以及主播头像
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/getanchor")
    IvhBaseResponse<IvhGetAnchorResponse> getAnchor(@JSONBody IvhBaseRequest<IvhGetAnchorRequest> request);

    /**
     * 4.7 查询某个主播下所有的形象
     * 通过Appkey和Accesstoken查询客户权限下拥有的所有主播，以及主播头像
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/getresourcebyanchor")
    IvhBaseResponse<IvhGetAnchorImageResponse> getResourceByAnchor(@JSONBody IvhBaseRequest<IvhGetResourceRequest> request);

    /**
     * 4.8 查询VirtualmanKey支持的⾳⾊
     * 根据VirtualmanKey查询该VirtualmanKey⽀持的⾳⾊。
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/gettimbre")
    IvhBaseResponse<IvhTimbreResponse> getTimbre(@JSONBody IvhBaseRequest<IvhGetTimbreRequest> request);

    /**
     * 4.9 查询VirtualmanKey支持的动作
     * 根据VirtualmanKey查询该VirtualmanKey支持的动作。
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/getaction")
    IvhBaseResponse<IvhActionResponse> getActions(@JSONBody IvhBaseRequest<IvhGetTimbreRequest> request);

    /**
     * 4.10 获取⽂本时间戳信息
     * 不进⾏tts试听或视频制作，仅获取ssml每个字的时间戳信息。
     *
     * @param request 请求参数
     * @return 返回参数
     */
    @Post(url = "/ssmltimestamp")
    IvhBaseResponse<IvhSsmlTimeResponse> getSsmlTime(@JSONBody IvhBaseRequest<IvhGetSsmlTimeRequest> request);

}
