package com.dl.aiservice.biz.service.digital.enums;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;

public enum DigitalComposeEnum {

    /**
     * 音频
     */
    VIDEO(0, "音频"),

    /**
     * 文本
     */
    TEXT(1, "文本");


    private final Integer code;
    private final String desc;

    DigitalComposeEnum(Integer errorCode, String errorDesc) {
        this.code = errorCode;
        this.desc = errorDesc;
    }

    public static DigitalComposeEnum getEnum(String desc) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getDesc(), desc)).findFirst().orElseThrow(() -> BusinessServiceException.getInstance("枚举异常"));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
