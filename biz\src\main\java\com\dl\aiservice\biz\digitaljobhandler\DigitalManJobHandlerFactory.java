package com.dl.aiservice.biz.digitaljobhandler;

import com.dl.aiservice.share.enums.ServiceChannelEnum;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-11 17:00
 */
@Component
public class DigitalManJobHandlerFactory implements ApplicationContextAware {

    @Resource
    private NoConcurrencyLimitDigitalManJobHandler noConcurrencyLimitDigitalManJobHandler;

    private static Map<ServiceChannelEnum, AbstractDigitalManJobHandler> digitalManJobHandlerMap = new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, AbstractDigitalManJobHandler> dmJobHandlerMap = applicationContext
                .getBeansOfType(AbstractDigitalManJobHandler.class);
        if (MapUtils.isEmpty(dmJobHandlerMap)) {
            return;
        }
        dmJobHandlerMap.values().stream().forEach(handler -> {
            if (Objects.nonNull(handler.supportChannel())) {
                digitalManJobHandlerMap.put(handler.supportChannel(), handler);
            }
        });
    }

    public AbstractDigitalManJobHandler getJobHandler(ServiceChannelEnum channelEnum) {
        if (digitalManJobHandlerMap.containsKey(channelEnum)) {
            return digitalManJobHandlerMap.get(channelEnum);
        }
        //返回默认无并发限制的jobHandler
        return noConcurrencyLimitDigitalManJobHandler;
    }

}
