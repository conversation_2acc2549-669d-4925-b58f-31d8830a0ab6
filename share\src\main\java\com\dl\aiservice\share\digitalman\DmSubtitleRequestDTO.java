package com.dl.aiservice.share.digitalman;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @describe: DmSubtitleRequestDTO
 * @author: zhousx
 * @date: 2023/6/17 16:33
 */
@Data
public class DmSubtitleRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 上层业务唯一id
     */
    @ApiModelProperty(value = "通用必填：上层业务唯一id", required = true)
    @NotNull(message = "上层业务唯一id不能为空")
    private Long worksBizId;

    /**
     * 合成的文本
     */
    @ApiModelProperty(value = "文本")
    private String text;

    /**
     * 必填 场景ID，从模特列表接口获取
     */
    @ApiModelProperty(value = "硅基必填： 场景ID，从模特列表接口获取")
    private String sceneId;

    /**
     * 发音人id 合成文本时必填
     * 腾讯云数字人声音 不建议使用该字段
     */
    @ApiModelProperty(value = "硅基：发音人id type为1 必填")
    @Deprecated
    private String speakerId;

    @ApiModelProperty(value = "字幕分段最大长度")
    private Integer maxLength;

    /**
     * 必填 语速（1.0为正常语速，范围[0.5-1.5]，值为0.5时播报语速最慢，值为1.5时播报语速最快，DriverType为⾳频驱动类型时，语速控制不⽣效）
     */
    @ApiModelProperty(value = "腾讯云必填： 语速")
    private Double speed = 1.0;
}
