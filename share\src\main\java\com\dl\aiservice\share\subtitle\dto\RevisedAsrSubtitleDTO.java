package com.dl.aiservice.share.subtitle.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @describe: RevisedAsrSubtitleDTO
 * @author: zhousx
 * @date: 2023/3/25 20:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RevisedAsrSubtitleDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一id
     * 与调用方传入的id一致
     */
    private Integer uniqId;

    private Integer timePointStart;

    private Integer timePointEnd;

    private String revisedSubtitle;
}
