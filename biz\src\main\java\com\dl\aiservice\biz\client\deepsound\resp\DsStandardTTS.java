package com.dl.aiservice.biz.client.deepsound.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DsStandardTTS implements Serializable {

    private static final long serialVersionUID = -6442816271939461816L;

    /**
     * 会话ID
     */
    @JsonProperty("sid")
    private String sid;

    /**
     * 音频url
     */
    @JsonProperty("url")
    private String url;

    /**
     * 时间戳, with_caption时返回
     */
    @JsonProperty("timeline")
    private String timeline;

    /**
     * 返回二进制base64数据，当output为base64_raw时返回
     */
    @JsonProperty("data")
    private String data;

    /**
     * 时长(单位：秒，如11.32)
     */
    @TableField(value = "duration")
    private Double duration;

}
