package com.dl.aiservice.biz.service.digital.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: xuebin
 * @description
 * @Date: 2023/3/2 10:05
 */
@NoArgsConstructor
@Data
public class VideoStyleDTO implements Serializable {

    private static final long serialVersionUID = 3468839436972034628L;
    /**
     * 水平方向位置
     */
    @ApiModelProperty(value = "硅基非必填：水平方向位置")
    private String x;
    /**
     * 垂直方向位置
     */
    @ApiModelProperty(value = "硅基非必填：水平方向位置")
    private String y;
    /**
     * 控制宽度
     */
    @ApiModelProperty(value = "硅基非必填：控制宽度")
    private String width;
    /**
     * 控制高度
     */
    @ApiModelProperty(value = "硅基非必填：控制高度")
    private String height;

}