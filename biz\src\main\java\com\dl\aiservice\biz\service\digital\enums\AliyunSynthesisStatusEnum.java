package com.dl.aiservice.biz.service.digital.enums;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

public enum AliyunSynthesisStatusEnum {

    /**
     * Finished：执行成功。
     * Failed：执行失败。
     * Executing：执行中。
     * Created：任务提交成功
     */
    Finished(3, "Finished"),
    Failed(4, "Failed"),
    Executing(2, "Executing"),
    Created(1, "Created");


    private final Integer code;
    private final String desc;

    AliyunSynthesisStatusEnum(Integer errorCode, String errorDesc) {
        this.code = errorCode;
        this.desc = errorDesc;
    }

    public static AliyunSynthesisStatusEnum getEnum(String desc) {
        Optional<AliyunSynthesisStatusEnum> first =
                Arrays.stream(values()).filter(value -> Objects.equals(value.getDesc(), desc)).findFirst();
        return first.orElseThrow(() -> BusinessServiceException.getInstance("枚举异常"));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
