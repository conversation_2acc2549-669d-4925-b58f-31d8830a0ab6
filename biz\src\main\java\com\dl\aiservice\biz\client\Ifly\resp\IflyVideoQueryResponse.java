package com.dl.aiservice.biz.client.Ifly.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IflyVideoQueryResponse {

    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 任务状态 -3：内容机器审核不通过0失败，1成功，2进行中
     */
    private Integer status;
    /**
     * 任务结果地址
     */
    private String url;
    /**
     * 生成的视频时长
     */
    private String duration;
    /**
     * 文件有效期(秒)  -1代表永久有效
     */
    private Integer validate;

}