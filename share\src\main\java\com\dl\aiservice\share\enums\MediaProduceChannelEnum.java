package com.dl.aiservice.share.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * 合成商渠道枚举类，属于ServiceChannelEnum的子集
 */
public enum MediaProduceChannelEnum {

    /**
     * 新华智云
     */
    XHZY(0, "XHZY"),
    /**
     * 阿里云
     */
    ALIYUN(4, "ALIYUN");

    private Integer code;
    private String desc;

    MediaProduceChannelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MediaProduceChannelEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> Objects.equals(code, e.getCode())).findAny().orElse(null);
    }
}
