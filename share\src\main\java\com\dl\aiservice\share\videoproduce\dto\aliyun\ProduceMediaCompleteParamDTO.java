package com.dl.aiservice.share.videoproduce.dto.aliyun;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * @describe: ProduceMediaCompleteParam
 * @author: zhousx
 * @date: 2023/2/9 17:07
 */
@Data
public class ProduceMediaCompleteParamDTO {
    @JsonProperty("EventType")
    private String eventType;

    @JsonProperty("UserId")
    private Long userId;

    @JsonProperty("EventTime")
    private Date eventTime;

    @JsonProperty("MessageBody")
    private MessageBody messageBody;

    @Data
    public class MessageBody {
        @JsonProperty("Status")
        private String status;

        @JsonProperty("MediaURL")
        private String mediaURL;

        @JsonProperty("MediaId")
        private String mediaId;

        @JsonProperty("ProjectId")
        private String projectId;

        @JsonProperty("ErrorCode")
        private String errorCode;

        @JsonProperty("ErrorMessage")
        private String errorMessage;

        @JsonProperty("JobId")
        private String jobId;
    }
}
