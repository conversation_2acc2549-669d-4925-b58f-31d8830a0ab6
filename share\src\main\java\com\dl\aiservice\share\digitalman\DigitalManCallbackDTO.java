package com.dl.aiservice.share.digitalman;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DigitalManCallbackDTO implements Serializable {

    private static final long serialVersionUID = 3366944451648377019L;

    /**
     * 租户代码
     */
    @ApiModelProperty(value = "租户代码")
    private String tenantCode;

    /**
     * 上层业务作品唯一标识
     */
    @ApiModelProperty(value = "上层业务作品唯一标识")
    private Long worksBizId;

    @ApiModelProperty(value = "内部快视频合成任务唯一标识")
    private Long videoTaskJobId;

    @ApiModelProperty(value = "视频合成结果列表")
    List<DigitalManVideoGenResultDTO> videoList;
}

