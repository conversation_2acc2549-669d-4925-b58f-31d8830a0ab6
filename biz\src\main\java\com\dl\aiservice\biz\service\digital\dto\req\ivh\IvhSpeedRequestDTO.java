package com.dl.aiservice.biz.service.digital.dto.req.ivh;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class IvhSpeedRequestDTO implements Serializable {


    private static final long serialVersionUID = -1301231693693196145L;

    /**
     * ⾳⾊key，默认使⽤形象⾃有⾳⾊
     */
    private String timbreKey;

}
