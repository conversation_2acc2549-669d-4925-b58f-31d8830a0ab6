package com.dl.aiservice.biz.client.ivh.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class IvhGetResourceRequest implements Serializable {


    private static final long serialVersionUID = -1301231693693196145L;
    /**
     * 为false时查询当前appkey对应资源，为true时查询当前⽤户所有资源
     */
    @JsonProperty(value = "GetAllResource")
    private Boolean getAllResource;
    /**
     * 主播code
     */
    @JsonProperty(value = "AnchorCode")
    private String anchorCode;

}
