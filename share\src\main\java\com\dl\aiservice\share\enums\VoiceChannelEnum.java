package com.dl.aiservice.share.enums;

import java.util.Objects;

/**
 * 声音厂商枚举 属于ServiceChannelEnum的子集
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-06-05 15:51
 */
public enum VoiceChannelEnum {
    /**
     * 新华智云
     */
    XHZY(0, "XHZY", "新华智云"),
    /**
     * 硅基智能
     */
    GUI_JI(1, "GUI_JI", "硅基智能"),
    /**
     * 腾讯云数智人
     */
    IVH(2, "IVH", "腾讯云-克隆音"),
    /**
     * 深声科技
     * 20句话线上训练的克隆音
     */
    DEEP_SOUND(3, "DS", "深声科技(线上训练)"),
    /**
     * 阿里云
     */
    ALIYUN(4, "ALIYUN", "阿里云合成音"),
    /**
     * 腾讯云TTS
     */
    TENCENT_CLOUD(5, "TENCENT_CLOUD", "腾讯云合成音"),
    /**
     * 火山引擎/字节跳动
     */
    VOLC_ENGINE(6, "VOLC_ENGINE", "火山引擎"),

    /**
     * 深声科技 线下定制版
     * 花1500块线下定制的克隆音
     */
    DEEP_SOUND_STANDARD(7, "DS_STANDARD", "深声科技(线下定制)"),

    /**
     * 科大讯飞
     */
    IFLY_TEK(8, "IFLY_TEK", "科大讯飞"),

    /**
     * 阿里云数字人，克隆音
     */
    ALIYUN_DIGIITAL(9, "ALIYUN_DIGIITAL", "阿里云克隆音"),

    /**
     * HeyGen数字人
     */
    HEYGEN(10, "HeyGen", "诗云科技"),

    /**
     * 华为云
     */
    HUAWEI(12, "HUAWEI", "华为云"),

    /**
     * 孚嘉科技的腾讯云数智人
     * 孚嘉是一个客户，他们有自己的腾讯云数智人账号
     */
    FUJIA_IVH(14, "FUJIA_IVH", "孚嘉科技的腾讯云数智人克隆音"),

    /**
     * 定力数影的腾讯云数智人
     */
    DLSY_IVH(16, "DLSY_IVH", "定力数影的腾讯云数智人"),

    /**
     * 定力数影的腾讯云数智人
     */
    MICROSOFT(17, "MICROSOFT", "微软TTS");

    private Integer code;
    private String desc;
    private String name;

    VoiceChannelEnum(Integer code, String desc, String name) {
        this.code = code;
        this.desc = desc;
        this.name = name;
    }

    public static VoiceChannelEnum parse(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (VoiceChannelEnum channelEnum : VoiceChannelEnum.values()) {
            if (code.equals(channelEnum.code)) {
                return channelEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getName() {
        return name;
    }
}
