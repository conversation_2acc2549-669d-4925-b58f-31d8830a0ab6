package com.dl.aiservice.share.digitalman;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-21 09:30
 */
@Data
public class DigitalManSceneBaseInfoDTO implements Serializable {
    private static final long serialVersionUID = -882433812624941069L;

    /**
     * 场景id
     */
    private String sceneId;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 服装信息：0 个人服饰 1 黑衣服 2 蓝礼服
     */
    private Integer cloth;

    /**
     * 姿态信息：1 坐姿; 2 半身站姿; 3 全身站姿
     */
    private Integer pose;

    /**
     * 分辨率：1 1080x1920; 2 1920x1080
     */
    private Integer resolution;

    /**
     * 场景封面地址
     */
    private String coverUrl;

    /**
     * 是否启用 0：否，1：是
     */
    private Integer isEnabled;

    /**
     * 场景样例视频地址
     */
    private String exampleUrl;

    /**
     * 场景样例文本
     */
    private String exampleText;

    /**
     * 场景样例时长
     */
    private Integer exampleDuration;
}
