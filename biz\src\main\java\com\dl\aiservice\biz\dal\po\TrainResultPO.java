package com.dl.aiservice.biz.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.aiservice.biz.common.po.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-28 15:44
 */
@Data
@TableName("train_result")
public class TrainResultPO extends BasePO implements Serializable {
    private static final long serialVersionUID = -3093019182650673386L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 训练名
     */
    private String name;

    /**
     * 厂商训练模型编号
     */
    private String extModelCode;

    /**
     * 厂商，3-深声科技（线上训练），6-火山引擎
     */
    private Integer channel;

    /**
     * 所属用户
     */
    private Long userId;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 训练类型：0 数字人训练；1 声音训练
     *
     * @See:com.dl.aiservice.biz.manager.train.enums.TrainTypeEnum
     */
    private Integer trainType;

    /**
     * 试听链接
     */
    private String sampleLink;

    /**
     * 训练状态：1 训练中；0 训练完成；-1 训练失败
     */
    private Integer status;

    /**
     * 已训练次数。训练成功+训练中
     */
    @TableField("trained_num")
    private Integer trainedNum;

    /**
     * 最新的失败原因
     */
    @TableField("latest_fail_reason")
    private String latestFailReason;

    /**
     * 是否删除，0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 修改人
     */
    private Long modifyBy;

}
