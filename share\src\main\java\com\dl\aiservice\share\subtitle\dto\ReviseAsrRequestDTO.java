package com.dl.aiservice.share.subtitle.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @describe: ReviseAsrRequestDTO
 * @author: zhousx
 * @date: 2023/3/25 20:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReviseAsrRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank
    private String originalScript;

    @NotEmpty
    private List<AsrSubtitleDTO> subtitles;
}
