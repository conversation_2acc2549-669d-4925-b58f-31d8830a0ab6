<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.aiservice.biz.dal.mapper.DaVirtualVoiceMapper">

    <resultMap id="BaseResultMap" type="com.dl.aiservice.biz.dal.po.DaVirtualVoicePO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bizId" column="biz_id" jdbcType="BIGINT"/>
        <result property="channel" column="channel" jdbcType="TINYINT"/>
        <result property="voiceKey" column="voice_key" jdbcType="VARCHAR"/>
        <result property="voiceName" column="voice_name" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="TINYINT"/>
        <result property="voiceDesc" column="voice_desc" jdbcType="VARCHAR"/>
        <result property="voiceType" column="voice_type" jdbcType="TINYINT"/>
        <result property="voiceCategory" column="voice_category" jdbcType="VARCHAR"/>
        <result property="isEnabled" column="is_enabled" jdbcType="TINYINT"/>
        <result property="isDeleted" column="is_deleted" jdbcType="TINYINT"/>
        <result property="sampleLink" column="sample_link" jdbcType="VARCHAR"/>
        <result property="effectDt" column="effect_dt" jdbcType="TIMESTAMP"/>
        <result property="expiryDt" column="expiry_dt" jdbcType="TIMESTAMP"/>
        <result property="createDt" column="create_dt" jdbcType="TIMESTAMP"/>
        <result property="modifyDt" column="modify_dt" jdbcType="TIMESTAMP"/>
        <result property="maxVoiceLink" column="max_voice_link" jdbcType="VARCHAR"/>
        <result property="volume" column="volume" jdbcType="VARCHAR"/>
        <result property="speed" column="speed" jdbcType="VARCHAR"/>
        <result property="headImg" column="head_img" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Column_List">
        biz_id,channel,voice_key,voice_name,gender,voice_desc,voice_type,voice_category,is_enabled,sample_link,
        effect_dt,expiry_dt,create_dt,modify_dt,max_voice_link,volume,speed,head_img,duration,voice_links
    </sql>

    <sql id="page_query">
        is_deleted = 0
        <if test="param.channel != null">
            AND channel = #{param.channel}
        </if>
        <if test="param.voiceName != null and param.voiceName != ''">
            AND voice_name like concat('%', #{param.voiceName}, '%')
        </if>
        <if test="param.isEnabled != null">
            AND is_enabled = #{param.isEnabled}
        </if>
    </sql>

    <select id="pageVoice" resultMap="BaseResultMap"
            parameterType="com.dl.aiservice.biz.manager.digitalasset.bo.DaVirtualVoicePageBO">
        SELECT
        <include refid="Column_List"/>
        FROM
        da_virtual_voice
        WHERE
        <include refid="page_query"/>
        ORDER BY id DESC
    </select>

    <select id="listByChannelAndVoiceKeyList" resultMap="BaseResultMap">
        SELECT
        <include refid="Column_List"/>
        FROM
        da_virtual_voice
        WHERE
        is_deleted = 0
        <if test="list != null and list.size() > 0">
            and
            (channel,voice_key) in
            (
            <foreach collection="list" item="item" index="index" separator=",">
                (#{item.channel},#{item.voiceKey})
            </foreach>
            )
        </if>
    </select>
</mapper>
